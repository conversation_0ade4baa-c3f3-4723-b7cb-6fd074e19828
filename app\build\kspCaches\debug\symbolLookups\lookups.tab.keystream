  
ValueAnimator android.animation  Activity android.app  Dialog android.app  Notification android.app  Service android.app  ActivityAnimationBinding android.app.Activity  ActivityChargingOverlayBinding android.app.Activity  ActivityEnterPasswordBinding android.app.Activity  ActivityMainBinding android.app.Activity  ActivityResultLauncher android.app.Activity  ActivitySplashBinding android.app.Activity  ActivityStartingBinding android.app.Activity  AnimationViewModel android.app.Activity  
AppRepository android.app.Activity  AppViewModel android.app.Activity  ApplovinBannerAdManager android.app.Activity  ApplovinInterstitialAdManager android.app.Activity  ApplovinRewardedAdManager android.app.Activity  BATTERY_SERVICE android.app.Activity  BackgroundPermissionDialog android.app.Activity  BatteryViewModel android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  Class android.app.Activity  CoreBatteryStatsProvider android.app.Activity  DischargeSessionRepository android.app.Activity  DynamicNavigationManager android.app.Activity  #EnhancedDischargeTimerServiceHelper android.app.Activity  	ExoPlayer android.app.Activity  FirebaseRemoteConfigHelper android.app.Activity  Handler android.app.Activity  Inject android.app.Activity  Int android.app.Activity  Intent android.app.Activity  Long android.app.Activity  MediaPlayer android.app.Activity  OptIn android.app.Activity  Runnable android.app.Activity  StatsChargeRepository android.app.Activity  String android.app.Activity  'UnifiedBatteryNotificationServiceHelper android.app.Activity  UnstableApi android.app.Activity  UsageStatsPermissionManager android.app.Activity  getValue android.app.Activity  lazy android.app.Activity  provideDelegate android.app.Activity  Activity android.app.Application  
AppRepository android.app.Application  Bundle android.app.Application  CoreBatteryServiceHelper android.app.Application  Inject android.app.Application  Int android.app.Application  	Lifecycle android.app.Application  Long android.app.Application  OnLifecycleEvent android.app.Application  PreferencesHelper android.app.Application  com android.app.Application  Activity android.app.Dialog  ActivityContext android.app.Dialog  ActivityResultLauncher android.app.Dialog  AppUsageStatsRepository android.app.Dialog  AppViewModel android.app.Dialog  BatteryViewModel android.app.Dialog  Boolean android.app.Dialog  Button android.app.Dialog  Context android.app.Dialog  DialogSelectBatteryAlarmBinding android.app.Dialog  "DialogSelectBatteryAlarmLowBinding android.app.Dialog  	ImageView android.app.Dialog  Inject android.app.Dialog  Int android.app.Dialog  LinearLayout android.app.Dialog  Long android.app.Dialog  ProgressBar android.app.Dialog  RecyclerView android.app.Dialog  Runnable android.app.Dialog  String android.app.Dialog  TextView android.app.Dialog  Unit android.app.Dialog  UsageStatsPermissionManager android.app.Dialog  VibrationService android.app.Dialog  getValue android.app.Dialog  lazy android.app.Dialog  provideDelegate android.app.Dialog  AnimationRepository android.app.Service  AppLifecycleManager android.app.Service  
AppRepository android.app.Service  BatteryManager android.app.Service  BatteryRepository android.app.Service  Boolean android.app.Service  BroadcastReceiver android.app.Service  Context android.app.Service  CoreBatteryStatsProvider android.app.Service  CoreBatteryStatsService android.app.Service  CoreBatteryStatus android.app.Service  DischargeSessionData android.app.Service  DischargeSessionRepository android.app.Service  Float android.app.Service  IBinder android.app.Service  Inject android.app.Service  Int android.app.Service  Intent android.app.Service  Long android.app.Service  Notification android.app.Service  PowerManager android.app.Service  Runnable android.app.Service  ScreenTimeValidationService android.app.Service  String android.app.Service  android android.app.Service  
UsageStats android.app.usage  BroadcastReceiver android.content  Context android.content  Intent android.content  SharedPreferences android.content  Activity android.content.Context  ActivityAnimationBinding android.content.Context  ActivityChargingOverlayBinding android.content.Context  ActivityEnterPasswordBinding android.content.Context  ActivityMainBinding android.content.Context  ActivityResultLauncher android.content.Context  ActivitySplashBinding android.content.Context  ActivityStartingBinding android.content.Context  AnimationRepository android.content.Context  AnimationViewModel android.content.Context  AppLifecycleManager android.content.Context  
AppRepository android.content.Context  AppViewModel android.content.Context  ApplovinBannerAdManager android.content.Context  ApplovinInterstitialAdManager android.content.Context  ApplovinRewardedAdManager android.content.Context  BackgroundPermissionDialog android.content.Context  BatteryManager android.content.Context  BatteryRepository android.content.Context  BatteryViewModel android.content.Context  Boolean android.content.Context  BroadcastReceiver android.content.Context  Bundle android.content.Context  Class android.content.Context  Context android.content.Context  CoreBatteryServiceHelper android.content.Context  CoreBatteryStatsProvider android.content.Context  CoreBatteryStatsService android.content.Context  CoreBatteryStatus android.content.Context  DischargeSessionData android.content.Context  DischargeSessionRepository android.content.Context  DynamicNavigationManager android.content.Context  #EnhancedDischargeTimerServiceHelper android.content.Context  	ExoPlayer android.content.Context  FirebaseRemoteConfigHelper android.content.Context  Float android.content.Context  Handler android.content.Context  IBinder android.content.Context  Inject android.content.Context  Int android.content.Context  Intent android.content.Context  	Lifecycle android.content.Context  Long android.content.Context  MODE_PRIVATE android.content.Context  MediaPlayer android.content.Context  Notification android.content.Context  OnLifecycleEvent android.content.Context  OptIn android.content.Context  PowerManager android.content.Context  PreferencesHelper android.content.Context  Runnable android.content.Context  ScreenTimeValidationService android.content.Context  StatsChargeRepository android.content.Context  String android.content.Context  'UnifiedBatteryNotificationServiceHelper android.content.Context  UnstableApi android.content.Context  UsageStatsPermissionManager android.content.Context  android android.content.Context  com android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  getValue android.content.Context  lazy android.content.Context  provideDelegate android.content.Context  Activity android.content.ContextWrapper  ActivityAnimationBinding android.content.ContextWrapper  ActivityChargingOverlayBinding android.content.ContextWrapper  ActivityEnterPasswordBinding android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityResultLauncher android.content.ContextWrapper  ActivitySplashBinding android.content.ContextWrapper  ActivityStartingBinding android.content.ContextWrapper  AnimationRepository android.content.ContextWrapper  AnimationViewModel android.content.ContextWrapper  AppLifecycleManager android.content.ContextWrapper  
AppRepository android.content.ContextWrapper  AppViewModel android.content.ContextWrapper  ApplovinBannerAdManager android.content.ContextWrapper  ApplovinInterstitialAdManager android.content.ContextWrapper  ApplovinRewardedAdManager android.content.ContextWrapper  BackgroundPermissionDialog android.content.ContextWrapper  BatteryManager android.content.ContextWrapper  BatteryRepository android.content.ContextWrapper  BatteryViewModel android.content.ContextWrapper  Boolean android.content.ContextWrapper  BroadcastReceiver android.content.ContextWrapper  Bundle android.content.ContextWrapper  Class android.content.ContextWrapper  Context android.content.ContextWrapper  CoreBatteryServiceHelper android.content.ContextWrapper  CoreBatteryStatsProvider android.content.ContextWrapper  CoreBatteryStatsService android.content.ContextWrapper  CoreBatteryStatus android.content.ContextWrapper  DischargeSessionData android.content.ContextWrapper  DischargeSessionRepository android.content.ContextWrapper  DynamicNavigationManager android.content.ContextWrapper  #EnhancedDischargeTimerServiceHelper android.content.ContextWrapper  	ExoPlayer android.content.ContextWrapper  FirebaseRemoteConfigHelper android.content.ContextWrapper  Float android.content.ContextWrapper  Handler android.content.ContextWrapper  IBinder android.content.ContextWrapper  Inject android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  	Lifecycle android.content.ContextWrapper  Long android.content.ContextWrapper  MediaPlayer android.content.ContextWrapper  Notification android.content.ContextWrapper  OnLifecycleEvent android.content.ContextWrapper  OptIn android.content.ContextWrapper  PowerManager android.content.ContextWrapper  PreferencesHelper android.content.ContextWrapper  Runnable android.content.ContextWrapper  ScreenTimeValidationService android.content.ContextWrapper  StatsChargeRepository android.content.ContextWrapper  String android.content.ContextWrapper  'UnifiedBatteryNotificationServiceHelper android.content.ContextWrapper  UnstableApi android.content.ContextWrapper  UsageStatsPermissionManager android.content.ContextWrapper  android android.content.ContextWrapper  com android.content.ContextWrapper  getValue android.content.ContextWrapper  lazy android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  edit !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  putFloat (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  ApplicationInfo android.content.pm  Drawable android.graphics.drawable  MediaPlayer 
android.media  BatteryManager 
android.os  Bundle 
android.os  Handler 
android.os  IBinder 
android.os  PowerManager 
android.os  AttributeSet android.util  Log android.util  d android.util.Log  LayoutInflater android.view  View android.view  	ViewGroup android.view  ActivityAnimationBinding  android.view.ContextThemeWrapper  ActivityChargingOverlayBinding  android.view.ContextThemeWrapper  ActivityEnterPasswordBinding  android.view.ContextThemeWrapper  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityResultLauncher  android.view.ContextThemeWrapper  ActivitySplashBinding  android.view.ContextThemeWrapper  ActivityStartingBinding  android.view.ContextThemeWrapper  AnimationViewModel  android.view.ContextThemeWrapper  
AppRepository  android.view.ContextThemeWrapper  AppViewModel  android.view.ContextThemeWrapper  ApplovinBannerAdManager  android.view.ContextThemeWrapper  ApplovinInterstitialAdManager  android.view.ContextThemeWrapper  ApplovinRewardedAdManager  android.view.ContextThemeWrapper  BackgroundPermissionDialog  android.view.ContextThemeWrapper  BatteryViewModel  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Class  android.view.ContextThemeWrapper  CoreBatteryStatsProvider  android.view.ContextThemeWrapper  DischargeSessionRepository  android.view.ContextThemeWrapper  DynamicNavigationManager  android.view.ContextThemeWrapper  #EnhancedDischargeTimerServiceHelper  android.view.ContextThemeWrapper  	ExoPlayer  android.view.ContextThemeWrapper  FirebaseRemoteConfigHelper  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  Inject  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  MediaPlayer  android.view.ContextThemeWrapper  OptIn  android.view.ContextThemeWrapper  Runnable  android.view.ContextThemeWrapper  StatsChargeRepository  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  'UnifiedBatteryNotificationServiceHelper  android.view.ContextThemeWrapper  UnstableApi  android.view.ContextThemeWrapper  UsageStatsPermissionManager  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  lazy  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  AttributeSet android.view.View  Context android.view.View  Int android.view.View  Button android.widget  	ImageView android.widget  LinearLayout android.widget  ProgressBar android.widget  SeekBar android.widget  TextView android.widget  AttributeSet android.widget.ProgressBar  Context android.widget.ProgressBar  Int android.widget.ProgressBar  ComponentActivity androidx.activity  ActivityAnimationBinding #androidx.activity.ComponentActivity  ActivityChargingOverlayBinding #androidx.activity.ComponentActivity  ActivityEnterPasswordBinding #androidx.activity.ComponentActivity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityResultLauncher #androidx.activity.ComponentActivity  ActivitySplashBinding #androidx.activity.ComponentActivity  ActivityStartingBinding #androidx.activity.ComponentActivity  AnimationViewModel #androidx.activity.ComponentActivity  
AppRepository #androidx.activity.ComponentActivity  AppViewModel #androidx.activity.ComponentActivity  ApplovinBannerAdManager #androidx.activity.ComponentActivity  ApplovinInterstitialAdManager #androidx.activity.ComponentActivity  ApplovinRewardedAdManager #androidx.activity.ComponentActivity  BackgroundPermissionDialog #androidx.activity.ComponentActivity  BatteryViewModel #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Class #androidx.activity.ComponentActivity  CoreBatteryStatsProvider #androidx.activity.ComponentActivity  DischargeSessionRepository #androidx.activity.ComponentActivity  DynamicNavigationManager #androidx.activity.ComponentActivity  #EnhancedDischargeTimerServiceHelper #androidx.activity.ComponentActivity  	ExoPlayer #androidx.activity.ComponentActivity  FirebaseRemoteConfigHelper #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  Inject #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  MediaPlayer #androidx.activity.ComponentActivity  OptIn #androidx.activity.ComponentActivity  Runnable #androidx.activity.ComponentActivity  StatsChargeRepository #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  'UnifiedBatteryNotificationServiceHelper #androidx.activity.ComponentActivity  UnstableApi #androidx.activity.ComponentActivity  UsageStatsPermissionManager #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  lazy #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  ActivityAnimationBinding -androidx.activity.ComponentActivity.Companion  ActivityChargingOverlayBinding -androidx.activity.ComponentActivity.Companion  ActivityEnterPasswordBinding -androidx.activity.ComponentActivity.Companion  ActivityMainBinding -androidx.activity.ComponentActivity.Companion  ActivitySplashBinding -androidx.activity.ComponentActivity.Companion  ActivityStartingBinding -androidx.activity.ComponentActivity.Companion  UnstableApi -androidx.activity.ComponentActivity.Companion  getGETValue -androidx.activity.ComponentActivity.Companion  getGetValue -androidx.activity.ComponentActivity.Companion  getLAZY -androidx.activity.ComponentActivity.Companion  getLazy -androidx.activity.ComponentActivity.Companion  getPROVIDEDelegate -androidx.activity.ComponentActivity.Companion  getProvideDelegate -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  lazy -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  ActivityResultLauncher androidx.activity.result  OptIn androidx.annotation  ActivityAnimationBinding (androidx.appcompat.app.AppCompatActivity  ActivityChargingOverlayBinding (androidx.appcompat.app.AppCompatActivity  ActivityEnterPasswordBinding (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultLauncher (androidx.appcompat.app.AppCompatActivity  ActivitySplashBinding (androidx.appcompat.app.AppCompatActivity  ActivityStartingBinding (androidx.appcompat.app.AppCompatActivity  AnimationViewModel (androidx.appcompat.app.AppCompatActivity  
AppRepository (androidx.appcompat.app.AppCompatActivity  AppViewModel (androidx.appcompat.app.AppCompatActivity  ApplovinBannerAdManager (androidx.appcompat.app.AppCompatActivity  ApplovinInterstitialAdManager (androidx.appcompat.app.AppCompatActivity  ApplovinRewardedAdManager (androidx.appcompat.app.AppCompatActivity  BackgroundPermissionDialog (androidx.appcompat.app.AppCompatActivity  BatteryViewModel (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  Class (androidx.appcompat.app.AppCompatActivity  CoreBatteryStatsProvider (androidx.appcompat.app.AppCompatActivity  DischargeSessionRepository (androidx.appcompat.app.AppCompatActivity  DynamicNavigationManager (androidx.appcompat.app.AppCompatActivity  #EnhancedDischargeTimerServiceHelper (androidx.appcompat.app.AppCompatActivity  	ExoPlayer (androidx.appcompat.app.AppCompatActivity  FirebaseRemoteConfigHelper (androidx.appcompat.app.AppCompatActivity  Handler (androidx.appcompat.app.AppCompatActivity  Inject (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  Long (androidx.appcompat.app.AppCompatActivity  MediaPlayer (androidx.appcompat.app.AppCompatActivity  OptIn (androidx.appcompat.app.AppCompatActivity  Runnable (androidx.appcompat.app.AppCompatActivity  StatsChargeRepository (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  'UnifiedBatteryNotificationServiceHelper (androidx.appcompat.app.AppCompatActivity  UnstableApi (androidx.appcompat.app.AppCompatActivity  UsageStatsPermissionManager (androidx.appcompat.app.AppCompatActivity  getValue (androidx.appcompat.app.AppCompatActivity  lazy (androidx.appcompat.app.AppCompatActivity  provideDelegate (androidx.appcompat.app.AppCompatActivity  ActivityAnimationBinding #androidx.core.app.ComponentActivity  ActivityChargingOverlayBinding #androidx.core.app.ComponentActivity  ActivityEnterPasswordBinding #androidx.core.app.ComponentActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityResultLauncher #androidx.core.app.ComponentActivity  ActivitySplashBinding #androidx.core.app.ComponentActivity  ActivityStartingBinding #androidx.core.app.ComponentActivity  AnimationViewModel #androidx.core.app.ComponentActivity  
AppRepository #androidx.core.app.ComponentActivity  AppViewModel #androidx.core.app.ComponentActivity  ApplovinBannerAdManager #androidx.core.app.ComponentActivity  ApplovinInterstitialAdManager #androidx.core.app.ComponentActivity  ApplovinRewardedAdManager #androidx.core.app.ComponentActivity  BackgroundPermissionDialog #androidx.core.app.ComponentActivity  BatteryViewModel #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Class #androidx.core.app.ComponentActivity  CoreBatteryStatsProvider #androidx.core.app.ComponentActivity  DischargeSessionRepository #androidx.core.app.ComponentActivity  DynamicNavigationManager #androidx.core.app.ComponentActivity  #EnhancedDischargeTimerServiceHelper #androidx.core.app.ComponentActivity  	ExoPlayer #androidx.core.app.ComponentActivity  FirebaseRemoteConfigHelper #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  Inject #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  MediaPlayer #androidx.core.app.ComponentActivity  OptIn #androidx.core.app.ComponentActivity  Runnable #androidx.core.app.ComponentActivity  StatsChargeRepository #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  'UnifiedBatteryNotificationServiceHelper #androidx.core.app.ComponentActivity  UnstableApi #androidx.core.app.ComponentActivity  UsageStatsPermissionManager #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  lazy #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  SplashScreen androidx.core.splashscreen  	Companion 'androidx.core.splashscreen.SplashScreen  Fragment androidx.fragment.app  FragmentManager androidx.fragment.app  AnimationAdapter androidx.fragment.app.Fragment  AnimationCategory androidx.fragment.app.Fragment  AnimationHelper androidx.fragment.app.Fragment  AnimationViewModel androidx.fragment.app.Fragment  AppLifecycleManager androidx.fragment.app.Fragment  
AppRepository androidx.fragment.app.Fragment  AppViewModel androidx.fragment.app.Fragment  ApplovinInterstitialAdManager androidx.fragment.app.Fragment  BatteryGalleryViewModel androidx.fragment.app.Fragment  BatteryStyleAdapter androidx.fragment.app.Fragment  BatteryViewModel androidx.fragment.app.Fragment  Boolean androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  Button androidx.fragment.app.Fragment  CategoryAdapter androidx.fragment.app.Fragment  CircularProgressIndicator androidx.fragment.app.Fragment  DischargeUiUpdater androidx.fragment.app.Fragment  DischargeViewModel androidx.fragment.app.Fragment  Double androidx.fragment.app.Fragment  Entry androidx.fragment.app.Fragment  FirebaseRemoteConfigHelper androidx.fragment.app.Fragment  Float androidx.fragment.app.Fragment  FragmentAnimationGridBinding androidx.fragment.app.Fragment  FragmentChargeBinding androidx.fragment.app.Fragment  FragmentHealthBinding androidx.fragment.app.Fragment  FragmentSettingsBinding androidx.fragment.app.Fragment  HealthCalculationMode androidx.fragment.app.Fragment  HealthViewModel androidx.fragment.app.Fragment  HistoryType androidx.fragment.app.Fragment  InfoButtonManager androidx.fragment.app.Fragment  Inject androidx.fragment.app.Fragment  Int androidx.fragment.app.Fragment  Job androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  LineDataSet androidx.fragment.app.Fragment  List androidx.fragment.app.Fragment  Log androidx.fragment.app.Fragment  Long androidx.fragment.app.Fragment  NewFragmentDischargeBinding androidx.fragment.app.Fragment  Number androidx.fragment.app.Fragment  Pair androidx.fragment.app.Fragment  PowerManager androidx.fragment.app.Fragment  PreferencesHelper androidx.fragment.app.Fragment  SeekBar androidx.fragment.app.Fragment  StatsChargeUiState androidx.fragment.app.Fragment  StatsChargeViewModel androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  TAG androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  
TimeConverter androidx.fragment.app.Fragment  VibrationService androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  android androidx.fragment.app.Fragment  com androidx.fragment.app.Fragment  getValue androidx.fragment.app.Fragment  lazy androidx.fragment.app.Fragment  provideDelegate androidx.fragment.app.Fragment  ActivityAnimationBinding &androidx.fragment.app.FragmentActivity  ActivityChargingOverlayBinding &androidx.fragment.app.FragmentActivity  ActivityEnterPasswordBinding &androidx.fragment.app.FragmentActivity  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityResultLauncher &androidx.fragment.app.FragmentActivity  ActivitySplashBinding &androidx.fragment.app.FragmentActivity  ActivityStartingBinding &androidx.fragment.app.FragmentActivity  AnimationViewModel &androidx.fragment.app.FragmentActivity  
AppRepository &androidx.fragment.app.FragmentActivity  AppViewModel &androidx.fragment.app.FragmentActivity  ApplovinBannerAdManager &androidx.fragment.app.FragmentActivity  ApplovinInterstitialAdManager &androidx.fragment.app.FragmentActivity  ApplovinRewardedAdManager &androidx.fragment.app.FragmentActivity  BackgroundPermissionDialog &androidx.fragment.app.FragmentActivity  BatteryViewModel &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  Class &androidx.fragment.app.FragmentActivity  CoreBatteryStatsProvider &androidx.fragment.app.FragmentActivity  DischargeSessionRepository &androidx.fragment.app.FragmentActivity  DynamicNavigationManager &androidx.fragment.app.FragmentActivity  #EnhancedDischargeTimerServiceHelper &androidx.fragment.app.FragmentActivity  	ExoPlayer &androidx.fragment.app.FragmentActivity  FirebaseRemoteConfigHelper &androidx.fragment.app.FragmentActivity  Handler &androidx.fragment.app.FragmentActivity  Inject &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  Long &androidx.fragment.app.FragmentActivity  MediaPlayer &androidx.fragment.app.FragmentActivity  OptIn &androidx.fragment.app.FragmentActivity  Runnable &androidx.fragment.app.FragmentActivity  StatsChargeRepository &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  'UnifiedBatteryNotificationServiceHelper &androidx.fragment.app.FragmentActivity  UnstableApi &androidx.fragment.app.FragmentActivity  UsageStatsPermissionManager &androidx.fragment.app.FragmentActivity  getValue &androidx.fragment.app.FragmentActivity  lazy &androidx.fragment.app.FragmentActivity  provideDelegate &androidx.fragment.app.FragmentActivity  DefaultLifecycleObserver androidx.lifecycle  	Lifecycle androidx.lifecycle  LifecycleObserver androidx.lifecycle  LifecycleOwner androidx.lifecycle  OnLifecycleEvent androidx.lifecycle  	ViewModel androidx.lifecycle  Event androidx.lifecycle.Lifecycle  ON_START "androidx.lifecycle.Lifecycle.Event  ON_START ,androidx.lifecycle.Lifecycle.Event.Companion  AnimationApplyEntry androidx.lifecycle.ViewModel  AnimationRepository androidx.lifecycle.ViewModel  
AppRepository androidx.lifecycle.ViewModel  ApplicationContext androidx.lifecycle.ViewModel  BatteryGalleryEvent androidx.lifecycle.ViewModel  BatteryGalleryState androidx.lifecycle.ViewModel  BatteryRepository androidx.lifecycle.ViewModel  BatteryStyle androidx.lifecycle.ViewModel  BatteryStyleCategory androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  $CalculateSimpleChargeEstimateUseCase androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  CoreBatteryStatsProvider androidx.lifecycle.ViewModel  
Deprecated androidx.lifecycle.ViewModel  DischargeCalculator androidx.lifecycle.ViewModel  DischargeSessionRepository androidx.lifecycle.ViewModel  DischargeUiState androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  #EnhancedDischargeTimerServiceHelper androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  GetBatteryStylesUseCase androidx.lifecycle.ViewModel  HealthCalculationMode androidx.lifecycle.ViewModel  HealthChartData androidx.lifecycle.ViewModel  HealthRepository androidx.lifecycle.ViewModel  HealthStatus androidx.lifecycle.ViewModel  
HealthUiState androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  ReplaceWith androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  StatsChargeRepository androidx.lifecycle.ViewModel  StatsChargeSession androidx.lifecycle.ViewModel  StatsChargeStatus androidx.lifecycle.ViewModel  StatsChargeUiState androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  
TimeConverter androidx.lifecycle.ViewModel  android androidx.lifecycle.ViewModel  UnstableApi androidx.media3.common.util  	ExoPlayer androidx.media3.exoplayer  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  	ImageView (androidx.recyclerview.widget.ListAdapter  RecyclerView (androidx.recyclerview.widget.ListAdapter  TextView (androidx.recyclerview.widget.ListAdapter  View (androidx.recyclerview.widget.ListAdapter  
ViewHolder )androidx.recyclerview.widget.RecyclerView  	ImageView 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ImageView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	LineChart #com.github.mikephil.charting.charts  Entry !com.github.mikephil.charting.data  LineDataSet !com.github.mikephil.charting.data  BottomNavigationView ,com.google.android.material.bottomnavigation  CircularProgressIndicator -com.google.android.material.progressindicator  Gson com.google.gson  toJson com.google.gson.Gson  SerializedName com.google.gson.annotations  AdLibHiltApplication com.tqhit.adlib.sdk  Activity (com.tqhit.adlib.sdk.AdLibHiltApplication  
AppRepository (com.tqhit.adlib.sdk.AdLibHiltApplication  Bundle (com.tqhit.adlib.sdk.AdLibHiltApplication  CoreBatteryServiceHelper (com.tqhit.adlib.sdk.AdLibHiltApplication  Inject (com.tqhit.adlib.sdk.AdLibHiltApplication  Int (com.tqhit.adlib.sdk.AdLibHiltApplication  	Lifecycle (com.tqhit.adlib.sdk.AdLibHiltApplication  Long (com.tqhit.adlib.sdk.AdLibHiltApplication  OnLifecycleEvent (com.tqhit.adlib.sdk.AdLibHiltApplication  PreferencesHelper (com.tqhit.adlib.sdk.AdLibHiltApplication  com (com.tqhit.adlib.sdk.AdLibHiltApplication  AnalyticsTracker com.tqhit.adlib.sdk.analytics  Activity -com.tqhit.adlib.sdk.base.AdLibBaseApplication  
AppRepository -com.tqhit.adlib.sdk.base.AdLibBaseApplication  Bundle -com.tqhit.adlib.sdk.base.AdLibBaseApplication  CoreBatteryServiceHelper -com.tqhit.adlib.sdk.base.AdLibBaseApplication  Inject -com.tqhit.adlib.sdk.base.AdLibBaseApplication  Int -com.tqhit.adlib.sdk.base.AdLibBaseApplication  	Lifecycle -com.tqhit.adlib.sdk.base.AdLibBaseApplication  Long -com.tqhit.adlib.sdk.base.AdLibBaseApplication  OnLifecycleEvent -com.tqhit.adlib.sdk.base.AdLibBaseApplication  PreferencesHelper -com.tqhit.adlib.sdk.base.AdLibBaseApplication  com -com.tqhit.adlib.sdk.base.AdLibBaseApplication  AdLibBaseActivity com.tqhit.adlib.sdk.base.ui  AdLibBaseDialog com.tqhit.adlib.sdk.base.ui  AdLibBaseFragment com.tqhit.adlib.sdk.base.ui  ActivityAnimationBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ActivityChargingOverlayBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ActivityEnterPasswordBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ActivityMainBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ActivityResultLauncher -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ActivitySplashBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ActivityStartingBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  AnimationViewModel -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  
AppRepository -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  AppViewModel -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ApplovinBannerAdManager -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ApplovinInterstitialAdManager -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  ApplovinRewardedAdManager -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  BackgroundPermissionDialog -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  BatteryViewModel -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Boolean -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Bundle -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Class -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  CoreBatteryStatsProvider -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  DischargeSessionRepository -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  DynamicNavigationManager -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  #EnhancedDischargeTimerServiceHelper -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  	ExoPlayer -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  FirebaseRemoteConfigHelper -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Handler -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Inject -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Int -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Intent -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Long -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  MediaPlayer -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  OptIn -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Runnable -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  StatsChargeRepository -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  String -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  'UnifiedBatteryNotificationServiceHelper -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  UnstableApi -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  UsageStatsPermissionManager -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  getValue -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  lazy -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  provideDelegate -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity  Activity +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  ActivityContext +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  ActivityResultLauncher +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  AppViewModel +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  BatteryViewModel +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  Boolean +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  Context +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  DialogSelectBatteryAlarmBinding +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  "DialogSelectBatteryAlarmLowBinding +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  Inject +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  Runnable +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  String +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  Unit +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  VibrationService +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  getValue +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  lazy +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  provideDelegate +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog  AnimationAdapter -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  AnimationCategory -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  AnimationViewModel -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  
AppRepository -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  AppViewModel -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  ApplovinInterstitialAdManager -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  BatteryGalleryViewModel -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  BatteryStyleAdapter -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  BatteryViewModel -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Boolean -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  CategoryAdapter -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Double -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Entry -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  FirebaseRemoteConfigHelper -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Float -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  FragmentAnimationGridBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  FragmentChargeBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  FragmentHealthBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  FragmentSettingsBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  HealthCalculationMode -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  HealthViewModel -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  HistoryType -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Inject -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Int -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Job -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  LineDataSet -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  List -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Log -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Long -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  NewFragmentDischargeBinding -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Number -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  Pair -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  PreferencesHelper -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  String -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  TAG -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  VibrationService -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  android -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  com -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  getValue -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  lazy -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  provideDelegate -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment  PreferencesHelper com.tqhit.adlib.sdk.data.local  saveBoolean 0com.tqhit.adlib.sdk.data.local.PreferencesHelper  saveInt 0com.tqhit.adlib.sdk.data.local.PreferencesHelper  
saveString 0com.tqhit.adlib.sdk.data.local.PreferencesHelper  FirebaseRemoteConfigHelper com.tqhit.adlib.sdk.firebase  BatteryApplication com.tqhit.battery.one  Int com.tqhit.battery.one  	Lifecycle com.tqhit.battery.one  Long com.tqhit.battery.one  com com.tqhit.battery.one  Activity (com.tqhit.battery.one.BatteryApplication  
AppRepository (com.tqhit.battery.one.BatteryApplication  Bundle (com.tqhit.battery.one.BatteryApplication  CoreBatteryServiceHelper (com.tqhit.battery.one.BatteryApplication  Inject (com.tqhit.battery.one.BatteryApplication  Int (com.tqhit.battery.one.BatteryApplication  	Lifecycle (com.tqhit.battery.one.BatteryApplication  Long (com.tqhit.battery.one.BatteryApplication  OnLifecycleEvent (com.tqhit.battery.one.BatteryApplication  PreferencesHelper (com.tqhit.battery.one.BatteryApplication  com (com.tqhit.battery.one.BatteryApplication  Activity 2com.tqhit.battery.one.BatteryApplication.Companion  
AppRepository 2com.tqhit.battery.one.BatteryApplication.Companion  Bundle 2com.tqhit.battery.one.BatteryApplication.Companion  CoreBatteryServiceHelper 2com.tqhit.battery.one.BatteryApplication.Companion  Inject 2com.tqhit.battery.one.BatteryApplication.Companion  Int 2com.tqhit.battery.one.BatteryApplication.Companion  	Lifecycle 2com.tqhit.battery.one.BatteryApplication.Companion  Long 2com.tqhit.battery.one.BatteryApplication.Companion  OnLifecycleEvent 2com.tqhit.battery.one.BatteryApplication.Companion  PreferencesHelper 2com.tqhit.battery.one.BatteryApplication.Companion  com 2com.tqhit.battery.one.BatteryApplication.Companion  ActivityAnimationBinding (com.tqhit.battery.one.activity.animation  AnimationActivity (com.tqhit.battery.one.activity.animation  Int (com.tqhit.battery.one.activity.animation  String (com.tqhit.battery.one.activity.animation  UnstableApi (com.tqhit.battery.one.activity.animation  getValue (com.tqhit.battery.one.activity.animation  lazy (com.tqhit.battery.one.activity.animation  provideDelegate (com.tqhit.battery.one.activity.animation  ActivityAnimationBinding :com.tqhit.battery.one.activity.animation.AnimationActivity  ActivityResultLauncher :com.tqhit.battery.one.activity.animation.AnimationActivity  AnimationViewModel :com.tqhit.battery.one.activity.animation.AnimationActivity  
AppRepository :com.tqhit.battery.one.activity.animation.AnimationActivity  ApplovinRewardedAdManager :com.tqhit.battery.one.activity.animation.AnimationActivity  	ExoPlayer :com.tqhit.battery.one.activity.animation.AnimationActivity  FirebaseRemoteConfigHelper :com.tqhit.battery.one.activity.animation.AnimationActivity  Inject :com.tqhit.battery.one.activity.animation.AnimationActivity  Int :com.tqhit.battery.one.activity.animation.AnimationActivity  Intent :com.tqhit.battery.one.activity.animation.AnimationActivity  OptIn :com.tqhit.battery.one.activity.animation.AnimationActivity  String :com.tqhit.battery.one.activity.animation.AnimationActivity  UnstableApi :com.tqhit.battery.one.activity.animation.AnimationActivity  getGETValue :com.tqhit.battery.one.activity.animation.AnimationActivity  getGetValue :com.tqhit.battery.one.activity.animation.AnimationActivity  getLAYOUTInflater :com.tqhit.battery.one.activity.animation.AnimationActivity  getLAZY :com.tqhit.battery.one.activity.animation.AnimationActivity  getLayoutInflater :com.tqhit.battery.one.activity.animation.AnimationActivity  getLazy :com.tqhit.battery.one.activity.animation.AnimationActivity  getPROVIDEDelegate :com.tqhit.battery.one.activity.animation.AnimationActivity  getProvideDelegate :com.tqhit.battery.one.activity.animation.AnimationActivity  getValue :com.tqhit.battery.one.activity.animation.AnimationActivity  layoutInflater :com.tqhit.battery.one.activity.animation.AnimationActivity  lazy :com.tqhit.battery.one.activity.animation.AnimationActivity  provideDelegate :com.tqhit.battery.one.activity.animation.AnimationActivity  setLayoutInflater :com.tqhit.battery.one.activity.animation.AnimationActivity  ActivityMainBinding #com.tqhit.battery.one.activity.main  Boolean #com.tqhit.battery.one.activity.main  Class #com.tqhit.battery.one.activity.main  Int #com.tqhit.battery.one.activity.main  Long #com.tqhit.battery.one.activity.main  MainActivity #com.tqhit.battery.one.activity.main  getValue #com.tqhit.battery.one.activity.main  lazy #com.tqhit.battery.one.activity.main  provideDelegate #com.tqhit.battery.one.activity.main  ActivityMainBinding 0com.tqhit.battery.one.activity.main.MainActivity  
AppRepository 0com.tqhit.battery.one.activity.main.MainActivity  ApplovinBannerAdManager 0com.tqhit.battery.one.activity.main.MainActivity  BackgroundPermissionDialog 0com.tqhit.battery.one.activity.main.MainActivity  Boolean 0com.tqhit.battery.one.activity.main.MainActivity  Bundle 0com.tqhit.battery.one.activity.main.MainActivity  Class 0com.tqhit.battery.one.activity.main.MainActivity  CoreBatteryStatsProvider 0com.tqhit.battery.one.activity.main.MainActivity  DischargeSessionRepository 0com.tqhit.battery.one.activity.main.MainActivity  DynamicNavigationManager 0com.tqhit.battery.one.activity.main.MainActivity  #EnhancedDischargeTimerServiceHelper 0com.tqhit.battery.one.activity.main.MainActivity  FirebaseRemoteConfigHelper 0com.tqhit.battery.one.activity.main.MainActivity  Inject 0com.tqhit.battery.one.activity.main.MainActivity  Int 0com.tqhit.battery.one.activity.main.MainActivity  Long 0com.tqhit.battery.one.activity.main.MainActivity  StatsChargeRepository 0com.tqhit.battery.one.activity.main.MainActivity  'UnifiedBatteryNotificationServiceHelper 0com.tqhit.battery.one.activity.main.MainActivity  UsageStatsPermissionManager 0com.tqhit.battery.one.activity.main.MainActivity  getGETValue 0com.tqhit.battery.one.activity.main.MainActivity  getGetValue 0com.tqhit.battery.one.activity.main.MainActivity  getLAYOUTInflater 0com.tqhit.battery.one.activity.main.MainActivity  getLAZY 0com.tqhit.battery.one.activity.main.MainActivity  getLayoutInflater 0com.tqhit.battery.one.activity.main.MainActivity  getLazy 0com.tqhit.battery.one.activity.main.MainActivity  getPROVIDEDelegate 0com.tqhit.battery.one.activity.main.MainActivity  getProvideDelegate 0com.tqhit.battery.one.activity.main.MainActivity  getValue 0com.tqhit.battery.one.activity.main.MainActivity  layoutInflater 0com.tqhit.battery.one.activity.main.MainActivity  lazy 0com.tqhit.battery.one.activity.main.MainActivity  provideDelegate 0com.tqhit.battery.one.activity.main.MainActivity  setLayoutInflater 0com.tqhit.battery.one.activity.main.MainActivity  ActivityMainBinding :com.tqhit.battery.one.activity.main.MainActivity.Companion  
AppRepository :com.tqhit.battery.one.activity.main.MainActivity.Companion  ApplovinBannerAdManager :com.tqhit.battery.one.activity.main.MainActivity.Companion  BackgroundPermissionDialog :com.tqhit.battery.one.activity.main.MainActivity.Companion  Boolean :com.tqhit.battery.one.activity.main.MainActivity.Companion  Bundle :com.tqhit.battery.one.activity.main.MainActivity.Companion  Class :com.tqhit.battery.one.activity.main.MainActivity.Companion  CoreBatteryStatsProvider :com.tqhit.battery.one.activity.main.MainActivity.Companion  DischargeSessionRepository :com.tqhit.battery.one.activity.main.MainActivity.Companion  DynamicNavigationManager :com.tqhit.battery.one.activity.main.MainActivity.Companion  #EnhancedDischargeTimerServiceHelper :com.tqhit.battery.one.activity.main.MainActivity.Companion  FirebaseRemoteConfigHelper :com.tqhit.battery.one.activity.main.MainActivity.Companion  Inject :com.tqhit.battery.one.activity.main.MainActivity.Companion  Int :com.tqhit.battery.one.activity.main.MainActivity.Companion  Long :com.tqhit.battery.one.activity.main.MainActivity.Companion  StatsChargeRepository :com.tqhit.battery.one.activity.main.MainActivity.Companion  'UnifiedBatteryNotificationServiceHelper :com.tqhit.battery.one.activity.main.MainActivity.Companion  UsageStatsPermissionManager :com.tqhit.battery.one.activity.main.MainActivity.Companion  getGETValue :com.tqhit.battery.one.activity.main.MainActivity.Companion  getGetValue :com.tqhit.battery.one.activity.main.MainActivity.Companion  getLAZY :com.tqhit.battery.one.activity.main.MainActivity.Companion  getLazy :com.tqhit.battery.one.activity.main.MainActivity.Companion  getPROVIDEDelegate :com.tqhit.battery.one.activity.main.MainActivity.Companion  getProvideDelegate :com.tqhit.battery.one.activity.main.MainActivity.Companion  getValue :com.tqhit.battery.one.activity.main.MainActivity.Companion  lazy :com.tqhit.battery.one.activity.main.MainActivity.Companion  provideDelegate :com.tqhit.battery.one.activity.main.MainActivity.Companion  ActivityChargingOverlayBinding &com.tqhit.battery.one.activity.overlay  ChargingOverlayActivity &com.tqhit.battery.one.activity.overlay  String &com.tqhit.battery.one.activity.overlay  UnstableApi &com.tqhit.battery.one.activity.overlay  getValue &com.tqhit.battery.one.activity.overlay  lazy &com.tqhit.battery.one.activity.overlay  provideDelegate &com.tqhit.battery.one.activity.overlay  ActivityChargingOverlayBinding >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  
AppRepository >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  	ExoPlayer >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  Inject >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  Intent >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  OptIn >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  String >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  UnstableApi >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getGETValue >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getGetValue >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getLAYOUTInflater >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getLAZY >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getLayoutInflater >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getLazy >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getPROVIDEDelegate >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getProvideDelegate >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  getValue >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  layoutInflater >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  lazy >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  provideDelegate >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  setLayoutInflater >com.tqhit.battery.one.activity.overlay.ChargingOverlayActivity  ActivityEnterPasswordBinding 'com.tqhit.battery.one.activity.password  EnterPasswordActivity 'com.tqhit.battery.one.activity.password  Runnable 'com.tqhit.battery.one.activity.password  getValue 'com.tqhit.battery.one.activity.password  lazy 'com.tqhit.battery.one.activity.password  provideDelegate 'com.tqhit.battery.one.activity.password  ActivityEnterPasswordBinding =com.tqhit.battery.one.activity.password.EnterPasswordActivity  
AppRepository =com.tqhit.battery.one.activity.password.EnterPasswordActivity  Handler =com.tqhit.battery.one.activity.password.EnterPasswordActivity  Inject =com.tqhit.battery.one.activity.password.EnterPasswordActivity  MediaPlayer =com.tqhit.battery.one.activity.password.EnterPasswordActivity  Runnable =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getGETValue =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getGetValue =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getLAYOUTInflater =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getLAZY =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getLayoutInflater =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getLazy =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getPROVIDEDelegate =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getProvideDelegate =com.tqhit.battery.one.activity.password.EnterPasswordActivity  getValue =com.tqhit.battery.one.activity.password.EnterPasswordActivity  layoutInflater =com.tqhit.battery.one.activity.password.EnterPasswordActivity  lazy =com.tqhit.battery.one.activity.password.EnterPasswordActivity  provideDelegate =com.tqhit.battery.one.activity.password.EnterPasswordActivity  setLayoutInflater =com.tqhit.battery.one.activity.password.EnterPasswordActivity  ActivityEnterPasswordBinding Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  
AppRepository Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  Handler Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  Inject Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  MediaPlayer Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  Runnable Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  getGETValue Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  getGetValue Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  getLAZY Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  getLazy Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  getPROVIDEDelegate Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  getProvideDelegate Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  getValue Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  lazy Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  provideDelegate Gcom.tqhit.battery.one.activity.password.EnterPasswordActivity.Companion  ActivitySplashBinding %com.tqhit.battery.one.activity.splash  SplashActivity %com.tqhit.battery.one.activity.splash  getValue %com.tqhit.battery.one.activity.splash  lazy %com.tqhit.battery.one.activity.splash  provideDelegate %com.tqhit.battery.one.activity.splash  ActivitySplashBinding 4com.tqhit.battery.one.activity.splash.SplashActivity  AppViewModel 4com.tqhit.battery.one.activity.splash.SplashActivity  Bundle 4com.tqhit.battery.one.activity.splash.SplashActivity  getGETValue 4com.tqhit.battery.one.activity.splash.SplashActivity  getGetValue 4com.tqhit.battery.one.activity.splash.SplashActivity  getLAYOUTInflater 4com.tqhit.battery.one.activity.splash.SplashActivity  getLAZY 4com.tqhit.battery.one.activity.splash.SplashActivity  getLayoutInflater 4com.tqhit.battery.one.activity.splash.SplashActivity  getLazy 4com.tqhit.battery.one.activity.splash.SplashActivity  getPROVIDEDelegate 4com.tqhit.battery.one.activity.splash.SplashActivity  getProvideDelegate 4com.tqhit.battery.one.activity.splash.SplashActivity  getValue 4com.tqhit.battery.one.activity.splash.SplashActivity  layoutInflater 4com.tqhit.battery.one.activity.splash.SplashActivity  lazy 4com.tqhit.battery.one.activity.splash.SplashActivity  provideDelegate 4com.tqhit.battery.one.activity.splash.SplashActivity  setLayoutInflater 4com.tqhit.battery.one.activity.splash.SplashActivity  ActivitySplashBinding >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  AppViewModel >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  Bundle >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  getGETValue >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  getGetValue >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  getLAZY >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  getLazy >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  getPROVIDEDelegate >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  getProvideDelegate >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  getValue >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  lazy >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  provideDelegate >com.tqhit.battery.one.activity.splash.SplashActivity.Companion  ActivityStartingBinding 'com.tqhit.battery.one.activity.starting  StartingActivity 'com.tqhit.battery.one.activity.starting  getValue 'com.tqhit.battery.one.activity.starting  lazy 'com.tqhit.battery.one.activity.starting  provideDelegate 'com.tqhit.battery.one.activity.starting  ActivityStartingBinding 8com.tqhit.battery.one.activity.starting.StartingActivity  AppViewModel 8com.tqhit.battery.one.activity.starting.StartingActivity  ApplovinInterstitialAdManager 8com.tqhit.battery.one.activity.starting.StartingActivity  BatteryViewModel 8com.tqhit.battery.one.activity.starting.StartingActivity  Inject 8com.tqhit.battery.one.activity.starting.StartingActivity  getGETValue 8com.tqhit.battery.one.activity.starting.StartingActivity  getGetValue 8com.tqhit.battery.one.activity.starting.StartingActivity  getLAYOUTInflater 8com.tqhit.battery.one.activity.starting.StartingActivity  getLAZY 8com.tqhit.battery.one.activity.starting.StartingActivity  getLayoutInflater 8com.tqhit.battery.one.activity.starting.StartingActivity  getLazy 8com.tqhit.battery.one.activity.starting.StartingActivity  getPROVIDEDelegate 8com.tqhit.battery.one.activity.starting.StartingActivity  getProvideDelegate 8com.tqhit.battery.one.activity.starting.StartingActivity  getValue 8com.tqhit.battery.one.activity.starting.StartingActivity  layoutInflater 8com.tqhit.battery.one.activity.starting.StartingActivity  lazy 8com.tqhit.battery.one.activity.starting.StartingActivity  provideDelegate 8com.tqhit.battery.one.activity.starting.StartingActivity  setLayoutInflater 8com.tqhit.battery.one.activity.starting.StartingActivity  ApplovinAppOpenAdManager com.tqhit.battery.one.ads.core  ApplovinBannerAdManager com.tqhit.battery.one.ads.core  ApplovinInterstitialAdManager com.tqhit.battery.one.ads.core  ApplovinNativeAdManager com.tqhit.battery.one.ads.core  ApplovinRewardedAdManager com.tqhit.battery.one.ads.core  String com.tqhit.battery.one.ads.core  Unit com.tqhit.battery.one.ads.core  AnalyticsTracker 7com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager  FirebaseRemoteConfigHelper 7com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager  Inject 7com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager  String 7com.tqhit.battery.one.ads.core.ApplovinAppOpenAdManager  AnalyticsTracker 6com.tqhit.battery.one.ads.core.ApplovinBannerAdManager  Inject 6com.tqhit.battery.one.ads.core.ApplovinBannerAdManager  Activity <com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager  AnalyticsTracker <com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager  ApplovinRewardedAdManager <com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager  FirebaseRemoteConfigHelper <com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager  Inject <com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager  String <com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager  Unit <com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager  ApplicationContext 6com.tqhit.battery.one.ads.core.ApplovinNativeAdManager  Context 6com.tqhit.battery.one.ads.core.ApplovinNativeAdManager  Inject 6com.tqhit.battery.one.ads.core.ApplovinNativeAdManager  Activity 8com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager  AnalyticsTracker 8com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager  Inject 8com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager  String 8com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager  Unit 8com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager  Int (com.tqhit.battery.one.component.progress  AttributeSet <com.tqhit.battery.one.component.progress.VerticalProgressBar  Context <com.tqhit.battery.one.component.progress.VerticalProgressBar  Int <com.tqhit.battery.one.component.progress.VerticalProgressBar  ActivityAnimationBinding !com.tqhit.battery.one.databinding  ActivityChargingOverlayBinding !com.tqhit.battery.one.databinding  ActivityEnterPasswordBinding !com.tqhit.battery.one.databinding  ActivityMainBinding !com.tqhit.battery.one.databinding  ActivitySplashBinding !com.tqhit.battery.one.databinding  ActivityStartingBinding !com.tqhit.battery.one.databinding  !DialogBackgroundPermissionBinding !com.tqhit.battery.one.databinding  DialogChangeCapacityBinding !com.tqhit.battery.one.databinding  DialogLoadingBinding !com.tqhit.battery.one.databinding  DialogNotificationBinding !com.tqhit.battery.one.databinding  DialogSelectBatteryAlarmBinding !com.tqhit.battery.one.databinding  "DialogSelectBatteryAlarmLowBinding !com.tqhit.battery.one.databinding  DialogSelectColorThemeBinding !com.tqhit.battery.one.databinding  DialogSelectLanguageBinding !com.tqhit.battery.one.databinding  DialogSelectThemeBinding !com.tqhit.battery.one.databinding  DialogSetupPasswordBinding !com.tqhit.battery.one.databinding  FragmentAnimationGridBinding !com.tqhit.battery.one.databinding  FragmentChargeBinding !com.tqhit.battery.one.databinding  FragmentEmojiBatteryBinding !com.tqhit.battery.one.databinding  FragmentHealthBinding !com.tqhit.battery.one.databinding  FragmentSettingsBinding !com.tqhit.battery.one.databinding  NewFragmentDischargeBinding !com.tqhit.battery.one.databinding  inflate :com.tqhit.battery.one.databinding.ActivityAnimationBinding  inflate @com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding  inflate >com.tqhit.battery.one.databinding.ActivityEnterPasswordBinding  inflate 5com.tqhit.battery.one.databinding.ActivityMainBinding  inflate 7com.tqhit.battery.one.databinding.ActivitySplashBinding  inflate 9com.tqhit.battery.one.databinding.ActivityStartingBinding  inflate Acom.tqhit.battery.one.databinding.DialogSelectBatteryAlarmBinding  inflate Dcom.tqhit.battery.one.databinding.DialogSelectBatteryAlarmLowBinding  inflate >com.tqhit.battery.one.databinding.FragmentAnimationGridBinding  inflate 7com.tqhit.battery.one.databinding.FragmentChargeBinding  inflate 7com.tqhit.battery.one.databinding.FragmentHealthBinding  inflate 9com.tqhit.battery.one.databinding.FragmentSettingsBinding  inflate =com.tqhit.battery.one.databinding.NewFragmentDischargeBinding  Boolean "com.tqhit.battery.one.dialog.alarm  DialogSelectBatteryAlarmBinding "com.tqhit.battery.one.dialog.alarm  "DialogSelectBatteryAlarmLowBinding "com.tqhit.battery.one.dialog.alarm  SelectBatteryAlarmDialog "com.tqhit.battery.one.dialog.alarm  SelectBatteryAlarmLowDialog "com.tqhit.battery.one.dialog.alarm  String "com.tqhit.battery.one.dialog.alarm  getValue "com.tqhit.battery.one.dialog.alarm  lazy "com.tqhit.battery.one.dialog.alarm  provideDelegate "com.tqhit.battery.one.dialog.alarm  ActivityContext ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  ActivityResultLauncher ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  AppViewModel ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  Boolean ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  Context ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  DialogSelectBatteryAlarmBinding ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  Inject ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  String ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  VibrationService ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getGETValue ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getGetValue ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getLAYOUTInflater ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getLAZY ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getLayoutInflater ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getLazy ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getPROVIDEDelegate ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getProvideDelegate ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  getValue ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  layoutInflater ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  lazy ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  provideDelegate ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  setLayoutInflater ;com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog  ActivityContext >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  ActivityResultLauncher >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  AppViewModel >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  Boolean >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  Context >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  "DialogSelectBatteryAlarmLowBinding >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  Inject >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  String >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  VibrationService >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getGETValue >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getGetValue >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getLAYOUTInflater >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getLAZY >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getLayoutInflater >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getLazy >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getPROVIDEDelegate >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getProvideDelegate >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  getValue >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  layoutInflater >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  lazy >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  provideDelegate >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  setLayoutInflater >com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmLowDialog  String %com.tqhit.battery.one.dialog.capacity  Unit %com.tqhit.battery.one.dialog.capacity  ActivityContext :com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog  BatteryViewModel :com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog  Context :com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog  ActivityContext 9com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog  Context 9com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog  String 9com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog  Unit 9com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog  Activity :com.tqhit.battery.one.dialog.language.SelectLanguageDialog  AppViewModel :com.tqhit.battery.one.dialog.language.SelectLanguageDialog  BackgroundPermissionDialog 'com.tqhit.battery.one.dialog.permission  Runnable 'com.tqhit.battery.one.dialog.permission  Unit 'com.tqhit.battery.one.dialog.permission  ActivityContext Bcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog  Context Bcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog  Runnable Bcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog  Unit Bcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog  ActivityContext Lcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog.Companion  Context Lcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog.Companion  Runnable Lcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog.Companion  Unit Lcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog.Companion  Activity 4com.tqhit.battery.one.dialog.theme.SelectColorDialog  Activity 4com.tqhit.battery.one.dialog.theme.SelectThemeDialog  String "com.tqhit.battery.one.dialog.utils  Unit "com.tqhit.battery.one.dialog.utils  Context 0com.tqhit.battery.one.dialog.utils.LoadingDialog  String 0com.tqhit.battery.one.dialog.utils.LoadingDialog  ActivityContext 5com.tqhit.battery.one.dialog.utils.NotificationDialog  Context 5com.tqhit.battery.one.dialog.utils.NotificationDialog  String 5com.tqhit.battery.one.dialog.utils.NotificationDialog  Unit 5com.tqhit.battery.one.dialog.utils.NotificationDialog  BatteryStyleRepositoryImpl 4com.tqhit.battery.one.features.emoji.data.repository  Boolean 4com.tqhit.battery.one.features.emoji.data.repository  List 4com.tqhit.battery.one.features.emoji.data.repository  Long 4com.tqhit.battery.one.features.emoji.data.repository  String 4com.tqhit.battery.one.features.emoji.data.repository  ApplicationContext Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  BatteryStyle Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  BatteryStyleCategory Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  Boolean Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  Context Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  FirebaseRemoteConfigHelper Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  Gson Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  Inject Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  List Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  Long Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  	StateFlow Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  String Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  ApplicationContext Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  BatteryStyle Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  BatteryStyleCategory Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  Boolean Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  Context Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  FirebaseRemoteConfigHelper Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  Gson Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  Inject Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  List Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  Long Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  	StateFlow Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  String Ycom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl.Companion  EmojiBatteryDIModule 'com.tqhit.battery.one.features.emoji.di  SingletonComponent 'com.tqhit.battery.one.features.emoji.di  BatteryStyleRepository <com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule  BatteryStyleRepositoryImpl <com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule  Binds <com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule  	Singleton <com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModule  BatteryStyle 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleCategory 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleRepository 6com.tqhit.battery.one.features.emoji.domain.repository  Boolean 6com.tqhit.battery.one.features.emoji.domain.repository  List 6com.tqhit.battery.one.features.emoji.domain.repository  Long 6com.tqhit.battery.one.features.emoji.domain.repository  String 6com.tqhit.battery.one.features.emoji.domain.repository  BatteryStyle Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  BatteryStyleCategory Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  Boolean Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  Flow Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  List Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  Long Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  String Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  Boolean 4com.tqhit.battery.one.features.emoji.domain.use_case  GetBatteryStylesUseCase 4com.tqhit.battery.one.features.emoji.domain.use_case  List 4com.tqhit.battery.one.features.emoji.domain.use_case  String 4com.tqhit.battery.one.features.emoji.domain.use_case  BatteryStyle Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  BatteryStyleCategory Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  BatteryStyleRepository Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  Boolean Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  Flow Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  Inject Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  List Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  String Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  BatteryStyle Vcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion  BatteryStyleCategory Vcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion  BatteryStyleRepository Vcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion  Boolean Vcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion  Flow Vcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion  Inject Vcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion  List Vcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion  String Vcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase.Companion  BatteryGalleryEvent 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryState 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryViewModel 9com.tqhit.battery.one.features.emoji.presentation.gallery  Boolean 9com.tqhit.battery.one.features.emoji.presentation.gallery  EmojiBatteryFragment 9com.tqhit.battery.one.features.emoji.presentation.gallery  FlowPreview 9com.tqhit.battery.one.features.emoji.presentation.gallery  Int 9com.tqhit.battery.one.features.emoji.presentation.gallery  List 9com.tqhit.battery.one.features.emoji.presentation.gallery  OptIn 9com.tqhit.battery.one.features.emoji.presentation.gallery  String 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryEvent Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  BatteryGalleryState Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  BatteryStyle Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  BatteryStyleCategory Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  Boolean Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  CoreBatteryStatsProvider Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  GetBatteryStylesUseCase Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  Inject Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  Int Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  List Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  	StateFlow Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  String Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  BatteryGalleryEvent [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  BatteryGalleryState [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  BatteryStyle [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  BatteryStyleCategory [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  Boolean [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  CoreBatteryStatsProvider [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  GetBatteryStylesUseCase [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  Inject [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  Int [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  List [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  	StateFlow [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  String [com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel.Companion  BatteryGalleryViewModel Ncom.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment  BatteryStyleAdapter Ncom.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment  BatteryGalleryViewModel Xcom.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment.Companion  BatteryStyleAdapter Xcom.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment.Companion  BatteryStyleAdapter Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  BatteryStyleViewHolder Ucom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter  Boolean )com.tqhit.battery.one.features.navigation  DynamicNavigationManager )com.tqhit.battery.one.features.navigation  Int )com.tqhit.battery.one.features.navigation  Long )com.tqhit.battery.one.features.navigation  NavigationState )com.tqhit.battery.one.features.navigation  NavigationStateChange )com.tqhit.battery.one.features.navigation  StateChangeReason )com.tqhit.battery.one.features.navigation  String )com.tqhit.battery.one.features.navigation  Boolean Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  BottomNavigationView Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  CoreBatteryStatsProvider Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  Fragment Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  FragmentManager Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  Inject Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  Int Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  LifecycleOwner Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  Long Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  NavigationState Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  NavigationStateChange Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  StateChangeReason Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  	StateFlow Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  String Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  Boolean Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  BottomNavigationView Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  CoreBatteryStatsProvider Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  Fragment Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  FragmentManager Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  Inject Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  Int Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  LifecycleOwner Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  Long Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  NavigationState Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  NavigationStateChange Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  StateChangeReason Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  	StateFlow Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  String Lcom.tqhit.battery.one.features.navigation.DynamicNavigationManager.Companion  Boolean Dcom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer  Int Dcom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer  Long Dcom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer  Boolean Ncom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.Companion  Int Ncom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.Companion  Long Ncom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.Companion  Boolean Rcom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.FragmentState  Int Rcom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.FragmentState  Long Rcom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer.FragmentState  AppPowerConsumptionData 2com.tqhit.battery.one.features.stats.apppower.data  AppPowerConsumptionSummary 2com.tqhit.battery.one.features.stats.apppower.data  Boolean 2com.tqhit.battery.one.features.stats.apppower.data  Double 2com.tqhit.battery.one.features.stats.apppower.data  List 2com.tqhit.battery.one.features.stats.apppower.data  Long 2com.tqhit.battery.one.features.stats.apppower.data  String 2com.tqhit.battery.one.features.stats.apppower.data  Boolean Jcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData  Double Jcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData  Drawable Jcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData  Long Jcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData  String Jcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionData  AppPowerConsumptionData Mcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary  Double Mcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary  List Mcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary  Long Mcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary  String Mcom.tqhit.battery.one.features.stats.apppower.data.AppPowerConsumptionSummary  Boolean 8com.tqhit.battery.one.features.stats.apppower.permission  String 8com.tqhit.battery.one.features.stats.apppower.permission  Unit 8com.tqhit.battery.one.features.stats.apppower.permission  UsageStatsPermissionManager 8com.tqhit.battery.one.features.stats.apppower.permission  ActivityResultLauncher Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  AppUsageStatsRepository Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  ApplicationContext Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  Boolean Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  ComponentActivity Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  Context Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  Inject Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  Intent Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  String Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  Unit Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  ActivityResultLauncher ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  AppUsageStatsRepository ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  ApplicationContext ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  Boolean ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  ComponentActivity ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  Context ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  Inject ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  Intent ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  String ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  Unit ^com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager.Companion  AppPowerConsumptionAdapter :com.tqhit.battery.one.features.stats.apppower.presentation  AppPowerConsumptionDialog :com.tqhit.battery.one.features.stats.apppower.presentation   AppPowerConsumptionDialogFactory :com.tqhit.battery.one.features.stats.apppower.presentation  Int :com.tqhit.battery.one.features.stats.apppower.presentation  Long :com.tqhit.battery.one.features.stats.apppower.presentation  Runnable :com.tqhit.battery.one.features.stats.apppower.presentation  
AppViewHolder Ucom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter  	ImageView Ucom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter  RecyclerView Ucom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter  TextView Ucom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter  View Ucom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter  	ImageView ccom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppViewHolder  TextView ccom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppViewHolder  View ccom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter.AppViewHolder  AppUsageStatsRepository Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  Button Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  Context Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  	ImageView Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  Int Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  LinearLayout Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  Long Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  ProgressBar Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  RecyclerView Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  Runnable Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  TextView Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  UsageStatsPermissionManager Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  AppUsageStatsRepository ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  Button ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  Context ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  	ImageView ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  Int ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  LinearLayout ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  Long ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  ProgressBar ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  RecyclerView ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  Runnable ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  TextView ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  UsageStatsPermissionManager ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  AppPowerConsumptionDialog [com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory  AppUsageStatsRepository [com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory  Context [com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory  Inject [com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory  Int [com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory  Long [com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory  UsageStatsPermissionManager [com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory  AppUsageStatsRepository 8com.tqhit.battery.one.features.stats.apppower.repository  Boolean 8com.tqhit.battery.one.features.stats.apppower.repository  Double 8com.tqhit.battery.one.features.stats.apppower.repository  Int 8com.tqhit.battery.one.features.stats.apppower.repository  Long 8com.tqhit.battery.one.features.stats.apppower.repository  String 8com.tqhit.battery.one.features.stats.apppower.repository  AppPowerConsumptionData Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  AppPowerConsumptionSummary Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  ApplicationContext Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  ApplicationInfo Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  Boolean Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  Context Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  Double Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  Inject Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  Int Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  Long Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  String Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  
UsageStats Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  AppPowerConsumptionData Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  AppPowerConsumptionSummary Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  ApplicationContext Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  ApplicationInfo Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  Boolean Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  Context Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  Double Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  Inject Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  Int Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  Long Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  String Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  
UsageStats Zcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository.Companion  PrefsStatsChargeCache 1com.tqhit.battery.one.features.stats.charge.cache  StatsChargeCache 1com.tqhit.battery.one.features.stats.charge.cache  Gson Gcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache  Inject Gcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache  PreferencesHelper Gcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache  StatsChargeSession Gcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache  Gson Qcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache.Companion  Inject Qcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache.Companion  PreferencesHelper Qcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache.Companion  StatsChargeSession Qcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache.Companion  StatsChargeSession Bcom.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache  Boolean 0com.tqhit.battery.one.features.stats.charge.data  Double 0com.tqhit.battery.one.features.stats.charge.data  Int 0com.tqhit.battery.one.features.stats.charge.data  Long 0com.tqhit.battery.one.features.stats.charge.data  StatsChargeSession 0com.tqhit.battery.one.features.stats.charge.data  StatsChargeStatus 0com.tqhit.battery.one.features.stats.charge.data  Boolean Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  Double Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  Int Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  Long Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  Boolean Mcom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion  Double Mcom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion  Int Mcom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion  Long Mcom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion  SingletonComponent .com.tqhit.battery.one.features.stats.charge.di  StatsChargeDIModule .com.tqhit.battery.one.features.stats.charge.di  Binds Bcom.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule  DefaultStatsChargeRepository Bcom.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule  PrefsStatsChargeCache Bcom.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule  	Singleton Bcom.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule  StatsChargeCache Bcom.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule  StatsChargeRepository Bcom.tqhit.battery.one.features.stats.charge.di.StatsChargeDIModule  $CalculateSimpleChargeEstimateUseCase 2com.tqhit.battery.one.features.stats.charge.domain  Double 2com.tqhit.battery.one.features.stats.charge.domain  Int 2com.tqhit.battery.one.features.stats.charge.domain  Long 2com.tqhit.battery.one.features.stats.charge.domain  Double Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  Inject Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  Int Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  Long Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  StatsChargeSession Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  StatsChargeStatus Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  Double acom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase.Companion  Inject acom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase.Companion  Int acom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase.Companion  Long acom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase.Companion  StatsChargeSession acom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase.Companion  StatsChargeStatus acom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase.Companion  Boolean 8com.tqhit.battery.one.features.stats.charge.presentation  Double 8com.tqhit.battery.one.features.stats.charge.presentation  Float 8com.tqhit.battery.one.features.stats.charge.presentation  Int 8com.tqhit.battery.one.features.stats.charge.presentation  Long 8com.tqhit.battery.one.features.stats.charge.presentation  StatsChargeFragment 8com.tqhit.battery.one.features.stats.charge.presentation  StatsChargeUiState 8com.tqhit.battery.one.features.stats.charge.presentation  StatsChargeViewModel 8com.tqhit.battery.one.features.stats.charge.presentation  String 8com.tqhit.battery.one.features.stats.charge.presentation  AppViewModel Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  Bundle Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  Button Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  CircularProgressIndicator Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  Inject Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  LayoutInflater Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  SeekBar Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  StatsChargeUiState Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  StatsChargeViewModel Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  TextView Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  VibrationService Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  View Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  	ViewGroup Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  AppViewModel Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  Bundle Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  Button Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  CircularProgressIndicator Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  Inject Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  LayoutInflater Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  SeekBar Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  StatsChargeUiState Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  StatsChargeViewModel Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  TextView Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  VibrationService Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  View Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  	ViewGroup Vcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.Companion  
AppRepository Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  Boolean Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  $CalculateSimpleChargeEstimateUseCase Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  Double Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  Float Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  Inject Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  Int Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  Long Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  	StateFlow Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  StatsChargeRepository Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  StatsChargeSession Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  StatsChargeStatus Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  StatsChargeUiState Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  String Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  
AppRepository Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  Boolean Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  $CalculateSimpleChargeEstimateUseCase Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  Double Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  Float Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  Inject Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  Int Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  Long Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  	StateFlow Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  StatsChargeRepository Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  StatsChargeSession Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  StatsChargeStatus Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  StatsChargeUiState Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  String Wcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel.Companion  DefaultStatsChargeRepository 6com.tqhit.battery.one.features.stats.charge.repository  Double 6com.tqhit.battery.one.features.stats.charge.repository  Int 6com.tqhit.battery.one.features.stats.charge.repository  StatsChargeRepository 6com.tqhit.battery.one.features.stats.charge.repository  
AppRepository Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  CoreBatteryStatsProvider Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  CoreBatteryStatus Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  Double Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  Inject Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  Int Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  	StateFlow Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  StatsChargeCache Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  StatsChargeSession Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  StatsChargeStatus Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  
AppRepository ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  CoreBatteryStatsProvider ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  CoreBatteryStatus ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  Double ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  Inject ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  Int ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  	StateFlow ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  StatsChargeCache ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  StatsChargeSession ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  StatsChargeStatus ]com.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository.Companion  Flow Lcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository  StatsChargeSession Lcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository  StatsChargeStatus Lcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository  CoreBatteryStatus 5com.tqhit.battery.one.features.stats.corebattery.data  CoreBatteryDIModule 3com.tqhit.battery.one.features.stats.corebattery.di  SingletonComponent 3com.tqhit.battery.one.features.stats.corebattery.di  Binds Gcom.tqhit.battery.one.features.stats.corebattery.di.CoreBatteryDIModule  CoreBatteryStatsProvider Gcom.tqhit.battery.one.features.stats.corebattery.di.CoreBatteryDIModule  DefaultCoreBatteryStatsProvider Gcom.tqhit.battery.one.features.stats.corebattery.di.CoreBatteryDIModule  	Singleton Gcom.tqhit.battery.one.features.stats.corebattery.di.CoreBatteryDIModule  CoreBatteryStatsProvider 7com.tqhit.battery.one.features.stats.corebattery.domain  DefaultCoreBatteryStatsProvider 7com.tqhit.battery.one.features.stats.corebattery.domain  CoreBatteryStatus Pcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider  	StateFlow Pcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider  CoreBatteryStatus Wcom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider  Inject Wcom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider  	StateFlow Wcom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider  CoreBatteryStatus acom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider.Companion  Inject acom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider.Companion  	StateFlow acom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider.Companion  Boolean 8com.tqhit.battery.one.features.stats.corebattery.service  CoreBatteryServiceHelper 8com.tqhit.battery.one.features.stats.corebattery.service  CoreBatteryStatsService 8com.tqhit.battery.one.features.stats.corebattery.service  Float 8com.tqhit.battery.one.features.stats.corebattery.service  Int 8com.tqhit.battery.one.features.stats.corebattery.service  Long 8com.tqhit.battery.one.features.stats.corebattery.service  String 8com.tqhit.battery.one.features.stats.corebattery.service  ApplicationContext Qcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper  Boolean Qcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper  Context Qcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper  Inject Qcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper  ApplicationContext [com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper.Companion  Boolean [com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper.Companion  Context [com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper.Companion  Inject [com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper.Companion  
AppRepository Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  BatteryManager Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  Boolean Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  BroadcastReceiver Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  CoreBatteryStatsProvider Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  CoreBatteryStatsService Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  CoreBatteryStatus Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  Float Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  IBinder Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  Inject Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  Int Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  Intent Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  Long Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  Notification Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  String Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  
AppRepository Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  BatteryManager Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  Boolean Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  BroadcastReceiver Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  CoreBatteryStatsProvider Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  CoreBatteryStatsService Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  CoreBatteryStatus Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  Float Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  IBinder Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  Inject Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  Int Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  Intent Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  Long Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  Notification Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  String Zcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.Companion  Context 4com.tqhit.battery.one.features.stats.discharge.cache  CurrentSessionCache 4com.tqhit.battery.one.features.stats.discharge.cache  DischargeRatesCache 4com.tqhit.battery.one.features.stats.discharge.cache  Dispatchers 4com.tqhit.battery.one.features.stats.discharge.cache  Double 4com.tqhit.battery.one.features.stats.discharge.cache  KEY_CURRENT_SESSION 4com.tqhit.battery.one.features.stats.discharge.cache  KEY_SCREEN_OFF_RATE 4com.tqhit.battery.one.features.stats.discharge.cache  KEY_SCREEN_ON_RATE 4com.tqhit.battery.one.features.stats.discharge.cache  
PREFS_NAME 4com.tqhit.battery.one.features.stats.discharge.cache  PrefsCurrentSessionCache 4com.tqhit.battery.one.features.stats.discharge.cache  PrefsDischargeRatesCache 4com.tqhit.battery.one.features.stats.discharge.cache  gson 4com.tqhit.battery.one.features.stats.discharge.cache  prefs 4com.tqhit.battery.one.features.stats.discharge.cache  withContext 4com.tqhit.battery.one.features.stats.discharge.cache  DischargeSessionData Hcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache  Double Hcom.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache  ApplicationContext Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  Context Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  DischargeSessionData Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  Dispatchers Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  Gson Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  Inject Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  KEY_CURRENT_SESSION Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  
PREFS_NAME Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  context Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  getWITHContext Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  getWithContext Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  gson Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  prefs Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  withContext Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  ApplicationContext Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  Context Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  DischargeSessionData Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  Dispatchers Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  Gson Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  Inject Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  KEY_CURRENT_SESSION Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  
PREFS_NAME Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  getWITHContext Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  getWithContext Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  gson Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  prefs Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  withContext Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache.Companion  ApplicationContext Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  Context Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  Dispatchers Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  Double Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  Inject Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  KEY_SCREEN_OFF_RATE Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  KEY_SCREEN_ON_RATE Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  
PREFS_NAME Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  context Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  getWITHContext Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  getWithContext Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  prefs Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  withContext Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  ApplicationContext Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  Context Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  Dispatchers Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  Double Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  Inject Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  KEY_SCREEN_OFF_RATE Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  KEY_SCREEN_ON_RATE Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  
PREFS_NAME Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  getWITHContext Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  getWithContext Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  prefs Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  withContext Wcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache.Companion  Boolean 3com.tqhit.battery.one.features.stats.discharge.data  DischargeSessionData 3com.tqhit.battery.one.features.stats.discharge.data  Double 3com.tqhit.battery.one.features.stats.discharge.data  Int 3com.tqhit.battery.one.features.stats.discharge.data  Long 3com.tqhit.battery.one.features.stats.discharge.data  ScreenStateChangeEvent 3com.tqhit.battery.one.features.stats.discharge.data  Boolean Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  Double Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  Int Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  Long Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  Boolean 9com.tqhit.battery.one.features.stats.discharge.datasource  ScreenStateReceiver 9com.tqhit.battery.one.features.stats.discharge.datasource  ApplicationContext Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  Boolean Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  Context Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  Inject Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  ScreenStateChangeEvent Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  
SharedFlow Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  ApplicationContext Wcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver.Companion  Boolean Wcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver.Companion  Context Wcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver.Companion  Inject Wcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver.Companion  ScreenStateChangeEvent Wcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver.Companion  
SharedFlow Wcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver.Companion  SingletonComponent 1com.tqhit.battery.one.features.stats.discharge.di  StatsDischargeModule 1com.tqhit.battery.one.features.stats.discharge.di  StatsDischargeProvidersModule 1com.tqhit.battery.one.features.stats.discharge.di  Binds Fcom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule  CurrentSessionCache Fcom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule  DischargeRatesCache Fcom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule  PrefsCurrentSessionCache Fcom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule  PrefsDischargeRatesCache Fcom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule  	Singleton Fcom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeModule  ApplicationContext Ocom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule  Context Ocom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule  Provides Ocom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule  ScreenStateReceiver Ocom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule  	Singleton Ocom.tqhit.battery.one.features.stats.discharge.di.StatsDischargeProvidersModule  AppLifecycleManager 5com.tqhit.battery.one.features.stats.discharge.domain  AppState 5com.tqhit.battery.one.features.stats.discharge.domain  Boolean 5com.tqhit.battery.one.features.stats.discharge.domain  CorrectionStrategy 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeRateCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  Double 5com.tqhit.battery.one.features.stats.discharge.domain  FullSessionReEstimator 5com.tqhit.battery.one.features.stats.discharge.domain  GapEstimationCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  Int 5com.tqhit.battery.one.features.stats.discharge.domain  Long 5com.tqhit.battery.one.features.stats.discharge.domain  Pair 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenStateTimeTracker 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeValidationService 5com.tqhit.battery.one.features.stats.discharge.domain  SessionManager 5com.tqhit.battery.one.features.stats.discharge.domain  SessionMetricsCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  String 5com.tqhit.battery.one.features.stats.discharge.domain  
TimeConverter 5com.tqhit.battery.one.features.stats.discharge.domain  ValidationResult 5com.tqhit.battery.one.features.stats.discharge.domain  AppState Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  Boolean Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  Inject Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  LifecycleOwner Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  Long Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  	StateFlow Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  String Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  AppState Scom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager.Companion  Boolean Scom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager.Companion  Inject Scom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager.Companion  LifecycleOwner Scom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager.Companion  Long Scom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager.Companion  	StateFlow Scom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager.Companion  String Scom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager.Companion  Boolean Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  Double Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  Inject Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  Int Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  Long Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  Boolean Scom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion  Double Scom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion  Inject Scom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion  Int Scom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion  Long Scom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion  Boolean Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  ConsumptionByState Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  CoreBatteryStatus Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  DischargeSessionData Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  Double Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  Inject Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  Int Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  Long Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  SessionRates Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  
TimeConverter Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  CoreBatteryStatus Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  DischargeRateCalculator Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  DischargeRatesCache Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  DischargeSessionData Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  Double Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  Inject Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  ScreenTimeCalculator Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  
TimeConverter Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  CoreBatteryStatus Vcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion  DischargeRateCalculator Vcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion  DischargeRatesCache Vcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion  DischargeSessionData Vcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion  Double Vcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion  Inject Vcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion  ScreenTimeCalculator Vcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion  
TimeConverter Vcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator.Companion  CoreBatteryStatus Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  DischargeRateCalculator Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  DischargeRatesCache Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  DischargeSessionData Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  Double Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  Inject Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  ScreenTimeCalculator Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  SessionMetricsCalculator Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  
TimeConverter Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  CoreBatteryStatus Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  DischargeRateCalculator Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  DischargeRatesCache Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  DischargeSessionData Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  Double Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  Inject Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  ScreenTimeCalculator Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  SessionMetricsCalculator Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  
TimeConverter Wcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator.Companion  Boolean Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  Int Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  Long Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  Pair Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  	StateFlow Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  incrementCurrentState Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  Boolean Vcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker.Companion  Int Vcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker.Companion  Long Vcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker.Companion  Pair Vcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker.Companion  	StateFlow Vcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker.Companion  invoke Vcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker.Companion  BatteryRates Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  Boolean Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  DischargeSessionData Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  Double Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  Inject Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  Long Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  Pair Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  ScreenTimeDeltas Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  ScreenTimes Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  
TimeConverter Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  AppLifecycleManager Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  Boolean Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  CorrectionStrategy Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  DischargeSessionData Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  Inject Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  Long Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  String Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  ValidationResult Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  AppLifecycleManager [com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion  Boolean [com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion  CorrectionStrategy [com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion  DischargeSessionData [com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion  Inject [com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion  Long [com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion  String [com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion  ValidationResult [com.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService.Companion  Boolean Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  CoreBatteryStatus Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  DischargeCalculator Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  DischargeRateCalculator Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  DischargeSessionData Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  Double Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  Inject Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  Long Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  ScreenTimeCalculator Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  SessionMetricsCalculator Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  Boolean Ncom.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator  CoreBatteryStatus Ncom.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator  DischargeRateCalculator Ncom.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator  DischargeSessionData Ncom.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator  Double Ncom.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator  Inject Ncom.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator  SessionMetrics Ncom.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator  
TimeConverter Ncom.tqhit.battery.one.features.stats.discharge.domain.SessionMetricsCalculator  Double Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  Inject Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  Long Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  String Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  AnimationHelper ;com.tqhit.battery.one.features.stats.discharge.presentation  Boolean ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeFragment ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeUiState ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeUiUpdater ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeViewModel ;com.tqhit.battery.one.features.stats.discharge.presentation  InfoButtonManager ;com.tqhit.battery.one.features.stats.discharge.presentation  Int ;com.tqhit.battery.one.features.stats.discharge.presentation  	StateFlow ;com.tqhit.battery.one.features.stats.discharge.presentation  Unit ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeUiUpdater Kcom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper  Int Kcom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper  NewFragmentDischargeBinding Kcom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper  
ValueAnimator Kcom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper  DischargeUiUpdater Ucom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper.Companion  Int Ucom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper.Companion  NewFragmentDischargeBinding Ucom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper.Companion  
ValueAnimator Ucom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper.Companion  AnimationHelper Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  AppLifecycleManager Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  Boolean Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  Bundle Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  DischargeUiUpdater Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  DischargeViewModel Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  InfoButtonManager Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  Inject Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  LayoutInflater Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  NewFragmentDischargeBinding Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  PowerManager Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  
TimeConverter Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  View Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  	ViewGroup Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  AnimationHelper Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  AppLifecycleManager Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  Boolean Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  Bundle Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  DischargeUiUpdater Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  DischargeViewModel Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  InfoButtonManager Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  Inject Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  LayoutInflater Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  NewFragmentDischargeBinding Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  PowerManager Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  
TimeConverter Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  View Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  	ViewGroup Wcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment.Companion  Context Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater  DischargeUiState Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater  NewFragmentDischargeBinding Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater  
TimeConverter Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater  Context Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater.Companion  DischargeUiState Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater.Companion  NewFragmentDischargeBinding Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater.Companion  
TimeConverter Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater.Companion  BatteryRepository Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  DischargeCalculator Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  DischargeSessionRepository Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  DischargeUiState Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  #EnhancedDischargeTimerServiceHelper Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  Inject Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  	StateFlow Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  
TimeConverter Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  BatteryRepository Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion  DischargeCalculator Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion  DischargeSessionRepository Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion  DischargeUiState Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion  #EnhancedDischargeTimerServiceHelper Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion  Inject Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion  	StateFlow Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion  
TimeConverter Xcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel.Companion  ActivityContext Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager   AppPowerConsumptionDialogFactory Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  Context Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  DischargeSessionData Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  Inject Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  Int Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  NewFragmentDischargeBinding Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  Unit Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  UsageStatsPermissionManager Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  ActivityContext Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion   AppPowerConsumptionDialogFactory Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion  Context Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion  DischargeSessionData Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion  Inject Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion  Int Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion  NewFragmentDischargeBinding Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion  Unit Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion  UsageStatsPermissionManager Wcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager.Companion  BatteryRepository 9com.tqhit.battery.one.features.stats.discharge.repository  Boolean 9com.tqhit.battery.one.features.stats.discharge.repository  DischargeSessionRepository 9com.tqhit.battery.one.features.stats.discharge.repository  Double 9com.tqhit.battery.one.features.stats.discharge.repository  Flow 9com.tqhit.battery.one.features.stats.discharge.repository  Int 9com.tqhit.battery.one.features.stats.discharge.repository  Long 9com.tqhit.battery.one.features.stats.discharge.repository  MutableStateFlow 9com.tqhit.battery.one.features.stats.discharge.repository  Pair 9com.tqhit.battery.one.features.stats.discharge.repository  ScreenStateTimeTracker 9com.tqhit.battery.one.features.stats.discharge.repository  	StateFlow 9com.tqhit.battery.one.features.stats.discharge.repository  Unit 9com.tqhit.battery.one.features.stats.discharge.repository  delay 9com.tqhit.battery.one.features.stats.discharge.repository  distinctUntilChanged 9com.tqhit.battery.one.features.stats.discharge.repository  flow 9com.tqhit.battery.one.features.stats.discharge.repository  screenStateTimeTracker 9com.tqhit.battery.one.features.stats.discharge.repository  
AppRepository Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  ApplicationContext Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  ConcurrentLinkedQueue Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  Context Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  CoreBatteryStatsProvider Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  CoreBatteryStatus Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  DischargeRatesCache Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  DischargeSessionRepository Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  Double Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  Flow Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  Inject Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  Int Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  MutableStateFlow Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  	StateFlow Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  Unit Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  
AppRepository Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  ApplicationContext Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  ConcurrentLinkedQueue Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  Context Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  CoreBatteryStatsProvider Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  CoreBatteryStatus Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  DischargeRatesCache Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  DischargeSessionRepository Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  Double Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  Flow Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  Inject Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  Int Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  MutableStateFlow Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  	StateFlow Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  Unit Ucom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository.Companion  ApplicationContext Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  Boolean Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  Context Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  CoreBatteryStatus Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  CurrentSessionCache Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  DischargeSessionData Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  FullSessionReEstimator Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  GapEstimationCalculator Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  Inject Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  Long Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  Pair Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  ScreenStateChangeEvent Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  ScreenStateReceiver Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  ScreenStateTimeTracker Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  SessionManager Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  SessionMetricsCalculator Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  	StateFlow Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  delay Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  distinctUntilChanged Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  flow Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  getDELAY Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  getDISTINCTUntilChanged Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  getDelay Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  getDistinctUntilChanged Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  getFLOW Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  getFlow Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  invoke Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  screenStateTimeTracker Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  ApplicationContext ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  Boolean ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  Context ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  CoreBatteryStatus ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  CurrentSessionCache ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  DischargeSessionData ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  FullSessionReEstimator ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  GapEstimationCalculator ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  Inject ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  Long ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  Pair ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  ScreenStateChangeEvent ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  ScreenStateReceiver ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  ScreenStateTimeTracker ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  SessionManager ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  SessionMetricsCalculator ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  	StateFlow ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  delay ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  distinctUntilChanged ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  flow ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  getDELAY ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  getDISTINCTUntilChanged ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  getDelay ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  getDistinctUntilChanged ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  getFLOW ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  getFlow ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  invoke ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  screenStateTimeTracker ^com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository.Companion  Boolean 6com.tqhit.battery.one.features.stats.discharge.service  EnhancedDischargeTimerService 6com.tqhit.battery.one.features.stats.discharge.service  #EnhancedDischargeTimerServiceHelper 6com.tqhit.battery.one.features.stats.discharge.service  Int 6com.tqhit.battery.one.features.stats.discharge.service  Long 6com.tqhit.battery.one.features.stats.discharge.service  String 6com.tqhit.battery.one.features.stats.discharge.service  AppLifecycleManager Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  DischargeSessionData Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  DischargeSessionRepository Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  IBinder Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  Inject Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  Int Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  Intent Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  Long Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  Notification Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  PowerManager Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  ScreenTimeValidationService Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  AppLifecycleManager ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  DischargeSessionData ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  DischargeSessionRepository ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  IBinder ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  Inject ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  Int ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  Intent ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  Long ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  Notification ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  PowerManager ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  ScreenTimeValidationService ^com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService.Companion  ApplicationContext Zcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper  Boolean Zcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper  Context Zcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper  Inject Zcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper  String Zcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper  ApplicationContext dcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper.Companion  Boolean dcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper.Companion  Context dcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper.Companion  Inject dcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper.Companion  String dcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper.Companion  DefaultHealthCache 1com.tqhit.battery.one.features.stats.health.cache  HealthCache 1com.tqhit.battery.one.features.stats.health.cache  HealthCalculationMode Dcom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache  HealthStatus Dcom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache  Inject Dcom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache  PreferencesHelper Dcom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache  HealthCalculationMode Ncom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache.Companion  HealthStatus Ncom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache.Companion  Inject Ncom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache.Companion  PreferencesHelper Ncom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache.Companion  HealthCalculationMode =com.tqhit.battery.one.features.stats.health.cache.HealthCache  HealthStatus =com.tqhit.battery.one.features.stats.health.cache.HealthCache  HealthCalculationMode 0com.tqhit.battery.one.features.stats.health.data  HealthChartData 0com.tqhit.battery.one.features.stats.health.data  HealthStatus 0com.tqhit.battery.one.features.stats.health.data  HealthDIModule .com.tqhit.battery.one.features.stats.health.di  SingletonComponent .com.tqhit.battery.one.features.stats.health.di  Binds =com.tqhit.battery.one.features.stats.health.di.HealthDIModule  DefaultHealthCache =com.tqhit.battery.one.features.stats.health.di.HealthDIModule  DefaultHealthRepository =com.tqhit.battery.one.features.stats.health.di.HealthDIModule  HealthCache =com.tqhit.battery.one.features.stats.health.di.HealthDIModule  HealthRepository =com.tqhit.battery.one.features.stats.health.di.HealthDIModule  	Singleton =com.tqhit.battery.one.features.stats.health.di.HealthDIModule  Boolean 2com.tqhit.battery.one.features.stats.health.domain  CalculateBatteryHealthUseCase 2com.tqhit.battery.one.features.stats.health.domain  Double 2com.tqhit.battery.one.features.stats.health.domain  GetHealthHistoryUseCase 2com.tqhit.battery.one.features.stats.health.domain  Int 2com.tqhit.battery.one.features.stats.health.domain  List 2com.tqhit.battery.one.features.stats.health.domain  Boolean Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  Double Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  HealthCalculationMode Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  HealthStatus Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  Inject Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  Int Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  Boolean Zcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase.Companion  Double Zcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase.Companion  HealthCalculationMode Zcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase.Companion  HealthStatus Zcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase.Companion  Inject Zcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase.Companion  Int Zcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase.Companion  BatteryRepository Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  Boolean Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  Double Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  Entry Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  HealthChartData Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  Inject Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  Int Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  List Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  BatteryRepository Tcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion  Boolean Tcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion  Double Tcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion  Entry Tcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion  HealthChartData Tcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion  Inject Tcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion  Int Tcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion  List Tcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase.Companion  Boolean 8com.tqhit.battery.one.features.stats.health.presentation  
HealthUiState 8com.tqhit.battery.one.features.stats.health.presentation  HealthViewModel 8com.tqhit.battery.one.features.stats.health.presentation  Int 8com.tqhit.battery.one.features.stats.health.presentation  List 8com.tqhit.battery.one.features.stats.health.presentation  String 8com.tqhit.battery.one.features.stats.health.presentation  Boolean Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  HealthCalculationMode Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  HealthChartData Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  HealthRepository Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  HealthStatus Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  
HealthUiState Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  Inject Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  Int Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  List Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  	StateFlow Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  String Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  Boolean Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  HealthCalculationMode Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  HealthChartData Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  HealthRepository Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  HealthStatus Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  
HealthUiState Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  Inject Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  Int Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  List Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  	StateFlow Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  String Rcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel.Companion  DefaultHealthRepository 6com.tqhit.battery.one.features.stats.health.repository  
Deprecated 6com.tqhit.battery.one.features.stats.health.repository  Double 6com.tqhit.battery.one.features.stats.health.repository  HealthRepository 6com.tqhit.battery.one.features.stats.health.repository  HistoryBatteryRepository 6com.tqhit.battery.one.features.stats.health.repository  Int 6com.tqhit.battery.one.features.stats.health.repository  List 6com.tqhit.battery.one.features.stats.health.repository  Long 6com.tqhit.battery.one.features.stats.health.repository  String 6com.tqhit.battery.one.features.stats.health.repository  
AppRepository Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  ChargingSessionManager Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  CoreBatteryStatsProvider Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  
Deprecated Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  Double Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  HealthCache Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  HealthCalculationMode Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  HealthChartData Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  HealthStatus Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  HistoryBatteryRepository Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  Inject Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  Int Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  List Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  	StateFlow Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  
AppRepository Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  ChargingSessionManager Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  CoreBatteryStatsProvider Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  
Deprecated Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  Double Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  HealthCache Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  HealthCalculationMode Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  HealthChartData Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  HealthStatus Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  HistoryBatteryRepository Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  Inject Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  Int Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  List Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  	StateFlow Xcom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository.Companion  Flow Gcom.tqhit.battery.one.features.stats.health.repository.HealthRepository  HealthCalculationMode Gcom.tqhit.battery.one.features.stats.health.repository.HealthRepository  HealthChartData Gcom.tqhit.battery.one.features.stats.health.repository.HealthRepository  HealthStatus Gcom.tqhit.battery.one.features.stats.health.repository.HealthRepository  Int Gcom.tqhit.battery.one.features.stats.health.repository.HealthRepository  CoreBatteryStatsProvider Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  Double Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  HistoryEntry Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  Inject Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  Int Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  List Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  Long Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  PreferencesHelper Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  String Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  CoreBatteryStatsProvider Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  Double Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  HistoryEntry Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  Inject Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  Int Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  List Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  Long Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  PreferencesHelper Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  String Ycom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository.Companion  Boolean 2com.tqhit.battery.one.features.stats.notifications  Int 2com.tqhit.battery.one.features.stats.notifications  Runnable 2com.tqhit.battery.one.features.stats.notifications  String 2com.tqhit.battery.one.features.stats.notifications  !UnifiedBatteryNotificationService 2com.tqhit.battery.one.features.stats.notifications  'UnifiedBatteryNotificationServiceHelper 2com.tqhit.battery.one.features.stats.notifications  android 2com.tqhit.battery.one.features.stats.notifications  
AppRepository Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  BroadcastReceiver Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  CoreBatteryStatsProvider Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  CoreBatteryStatus Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  IBinder Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  Inject Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  Int Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  Intent Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  Notification Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  Runnable Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  String Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  android Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  
AppRepository ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  BroadcastReceiver ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  CoreBatteryStatsProvider ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  CoreBatteryStatus ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  IBinder ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  Inject ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  Int ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  Intent ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  Notification ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  Runnable ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  String ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  android ^com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService.Companion  ApplicationContext Zcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper  Boolean Zcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper  Context Zcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper  Inject Zcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper  ApplicationContext dcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper.Companion  Boolean dcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper.Companion  Context dcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper.Companion  Inject dcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper.Companion  Boolean #com.tqhit.battery.one.fragment.main  ChargeFragment #com.tqhit.battery.one.fragment.main  
Deprecated #com.tqhit.battery.one.fragment.main  DischargeFragment #com.tqhit.battery.one.fragment.main  Double #com.tqhit.battery.one.fragment.main  Float #com.tqhit.battery.one.fragment.main  FragmentChargeBinding #com.tqhit.battery.one.fragment.main  FragmentHealthBinding #com.tqhit.battery.one.fragment.main  FragmentSettingsBinding #com.tqhit.battery.one.fragment.main  HealthFragment #com.tqhit.battery.one.fragment.main  HistoryType #com.tqhit.battery.one.fragment.main  Int #com.tqhit.battery.one.fragment.main  List #com.tqhit.battery.one.fragment.main  Log #com.tqhit.battery.one.fragment.main  Long #com.tqhit.battery.one.fragment.main  NewFragmentDischargeBinding #com.tqhit.battery.one.fragment.main  Number #com.tqhit.battery.one.fragment.main  Pair #com.tqhit.battery.one.fragment.main  ReplaceWith #com.tqhit.battery.one.fragment.main  SettingsFragment #com.tqhit.battery.one.fragment.main  String #com.tqhit.battery.one.fragment.main  TAG #com.tqhit.battery.one.fragment.main  android #com.tqhit.battery.one.fragment.main  com #com.tqhit.battery.one.fragment.main  getValue #com.tqhit.battery.one.fragment.main  lazy #com.tqhit.battery.one.fragment.main  provideDelegate #com.tqhit.battery.one.fragment.main  AppViewModel 2com.tqhit.battery.one.fragment.main.ChargeFragment  ApplovinInterstitialAdManager 2com.tqhit.battery.one.fragment.main.ChargeFragment  BatteryViewModel 2com.tqhit.battery.one.fragment.main.ChargeFragment  Boolean 2com.tqhit.battery.one.fragment.main.ChargeFragment  FragmentChargeBinding 2com.tqhit.battery.one.fragment.main.ChargeFragment  Inject 2com.tqhit.battery.one.fragment.main.ChargeFragment  Int 2com.tqhit.battery.one.fragment.main.ChargeFragment  VibrationService 2com.tqhit.battery.one.fragment.main.ChargeFragment  getGETValue 2com.tqhit.battery.one.fragment.main.ChargeFragment  getGetValue 2com.tqhit.battery.one.fragment.main.ChargeFragment  getLAYOUTInflater 2com.tqhit.battery.one.fragment.main.ChargeFragment  getLAZY 2com.tqhit.battery.one.fragment.main.ChargeFragment  getLayoutInflater 2com.tqhit.battery.one.fragment.main.ChargeFragment  getLazy 2com.tqhit.battery.one.fragment.main.ChargeFragment  getPROVIDEDelegate 2com.tqhit.battery.one.fragment.main.ChargeFragment  getProvideDelegate 2com.tqhit.battery.one.fragment.main.ChargeFragment  getValue 2com.tqhit.battery.one.fragment.main.ChargeFragment  layoutInflater 2com.tqhit.battery.one.fragment.main.ChargeFragment  lazy 2com.tqhit.battery.one.fragment.main.ChargeFragment  provideDelegate 2com.tqhit.battery.one.fragment.main.ChargeFragment  setLayoutInflater 2com.tqhit.battery.one.fragment.main.ChargeFragment  AppViewModel 5com.tqhit.battery.one.fragment.main.DischargeFragment  ApplovinInterstitialAdManager 5com.tqhit.battery.one.fragment.main.DischargeFragment  BatteryViewModel 5com.tqhit.battery.one.fragment.main.DischargeFragment  Inject 5com.tqhit.battery.one.fragment.main.DischargeFragment  Int 5com.tqhit.battery.one.fragment.main.DischargeFragment  Log 5com.tqhit.battery.one.fragment.main.DischargeFragment  NewFragmentDischargeBinding 5com.tqhit.battery.one.fragment.main.DischargeFragment  TAG 5com.tqhit.battery.one.fragment.main.DischargeFragment  VibrationService 5com.tqhit.battery.one.fragment.main.DischargeFragment  android 5com.tqhit.battery.one.fragment.main.DischargeFragment  getGETValue 5com.tqhit.battery.one.fragment.main.DischargeFragment  getGetValue 5com.tqhit.battery.one.fragment.main.DischargeFragment  getLAYOUTInflater 5com.tqhit.battery.one.fragment.main.DischargeFragment  getLAZY 5com.tqhit.battery.one.fragment.main.DischargeFragment  getLayoutInflater 5com.tqhit.battery.one.fragment.main.DischargeFragment  getLazy 5com.tqhit.battery.one.fragment.main.DischargeFragment  getPROVIDEDelegate 5com.tqhit.battery.one.fragment.main.DischargeFragment  getProvideDelegate 5com.tqhit.battery.one.fragment.main.DischargeFragment  getValue 5com.tqhit.battery.one.fragment.main.DischargeFragment  layoutInflater 5com.tqhit.battery.one.fragment.main.DischargeFragment  lazy 5com.tqhit.battery.one.fragment.main.DischargeFragment  provideDelegate 5com.tqhit.battery.one.fragment.main.DischargeFragment  setLayoutInflater 5com.tqhit.battery.one.fragment.main.DischargeFragment  AppViewModel ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  ApplovinInterstitialAdManager ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  BatteryViewModel ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  Inject ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  Int ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  Log ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  NewFragmentDischargeBinding ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  TAG ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  VibrationService ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  android ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  getGETValue ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  getGetValue ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  getLAZY ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  getLazy ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  getPROVIDEDelegate ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  getProvideDelegate ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  getValue ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  lazy ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  provideDelegate ?com.tqhit.battery.one.fragment.main.DischargeFragment.Companion  ApplovinInterstitialAdManager 2com.tqhit.battery.one.fragment.main.HealthFragment  BatteryViewModel 2com.tqhit.battery.one.fragment.main.HealthFragment  Double 2com.tqhit.battery.one.fragment.main.HealthFragment  Entry 2com.tqhit.battery.one.fragment.main.HealthFragment  Float 2com.tqhit.battery.one.fragment.main.HealthFragment  FragmentHealthBinding 2com.tqhit.battery.one.fragment.main.HealthFragment  HealthCalculationMode 2com.tqhit.battery.one.fragment.main.HealthFragment  HealthViewModel 2com.tqhit.battery.one.fragment.main.HealthFragment  HistoryType 2com.tqhit.battery.one.fragment.main.HealthFragment  Inject 2com.tqhit.battery.one.fragment.main.HealthFragment  Int 2com.tqhit.battery.one.fragment.main.HealthFragment  Job 2com.tqhit.battery.one.fragment.main.HealthFragment  LineDataSet 2com.tqhit.battery.one.fragment.main.HealthFragment  List 2com.tqhit.battery.one.fragment.main.HealthFragment  Long 2com.tqhit.battery.one.fragment.main.HealthFragment  Number 2com.tqhit.battery.one.fragment.main.HealthFragment  Pair 2com.tqhit.battery.one.fragment.main.HealthFragment  String 2com.tqhit.battery.one.fragment.main.HealthFragment  android 2com.tqhit.battery.one.fragment.main.HealthFragment  com 2com.tqhit.battery.one.fragment.main.HealthFragment  getGETValue 2com.tqhit.battery.one.fragment.main.HealthFragment  getGetValue 2com.tqhit.battery.one.fragment.main.HealthFragment  getLAYOUTInflater 2com.tqhit.battery.one.fragment.main.HealthFragment  getLAZY 2com.tqhit.battery.one.fragment.main.HealthFragment  getLayoutInflater 2com.tqhit.battery.one.fragment.main.HealthFragment  getLazy 2com.tqhit.battery.one.fragment.main.HealthFragment  getPROVIDEDelegate 2com.tqhit.battery.one.fragment.main.HealthFragment  getProvideDelegate 2com.tqhit.battery.one.fragment.main.HealthFragment  getValue 2com.tqhit.battery.one.fragment.main.HealthFragment  layoutInflater 2com.tqhit.battery.one.fragment.main.HealthFragment  lazy 2com.tqhit.battery.one.fragment.main.HealthFragment  provideDelegate 2com.tqhit.battery.one.fragment.main.HealthFragment  setLayoutInflater 2com.tqhit.battery.one.fragment.main.HealthFragment  AppViewModel 4com.tqhit.battery.one.fragment.main.SettingsFragment  ApplovinInterstitialAdManager 4com.tqhit.battery.one.fragment.main.SettingsFragment  BatteryViewModel 4com.tqhit.battery.one.fragment.main.SettingsFragment  FragmentSettingsBinding 4com.tqhit.battery.one.fragment.main.SettingsFragment  Inject 4com.tqhit.battery.one.fragment.main.SettingsFragment  PreferencesHelper 4com.tqhit.battery.one.fragment.main.SettingsFragment  getGETValue 4com.tqhit.battery.one.fragment.main.SettingsFragment  getGetValue 4com.tqhit.battery.one.fragment.main.SettingsFragment  getLAYOUTInflater 4com.tqhit.battery.one.fragment.main.SettingsFragment  getLAZY 4com.tqhit.battery.one.fragment.main.SettingsFragment  getLayoutInflater 4com.tqhit.battery.one.fragment.main.SettingsFragment  getLazy 4com.tqhit.battery.one.fragment.main.SettingsFragment  getPROVIDEDelegate 4com.tqhit.battery.one.fragment.main.SettingsFragment  getProvideDelegate 4com.tqhit.battery.one.fragment.main.SettingsFragment  getValue 4com.tqhit.battery.one.fragment.main.SettingsFragment  layoutInflater 4com.tqhit.battery.one.fragment.main.SettingsFragment  lazy 4com.tqhit.battery.one.fragment.main.SettingsFragment  provideDelegate 4com.tqhit.battery.one.fragment.main.SettingsFragment  setLayoutInflater 4com.tqhit.battery.one.fragment.main.SettingsFragment  AnimationGridFragment -com.tqhit.battery.one.fragment.main.animation  FragmentAnimationGridBinding -com.tqhit.battery.one.fragment.main.animation  Int -com.tqhit.battery.one.fragment.main.animation  List -com.tqhit.battery.one.fragment.main.animation  getValue -com.tqhit.battery.one.fragment.main.animation  lazy -com.tqhit.battery.one.fragment.main.animation  provideDelegate -com.tqhit.battery.one.fragment.main.animation  AnimationAdapter Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  AnimationCategory Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  AnimationViewModel Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  
AppRepository Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  ApplovinInterstitialAdManager Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  CategoryAdapter Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  FirebaseRemoteConfigHelper Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  FragmentAnimationGridBinding Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  Inject Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  Int Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  List Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getGETValue Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getGetValue Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getLAYOUTInflater Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getLAZY Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getLayoutInflater Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getLazy Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getPROVIDEDelegate Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getProvideDelegate Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  getValue Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  layoutInflater Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  lazy Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  provideDelegate Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  setLayoutInflater Ccom.tqhit.battery.one.fragment.main.animation.AnimationGridFragment  AnimationAdapter 5com.tqhit.battery.one.fragment.main.animation.adapter  CategoryAdapter 5com.tqhit.battery.one.fragment.main.animation.adapter  AnimationCategory 2com.tqhit.battery.one.fragment.main.animation.data  
AnimationItem 2com.tqhit.battery.one.fragment.main.animation.data  Boolean 2com.tqhit.battery.one.fragment.main.animation.data  List 2com.tqhit.battery.one.fragment.main.animation.data  String 2com.tqhit.battery.one.fragment.main.animation.data  
AnimationItem Dcom.tqhit.battery.one.fragment.main.animation.data.AnimationCategory  List Dcom.tqhit.battery.one.fragment.main.animation.data.AnimationCategory  SerializedName Dcom.tqhit.battery.one.fragment.main.animation.data.AnimationCategory  String Dcom.tqhit.battery.one.fragment.main.animation.data.AnimationCategory  Boolean @com.tqhit.battery.one.fragment.main.animation.data.AnimationItem  SerializedName @com.tqhit.battery.one.fragment.main.animation.data.AnimationItem  String @com.tqhit.battery.one.fragment.main.animation.data.AnimationItem  
ChargeSession $com.tqhit.battery.one.manager.charge  ChargingSessionManager $com.tqhit.battery.one.manager.charge  Double $com.tqhit.battery.one.manager.charge  Int $com.tqhit.battery.one.manager.charge  List $com.tqhit.battery.one.manager.charge  Long $com.tqhit.battery.one.manager.charge  Double 2com.tqhit.battery.one.manager.charge.ChargeSession  Int 2com.tqhit.battery.one.manager.charge.ChargeSession  Long 2com.tqhit.battery.one.manager.charge.ChargeSession  Double <com.tqhit.battery.one.manager.charge.ChargeSession.Companion  Int <com.tqhit.battery.one.manager.charge.ChargeSession.Companion  Long <com.tqhit.battery.one.manager.charge.ChargeSession.Companion  
ChargeSession ;com.tqhit.battery.one.manager.charge.ChargingSessionManager  Double ;com.tqhit.battery.one.manager.charge.ChargingSessionManager  Inject ;com.tqhit.battery.one.manager.charge.ChargingSessionManager  Int ;com.tqhit.battery.one.manager.charge.ChargingSessionManager  List ;com.tqhit.battery.one.manager.charge.ChargingSessionManager  PreferencesHelper ;com.tqhit.battery.one.manager.charge.ChargingSessionManager  
ChargeSession Ecom.tqhit.battery.one.manager.charge.ChargingSessionManager.Companion  Double Ecom.tqhit.battery.one.manager.charge.ChargingSessionManager.Companion  Inject Ecom.tqhit.battery.one.manager.charge.ChargingSessionManager.Companion  Int Ecom.tqhit.battery.one.manager.charge.ChargingSessionManager.Companion  List Ecom.tqhit.battery.one.manager.charge.ChargingSessionManager.Companion  PreferencesHelper Ecom.tqhit.battery.one.manager.charge.ChargingSessionManager.Companion  DischargeSession 'com.tqhit.battery.one.manager.discharge  DischargeSessionManager 'com.tqhit.battery.one.manager.discharge  Double 'com.tqhit.battery.one.manager.discharge  Int 'com.tqhit.battery.one.manager.discharge  Long 'com.tqhit.battery.one.manager.discharge  Double 8com.tqhit.battery.one.manager.discharge.DischargeSession  Int 8com.tqhit.battery.one.manager.discharge.DischargeSession  Long 8com.tqhit.battery.one.manager.discharge.DischargeSession  Double Bcom.tqhit.battery.one.manager.discharge.DischargeSession.Companion  Int Bcom.tqhit.battery.one.manager.discharge.DischargeSession.Companion  Long Bcom.tqhit.battery.one.manager.discharge.DischargeSession.Companion  DischargeSession ?com.tqhit.battery.one.manager.discharge.DischargeSessionManager  Double ?com.tqhit.battery.one.manager.discharge.DischargeSessionManager  Inject ?com.tqhit.battery.one.manager.discharge.DischargeSessionManager  Int ?com.tqhit.battery.one.manager.discharge.DischargeSessionManager  PreferencesHelper ?com.tqhit.battery.one.manager.discharge.DischargeSessionManager  DischargeSession Icom.tqhit.battery.one.manager.discharge.DischargeSessionManager.Companion  Double Icom.tqhit.battery.one.manager.discharge.DischargeSessionManager.Companion  Inject Icom.tqhit.battery.one.manager.discharge.DischargeSessionManager.Companion  Int Icom.tqhit.battery.one.manager.discharge.DischargeSessionManager.Companion  PreferencesHelper Icom.tqhit.battery.one.manager.discharge.DischargeSessionManager.Companion  BatteryHistoryManager #com.tqhit.battery.one.manager.graph  Double #com.tqhit.battery.one.manager.graph  HistoryEntry #com.tqhit.battery.one.manager.graph  HistoryManager #com.tqhit.battery.one.manager.graph  Int #com.tqhit.battery.one.manager.graph  List #com.tqhit.battery.one.manager.graph  Long #com.tqhit.battery.one.manager.graph  String #com.tqhit.battery.one.manager.graph  TemperatureHistoryManager #com.tqhit.battery.one.manager.graph  
mutableListOf #com.tqhit.battery.one.manager.graph  Inject 9com.tqhit.battery.one.manager.graph.BatteryHistoryManager  Int 9com.tqhit.battery.one.manager.graph.BatteryHistoryManager  PreferencesHelper 9com.tqhit.battery.one.manager.graph.BatteryHistoryManager  String 9com.tqhit.battery.one.manager.graph.BatteryHistoryManager  Double 2com.tqhit.battery.one.manager.graph.HistoryManager  HistoryEntry 2com.tqhit.battery.one.manager.graph.HistoryManager  Inject 2com.tqhit.battery.one.manager.graph.HistoryManager  Int 2com.tqhit.battery.one.manager.graph.HistoryManager  List 2com.tqhit.battery.one.manager.graph.HistoryManager  Long 2com.tqhit.battery.one.manager.graph.HistoryManager  PreferencesHelper 2com.tqhit.battery.one.manager.graph.HistoryManager  String 2com.tqhit.battery.one.manager.graph.HistoryManager  T 2com.tqhit.battery.one.manager.graph.HistoryManager  getMUTABLEListOf 2com.tqhit.battery.one.manager.graph.HistoryManager  getMutableListOf 2com.tqhit.battery.one.manager.graph.HistoryManager  
mutableListOf 2com.tqhit.battery.one.manager.graph.HistoryManager  Double =com.tqhit.battery.one.manager.graph.TemperatureHistoryManager  Inject =com.tqhit.battery.one.manager.graph.TemperatureHistoryManager  PreferencesHelper =com.tqhit.battery.one.manager.graph.TemperatureHistoryManager  String =com.tqhit.battery.one.manager.graph.TemperatureHistoryManager  SharedPreferences 0com.tqhit.battery.one.manager.theme.ThemeManager  Activity  com.tqhit.battery.one.repository  AnimationRepository  com.tqhit.battery.one.repository  
AppRepository  com.tqhit.battery.one.repository  BatteryRepository  com.tqhit.battery.one.repository  Boolean  com.tqhit.battery.one.repository  Double  com.tqhit.battery.one.repository  Int  com.tqhit.battery.one.repository  KEY_ANIMATION_OVERLAY_ENABLED  com.tqhit.battery.one.repository  "KEY_ANIMATION_OVERLAY_TIME_ENABLED  com.tqhit.battery.one.repository  KEY_ANTI_THIEF_ALERT_ACTIVE  com.tqhit.battery.one.repository  KEY_ANTI_THIEF_ENABLED  com.tqhit.battery.one.repository  KEY_ANTI_THIEF_PASSWORD  com.tqhit.battery.one.repository  KEY_ANTI_THIEF_SOUND_ENABLED  com.tqhit.battery.one.repository  KEY_CHARGE_ALARM_ENABLED  com.tqhit.battery.one.repository  KEY_CHARGE_NOTIFICATION_ENABLED  com.tqhit.battery.one.repository  KEY_CONSENT_FLOW_USER_GEOGRAPHY  com.tqhit.battery.one.repository  KEY_DISCHARGE_ALARM_ENABLED  com.tqhit.battery.one.repository  KEY_DISCHARGE_ALARM_PERCENT  com.tqhit.battery.one.repository  "KEY_DISCHARGE_NOTIFICATION_ENABLED  com.tqhit.battery.one.repository  KEY_DONT_DISTURB_CHARGE_ENABLED  com.tqhit.battery.one.repository  !KEY_DONT_DISTURB_CHARGE_FROM_TIME  com.tqhit.battery.one.repository  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME  com.tqhit.battery.one.repository  "KEY_DONT_DISTURB_DISCHARGE_ENABLED  com.tqhit.battery.one.repository  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME  com.tqhit.battery.one.repository  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME  com.tqhit.battery.one.repository  KEY_LANGUAGE  com.tqhit.battery.one.repository  KEY_NOTIFY_FULL_CHARGE_ENABLED  com.tqhit.battery.one.repository  KEY_PRIVACY_POLICY_ACCEPTED  com.tqhit.battery.one.repository  KEY_SHOWED_START_PAGE  com.tqhit.battery.one.repository  KEY_VIBRATION_CHARGE_ENABLED  com.tqhit.battery.one.repository  KEY_VIBRATION_DISCHARGE_ENABLED  com.tqhit.battery.one.repository  KEY_VIBRATION_ENABLED  com.tqhit.battery.one.repository  KEY_VIDEO_PATH  com.tqhit.battery.one.repository  List  com.tqhit.battery.one.repository  Long  com.tqhit.battery.one.repository  String  com.tqhit.battery.one.repository  AnimationApplyEntry 4com.tqhit.battery.one.repository.AnimationRepository  Boolean 4com.tqhit.battery.one.repository.AnimationRepository  Inject 4com.tqhit.battery.one.repository.AnimationRepository  List 4com.tqhit.battery.one.repository.AnimationRepository  Long 4com.tqhit.battery.one.repository.AnimationRepository  PreferencesHelper 4com.tqhit.battery.one.repository.AnimationRepository  String 4com.tqhit.battery.one.repository.AnimationRepository  Boolean >com.tqhit.battery.one.repository.AnimationRepository.Companion  Inject >com.tqhit.battery.one.repository.AnimationRepository.Companion  List >com.tqhit.battery.one.repository.AnimationRepository.Companion  Long >com.tqhit.battery.one.repository.AnimationRepository.Companion  PreferencesHelper >com.tqhit.battery.one.repository.AnimationRepository.Companion  String >com.tqhit.battery.one.repository.AnimationRepository.Companion  ApplicationContext .com.tqhit.battery.one.repository.AppRepository  Boolean .com.tqhit.battery.one.repository.AppRepository  Context .com.tqhit.battery.one.repository.AppRepository  Flow .com.tqhit.battery.one.repository.AppRepository  Inject .com.tqhit.battery.one.repository.AppRepository  Int .com.tqhit.battery.one.repository.AppRepository  KEY_ANIMATION_OVERLAY_ENABLED .com.tqhit.battery.one.repository.AppRepository  "KEY_ANIMATION_OVERLAY_TIME_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_ANTI_THIEF_ALERT_ACTIVE .com.tqhit.battery.one.repository.AppRepository  KEY_ANTI_THIEF_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_ANTI_THIEF_PASSWORD .com.tqhit.battery.one.repository.AppRepository  KEY_ANTI_THIEF_SOUND_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_CHARGE_ALARM_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_CHARGE_NOTIFICATION_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_CONSENT_FLOW_USER_GEOGRAPHY .com.tqhit.battery.one.repository.AppRepository  KEY_DISCHARGE_ALARM_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_DISCHARGE_ALARM_PERCENT .com.tqhit.battery.one.repository.AppRepository  "KEY_DISCHARGE_NOTIFICATION_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_DONT_DISTURB_CHARGE_ENABLED .com.tqhit.battery.one.repository.AppRepository  !KEY_DONT_DISTURB_CHARGE_FROM_TIME .com.tqhit.battery.one.repository.AppRepository  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME .com.tqhit.battery.one.repository.AppRepository  "KEY_DONT_DISTURB_DISCHARGE_ENABLED .com.tqhit.battery.one.repository.AppRepository  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME .com.tqhit.battery.one.repository.AppRepository  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME .com.tqhit.battery.one.repository.AppRepository  KEY_LANGUAGE .com.tqhit.battery.one.repository.AppRepository  KEY_NOTIFY_FULL_CHARGE_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_PRIVACY_POLICY_ACCEPTED .com.tqhit.battery.one.repository.AppRepository  KEY_SHOWED_START_PAGE .com.tqhit.battery.one.repository.AppRepository  KEY_VIBRATION_CHARGE_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_VIBRATION_DISCHARGE_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_VIBRATION_ENABLED .com.tqhit.battery.one.repository.AppRepository  KEY_VIDEO_PATH .com.tqhit.battery.one.repository.AppRepository  PreferencesHelper .com.tqhit.battery.one.repository.AppRepository  String .com.tqhit.battery.one.repository.AppRepository  acceptPrivacyPolicy .com.tqhit.battery.one.repository.AppRepository  clearAntiThiefPassword .com.tqhit.battery.one.repository.AppRepository  preferencesHelper .com.tqhit.battery.one.repository.AppRepository  setAnimationOverlayEnabled .com.tqhit.battery.one.repository.AppRepository  setAnimationOverlayTimeEnabled .com.tqhit.battery.one.repository.AppRepository  setAntiThiefEnabled .com.tqhit.battery.one.repository.AppRepository  setAntiThiefPassword .com.tqhit.battery.one.repository.AppRepository  setAntiThiefSoundEnabled .com.tqhit.battery.one.repository.AppRepository  setChargeAlarmEnabled .com.tqhit.battery.one.repository.AppRepository  setChargeAlarmPercent .com.tqhit.battery.one.repository.AppRepository  setChargeNotificationEnabled .com.tqhit.battery.one.repository.AppRepository  setConsentFlowUserGeography .com.tqhit.battery.one.repository.AppRepository  setDischargeAlarmEnabled .com.tqhit.battery.one.repository.AppRepository  setDischargeAlarmPercent .com.tqhit.battery.one.repository.AppRepository  setDischargeNotificationEnabled .com.tqhit.battery.one.repository.AppRepository  setDontDisturbChargeEnabled .com.tqhit.battery.one.repository.AppRepository  setDontDisturbChargeFromTime .com.tqhit.battery.one.repository.AppRepository  setDontDisturbChargeUntilTime .com.tqhit.battery.one.repository.AppRepository  setDontDisturbDischargeEnabled .com.tqhit.battery.one.repository.AppRepository  setDontDisturbDischargeFromTime .com.tqhit.battery.one.repository.AppRepository   setDontDisturbDischargeUntilTime .com.tqhit.battery.one.repository.AppRepository  setLanguage .com.tqhit.battery.one.repository.AppRepository  setNotifyFullChargeEnabled .com.tqhit.battery.one.repository.AppRepository  setShowedStartPage .com.tqhit.battery.one.repository.AppRepository  setVibrationChargeEnabled .com.tqhit.battery.one.repository.AppRepository  setVibrationDischargeEnabled .com.tqhit.battery.one.repository.AppRepository  setVibrationEnabled .com.tqhit.battery.one.repository.AppRepository  ApplicationContext 8com.tqhit.battery.one.repository.AppRepository.Companion  Boolean 8com.tqhit.battery.one.repository.AppRepository.Companion  Context 8com.tqhit.battery.one.repository.AppRepository.Companion  Flow 8com.tqhit.battery.one.repository.AppRepository.Companion  Inject 8com.tqhit.battery.one.repository.AppRepository.Companion  Int 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_ANIMATION_OVERLAY_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  "KEY_ANIMATION_OVERLAY_TIME_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_ANTI_THIEF_ALERT_ACTIVE 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_ANTI_THIEF_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_ANTI_THIEF_PASSWORD 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_ANTI_THIEF_SOUND_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_CHARGE_ALARM_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_CHARGE_NOTIFICATION_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_CONSENT_FLOW_USER_GEOGRAPHY 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_DISCHARGE_ALARM_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_DISCHARGE_ALARM_PERCENT 8com.tqhit.battery.one.repository.AppRepository.Companion  "KEY_DISCHARGE_NOTIFICATION_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_DONT_DISTURB_CHARGE_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  !KEY_DONT_DISTURB_CHARGE_FROM_TIME 8com.tqhit.battery.one.repository.AppRepository.Companion  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME 8com.tqhit.battery.one.repository.AppRepository.Companion  "KEY_DONT_DISTURB_DISCHARGE_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME 8com.tqhit.battery.one.repository.AppRepository.Companion  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_LANGUAGE 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_NOTIFY_FULL_CHARGE_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_PRIVACY_POLICY_ACCEPTED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_SHOWED_START_PAGE 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_VIBRATION_CHARGE_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_VIBRATION_DISCHARGE_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_VIBRATION_ENABLED 8com.tqhit.battery.one.repository.AppRepository.Companion  KEY_VIDEO_PATH 8com.tqhit.battery.one.repository.AppRepository.Companion  PreferencesHelper 8com.tqhit.battery.one.repository.AppRepository.Companion  String 8com.tqhit.battery.one.repository.AppRepository.Companion  Activity 2com.tqhit.battery.one.repository.BatteryRepository  ApplicationContext 2com.tqhit.battery.one.repository.BatteryRepository  BatteryHistoryManager 2com.tqhit.battery.one.repository.BatteryRepository  BatteryManager 2com.tqhit.battery.one.repository.BatteryRepository  Boolean 2com.tqhit.battery.one.repository.BatteryRepository  
ChargeSession 2com.tqhit.battery.one.repository.BatteryRepository  ChargingSessionManager 2com.tqhit.battery.one.repository.BatteryRepository  Context 2com.tqhit.battery.one.repository.BatteryRepository  DischargeSession 2com.tqhit.battery.one.repository.BatteryRepository  DischargeSessionManager 2com.tqhit.battery.one.repository.BatteryRepository  Double 2com.tqhit.battery.one.repository.BatteryRepository  HistoryEntry 2com.tqhit.battery.one.repository.BatteryRepository  Inject 2com.tqhit.battery.one.repository.BatteryRepository  Int 2com.tqhit.battery.one.repository.BatteryRepository  Intent 2com.tqhit.battery.one.repository.BatteryRepository  List 2com.tqhit.battery.one.repository.BatteryRepository  Long 2com.tqhit.battery.one.repository.BatteryRepository  PreferencesHelper 2com.tqhit.battery.one.repository.BatteryRepository  	StateFlow 2com.tqhit.battery.one.repository.BatteryRepository  String 2com.tqhit.battery.one.repository.BatteryRepository  TemperatureHistoryManager 2com.tqhit.battery.one.repository.BatteryRepository  amperage 2com.tqhit.battery.one.repository.BatteryRepository  averageMilliAmperes 2com.tqhit.battery.one.repository.BatteryRepository  averageScreenOffMilliAmperes 2com.tqhit.battery.one.repository.BatteryRepository  averageScreenOffSpeed 2com.tqhit.battery.one.repository.BatteryRepository  averageScreenOnMilliAmperes 2com.tqhit.battery.one.repository.BatteryRepository  averageScreenOnSpeed 2com.tqhit.battery.one.repository.BatteryRepository  averageSpeed 2com.tqhit.battery.one.repository.BatteryRepository  averageSpeedMilliAmperesSession 2com.tqhit.battery.one.repository.BatteryRepository  averageSpeedSession 2com.tqhit.battery.one.repository.BatteryRepository  batteryCapacity 2com.tqhit.battery.one.repository.BatteryRepository  
batteryHealth 2com.tqhit.battery.one.repository.BatteryRepository  batteryPercentage 2com.tqhit.battery.one.repository.BatteryRepository  chargingRate 2com.tqhit.battery.one.repository.BatteryRepository  chargingTimeRemaining 2com.tqhit.battery.one.repository.BatteryRepository  chargingTimeRemainingToTarget 2com.tqhit.battery.one.repository.BatteryRepository  context 2com.tqhit.battery.one.repository.BatteryRepository  (dischargeAverageSpeedMilliAmperesSession 2com.tqhit.battery.one.repository.BatteryRepository  dischargeAverageSpeedSession 2com.tqhit.battery.one.repository.BatteryRepository  dischargeEndPercentSession 2com.tqhit.battery.one.repository.BatteryRepository  dischargeEndTimeSession 2com.tqhit.battery.one.repository.BatteryRepository  &dischargeRightNowPercentPerHourSession 2com.tqhit.battery.one.repository.BatteryRepository  %dischargeScreenOffAverageSpeedSession 2com.tqhit.battery.one.repository.BatteryRepository  %dischargeScreenOffMilliAmperesSession 2com.tqhit.battery.one.repository.BatteryRepository  $dischargeScreenOnAverageSpeedSession 2com.tqhit.battery.one.repository.BatteryRepository  $dischargeScreenOnMilliAmperesSession 2com.tqhit.battery.one.repository.BatteryRepository  dischargeStartPercentSession 2com.tqhit.battery.one.repository.BatteryRepository  dischargeStartTimeSession 2com.tqhit.battery.one.repository.BatteryRepository  !dischargeTotalMilliAmperesSession 2com.tqhit.battery.one.repository.BatteryRepository  endPercentSession 2com.tqhit.battery.one.repository.BatteryRepository  endTimeSession 2com.tqhit.battery.one.repository.BatteryRepository  getCurrentChargingSession 2com.tqhit.battery.one.repository.BatteryRepository  getCurrentDischargeSession 2com.tqhit.battery.one.repository.BatteryRepository  getDailyWearData 2com.tqhit.battery.one.repository.BatteryRepository  getHistoryBatteryForHours 2com.tqhit.battery.one.repository.BatteryRepository  getHistoryTemperatureForHours 2com.tqhit.battery.one.repository.BatteryRepository  getLastValidChargingSession 2com.tqhit.battery.one.repository.BatteryRepository  getLastValidDischargeSession 2com.tqhit.battery.one.repository.BatteryRepository  
isCharging 2com.tqhit.battery.one.repository.BatteryRepository  power 2com.tqhit.battery.one.repository.BatteryRepository  rightNowPercentPerHourSession 2com.tqhit.battery.one.repository.BatteryRepository  screenOffAverageSpeedSession 2com.tqhit.battery.one.repository.BatteryRepository  screenOffMilliAmperesSession 2com.tqhit.battery.one.repository.BatteryRepository  screenOffTimeRemaining 2com.tqhit.battery.one.repository.BatteryRepository  screenOffTimeRemainingAt100 2com.tqhit.battery.one.repository.BatteryRepository  screenOnAverageSpeedSession 2com.tqhit.battery.one.repository.BatteryRepository  screenOnMilliAmperesSession 2com.tqhit.battery.one.repository.BatteryRepository  screenOnTimeRemaining 2com.tqhit.battery.one.repository.BatteryRepository  screenOnTimeRemainingAt100 2com.tqhit.battery.one.repository.BatteryRepository  selectedPercent 2com.tqhit.battery.one.repository.BatteryRepository  setBatteryCapacity 2com.tqhit.battery.one.repository.BatteryRepository  setCurrentChargingSession 2com.tqhit.battery.one.repository.BatteryRepository  setCurrentDischargeSession 2com.tqhit.battery.one.repository.BatteryRepository  setLastValidChargingSession 2com.tqhit.battery.one.repository.BatteryRepository  setLastValidDischargeSession 2com.tqhit.battery.one.repository.BatteryRepository  setSelectedPercent 2com.tqhit.battery.one.repository.BatteryRepository  startPercentSession 2com.tqhit.battery.one.repository.BatteryRepository  startTimeSession 2com.tqhit.battery.one.repository.BatteryRepository  temperature 2com.tqhit.battery.one.repository.BatteryRepository  totalChargeSession 2com.tqhit.battery.one.repository.BatteryRepository  totalMilliAmperesSession 2com.tqhit.battery.one.repository.BatteryRepository  usageStyleTimeRemaining 2com.tqhit.battery.one.repository.BatteryRepository  usageStyleTimeRemainingAt100 2com.tqhit.battery.one.repository.BatteryRepository  voltage 2com.tqhit.battery.one.repository.BatteryRepository  Activity <com.tqhit.battery.one.repository.BatteryRepository.Companion  ApplicationContext <com.tqhit.battery.one.repository.BatteryRepository.Companion  BatteryHistoryManager <com.tqhit.battery.one.repository.BatteryRepository.Companion  BatteryManager <com.tqhit.battery.one.repository.BatteryRepository.Companion  Boolean <com.tqhit.battery.one.repository.BatteryRepository.Companion  
ChargeSession <com.tqhit.battery.one.repository.BatteryRepository.Companion  ChargingSessionManager <com.tqhit.battery.one.repository.BatteryRepository.Companion  Context <com.tqhit.battery.one.repository.BatteryRepository.Companion  DischargeSession <com.tqhit.battery.one.repository.BatteryRepository.Companion  DischargeSessionManager <com.tqhit.battery.one.repository.BatteryRepository.Companion  Double <com.tqhit.battery.one.repository.BatteryRepository.Companion  HistoryEntry <com.tqhit.battery.one.repository.BatteryRepository.Companion  Inject <com.tqhit.battery.one.repository.BatteryRepository.Companion  Int <com.tqhit.battery.one.repository.BatteryRepository.Companion  Intent <com.tqhit.battery.one.repository.BatteryRepository.Companion  List <com.tqhit.battery.one.repository.BatteryRepository.Companion  Long <com.tqhit.battery.one.repository.BatteryRepository.Companion  PreferencesHelper <com.tqhit.battery.one.repository.BatteryRepository.Companion  	StateFlow <com.tqhit.battery.one.repository.BatteryRepository.Companion  String <com.tqhit.battery.one.repository.BatteryRepository.Companion  TemperatureHistoryManager <com.tqhit.battery.one.repository.BatteryRepository.Companion  BatteryMonitorService com.tqhit.battery.one.service  Boolean com.tqhit.battery.one.service  ChargingOverlayService com.tqhit.battery.one.service  Int com.tqhit.battery.one.service  IntArray com.tqhit.battery.one.service  Long com.tqhit.battery.one.service  	LongArray com.tqhit.battery.one.service  
PATTERN_CLICK com.tqhit.battery.one.service  PATTERN_DOUBLE_CLICK com.tqhit.battery.one.service  
PATTERN_ERROR com.tqhit.battery.one.service  PATTERN_SUCCESS com.tqhit.battery.one.service  PATTERN_TICK com.tqhit.battery.one.service  PATTERN_WARNING com.tqhit.battery.one.service  Runnable com.tqhit.battery.one.service  String com.tqhit.battery.one.service  VibrationService com.tqhit.battery.one.service  android com.tqhit.battery.one.service  longArrayOf com.tqhit.battery.one.service  
AppRepository 3com.tqhit.battery.one.service.BatteryMonitorService  BatteryRepository 3com.tqhit.battery.one.service.BatteryMonitorService  Boolean 3com.tqhit.battery.one.service.BatteryMonitorService  IBinder 3com.tqhit.battery.one.service.BatteryMonitorService  Inject 3com.tqhit.battery.one.service.BatteryMonitorService  Int 3com.tqhit.battery.one.service.BatteryMonitorService  Intent 3com.tqhit.battery.one.service.BatteryMonitorService  Notification 3com.tqhit.battery.one.service.BatteryMonitorService  Runnable 3com.tqhit.battery.one.service.BatteryMonitorService  android 3com.tqhit.battery.one.service.BatteryMonitorService  
AppRepository =com.tqhit.battery.one.service.BatteryMonitorService.Companion  BatteryRepository =com.tqhit.battery.one.service.BatteryMonitorService.Companion  Boolean =com.tqhit.battery.one.service.BatteryMonitorService.Companion  IBinder =com.tqhit.battery.one.service.BatteryMonitorService.Companion  Inject =com.tqhit.battery.one.service.BatteryMonitorService.Companion  Int =com.tqhit.battery.one.service.BatteryMonitorService.Companion  Intent =com.tqhit.battery.one.service.BatteryMonitorService.Companion  Notification =com.tqhit.battery.one.service.BatteryMonitorService.Companion  Runnable =com.tqhit.battery.one.service.BatteryMonitorService.Companion  android =com.tqhit.battery.one.service.BatteryMonitorService.Companion  AnimationRepository 4com.tqhit.battery.one.service.ChargingOverlayService  
AppRepository 4com.tqhit.battery.one.service.ChargingOverlayService  Context 4com.tqhit.battery.one.service.ChargingOverlayService  IBinder 4com.tqhit.battery.one.service.ChargingOverlayService  Inject 4com.tqhit.battery.one.service.ChargingOverlayService  Int 4com.tqhit.battery.one.service.ChargingOverlayService  Intent 4com.tqhit.battery.one.service.ChargingOverlayService  String 4com.tqhit.battery.one.service.ChargingOverlayService  AnimationRepository >com.tqhit.battery.one.service.ChargingOverlayService.Companion  
AppRepository >com.tqhit.battery.one.service.ChargingOverlayService.Companion  Context >com.tqhit.battery.one.service.ChargingOverlayService.Companion  IBinder >com.tqhit.battery.one.service.ChargingOverlayService.Companion  Inject >com.tqhit.battery.one.service.ChargingOverlayService.Companion  Int >com.tqhit.battery.one.service.ChargingOverlayService.Companion  Intent >com.tqhit.battery.one.service.ChargingOverlayService.Companion  String >com.tqhit.battery.one.service.ChargingOverlayService.Companion  ApplicationContext .com.tqhit.battery.one.service.VibrationService  Boolean .com.tqhit.battery.one.service.VibrationService  Context .com.tqhit.battery.one.service.VibrationService  Inject .com.tqhit.battery.one.service.VibrationService  Int .com.tqhit.battery.one.service.VibrationService  IntArray .com.tqhit.battery.one.service.VibrationService  Long .com.tqhit.battery.one.service.VibrationService  	LongArray .com.tqhit.battery.one.service.VibrationService  
PATTERN_CLICK .com.tqhit.battery.one.service.VibrationService  PATTERN_DOUBLE_CLICK .com.tqhit.battery.one.service.VibrationService  
PATTERN_ERROR .com.tqhit.battery.one.service.VibrationService  PATTERN_SUCCESS .com.tqhit.battery.one.service.VibrationService  PATTERN_TICK .com.tqhit.battery.one.service.VibrationService  PATTERN_WARNING .com.tqhit.battery.one.service.VibrationService  longArrayOf .com.tqhit.battery.one.service.VibrationService  vibratePattern .com.tqhit.battery.one.service.VibrationService  ApplicationContext 8com.tqhit.battery.one.service.VibrationService.Companion  Boolean 8com.tqhit.battery.one.service.VibrationService.Companion  Context 8com.tqhit.battery.one.service.VibrationService.Companion  Inject 8com.tqhit.battery.one.service.VibrationService.Companion  Int 8com.tqhit.battery.one.service.VibrationService.Companion  IntArray 8com.tqhit.battery.one.service.VibrationService.Companion  Long 8com.tqhit.battery.one.service.VibrationService.Companion  	LongArray 8com.tqhit.battery.one.service.VibrationService.Companion  
PATTERN_CLICK 8com.tqhit.battery.one.service.VibrationService.Companion  PATTERN_DOUBLE_CLICK 8com.tqhit.battery.one.service.VibrationService.Companion  
PATTERN_ERROR 8com.tqhit.battery.one.service.VibrationService.Companion  PATTERN_SUCCESS 8com.tqhit.battery.one.service.VibrationService.Companion  PATTERN_TICK 8com.tqhit.battery.one.service.VibrationService.Companion  PATTERN_WARNING 8com.tqhit.battery.one.service.VibrationService.Companion  VIBRATION_DURATION_MEDIUM 8com.tqhit.battery.one.service.VibrationService.Companion  VIBRATION_DURATION_SHORT 8com.tqhit.battery.one.service.VibrationService.Companion  getLONGArrayOf 8com.tqhit.battery.one.service.VibrationService.Companion  getLongArrayOf 8com.tqhit.battery.one.service.VibrationService.Companion  longArrayOf 8com.tqhit.battery.one.service.VibrationService.Companion  AppViewModel com.tqhit.battery.one.viewmodel  Boolean com.tqhit.battery.one.viewmodel  Int com.tqhit.battery.one.viewmodel  String com.tqhit.battery.one.viewmodel  android com.tqhit.battery.one.viewmodel  
AppRepository ,com.tqhit.battery.one.viewmodel.AppViewModel  Boolean ,com.tqhit.battery.one.viewmodel.AppViewModel  Inject ,com.tqhit.battery.one.viewmodel.AppViewModel  Int ,com.tqhit.battery.one.viewmodel.AppViewModel  String ,com.tqhit.battery.one.viewmodel.AppViewModel  android ,com.tqhit.battery.one.viewmodel.AppViewModel  
appRepository ,com.tqhit.battery.one.viewmodel.AppViewModel  AnimationViewModel )com.tqhit.battery.one.viewmodel.animation  Boolean )com.tqhit.battery.one.viewmodel.animation  List )com.tqhit.battery.one.viewmodel.animation  Long )com.tqhit.battery.one.viewmodel.animation  String )com.tqhit.battery.one.viewmodel.animation  AnimationApplyEntry <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  AnimationRepository <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  ApplicationContext <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  Boolean <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  Context <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  Inject <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  List <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  Long <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  String <com.tqhit.battery.one.viewmodel.animation.AnimationViewModel  BatteryViewModel 'com.tqhit.battery.one.viewmodel.battery  Boolean 'com.tqhit.battery.one.viewmodel.battery  
Deprecated 'com.tqhit.battery.one.viewmodel.battery  Int 'com.tqhit.battery.one.viewmodel.battery  Long 'com.tqhit.battery.one.viewmodel.battery  ReplaceWith 'com.tqhit.battery.one.viewmodel.battery  String 'com.tqhit.battery.one.viewmodel.battery  ApplicationContext 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  BatteryRepository 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  Boolean 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  Context 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  
Deprecated 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  Inject 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  Int 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  Long 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  ReplaceWith 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  String 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  
repository 8com.tqhit.battery.one.viewmodel.battery.BatteryViewModel  Binds dagger  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  HiltViewModelMap &dagger.hilt.android.internal.lifecycle  KeySet 7dagger.hilt.android.internal.lifecycle.HiltViewModelMap  
HiltViewModel dagger.hilt.android.lifecycle  ActivityContext dagger.hilt.android.qualifiers  ApplicationContext dagger.hilt.android.qualifiers  OriginatingElement dagger.hilt.codegen  SingletonComponent dagger.hilt.components  GeneratedEntryPoint dagger.hilt.internal  IntoMap dagger.multibindings  LazyClassKey dagger.multibindings  Inject jakarta.inject  Activity 	java.lang  ActivityAnimationBinding 	java.lang  ActivityChargingOverlayBinding 	java.lang  ActivityEnterPasswordBinding 	java.lang  ActivityMainBinding 	java.lang  ActivitySplashBinding 	java.lang  ActivityStartingBinding 	java.lang  Class 	java.lang  Context 	java.lang  DialogSelectBatteryAlarmBinding 	java.lang  "DialogSelectBatteryAlarmLowBinding 	java.lang  Dispatchers 	java.lang  FlowPreview 	java.lang  FragmentAnimationGridBinding 	java.lang  FragmentChargeBinding 	java.lang  FragmentHealthBinding 	java.lang  FragmentSettingsBinding 	java.lang  KEY_ANIMATION_OVERLAY_ENABLED 	java.lang  "KEY_ANIMATION_OVERLAY_TIME_ENABLED 	java.lang  KEY_ANTI_THIEF_ALERT_ACTIVE 	java.lang  KEY_ANTI_THIEF_ENABLED 	java.lang  KEY_ANTI_THIEF_PASSWORD 	java.lang  KEY_ANTI_THIEF_SOUND_ENABLED 	java.lang  KEY_CHARGE_ALARM_ENABLED 	java.lang  KEY_CHARGE_NOTIFICATION_ENABLED 	java.lang  KEY_CONSENT_FLOW_USER_GEOGRAPHY 	java.lang  KEY_CURRENT_SESSION 	java.lang  KEY_DISCHARGE_ALARM_ENABLED 	java.lang  KEY_DISCHARGE_ALARM_PERCENT 	java.lang  "KEY_DISCHARGE_NOTIFICATION_ENABLED 	java.lang  KEY_DONT_DISTURB_CHARGE_ENABLED 	java.lang  !KEY_DONT_DISTURB_CHARGE_FROM_TIME 	java.lang  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME 	java.lang  "KEY_DONT_DISTURB_DISCHARGE_ENABLED 	java.lang  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME 	java.lang  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME 	java.lang  KEY_LANGUAGE 	java.lang  KEY_NOTIFY_FULL_CHARGE_ENABLED 	java.lang  KEY_PRIVACY_POLICY_ACCEPTED 	java.lang  KEY_SCREEN_OFF_RATE 	java.lang  KEY_SCREEN_ON_RATE 	java.lang  KEY_SHOWED_START_PAGE 	java.lang  KEY_VIBRATION_CHARGE_ENABLED 	java.lang  KEY_VIBRATION_DISCHARGE_ENABLED 	java.lang  KEY_VIBRATION_ENABLED 	java.lang  KEY_VIDEO_PATH 	java.lang  	Lifecycle 	java.lang  Log 	java.lang  NewFragmentDischargeBinding 	java.lang  
PATTERN_CLICK 	java.lang  PATTERN_DOUBLE_CLICK 	java.lang  
PATTERN_ERROR 	java.lang  PATTERN_SUCCESS 	java.lang  PATTERN_TICK 	java.lang  PATTERN_WARNING 	java.lang  
PREFS_NAME 	java.lang  ReplaceWith 	java.lang  Runnable 	java.lang  ScreenStateTimeTracker 	java.lang  SingletonComponent 	java.lang  TAG 	java.lang  UnstableApi 	java.lang  android 	java.lang  com 	java.lang  delay 	java.lang  distinctUntilChanged 	java.lang  flow 	java.lang  getValue 	java.lang  gson 	java.lang  lazy 	java.lang  longArrayOf 	java.lang  
mutableListOf 	java.lang  prefs 	java.lang  provideDelegate 	java.lang  screenStateTimeTracker 	java.lang  withContext 	java.lang  Activity 	java.util  FragmentHealthBinding 	java.util  Pair 	java.util  Runnable 	java.util  android 	java.util  com 	java.util  getValue 	java.util  lazy 	java.util  provideDelegate 	java.util  ConcurrentLinkedQueue java.util.concurrent  	Generated javax.annotation.processing  Inject javax.inject  	Singleton javax.inject  Activity kotlin  ActivityAnimationBinding kotlin  ActivityChargingOverlayBinding kotlin  ActivityEnterPasswordBinding kotlin  ActivityMainBinding kotlin  ActivitySplashBinding kotlin  ActivityStartingBinding kotlin  Any kotlin  Boolean kotlin  Class kotlin  Context kotlin  
Deprecated kotlin  DialogSelectBatteryAlarmBinding kotlin  "DialogSelectBatteryAlarmLowBinding kotlin  Dispatchers kotlin  Double kotlin  Float kotlin  FlowPreview kotlin  FragmentAnimationGridBinding kotlin  FragmentChargeBinding kotlin  FragmentHealthBinding kotlin  FragmentSettingsBinding kotlin  	Function0 kotlin  Int kotlin  IntArray kotlin  KEY_ANIMATION_OVERLAY_ENABLED kotlin  "KEY_ANIMATION_OVERLAY_TIME_ENABLED kotlin  KEY_ANTI_THIEF_ALERT_ACTIVE kotlin  KEY_ANTI_THIEF_ENABLED kotlin  KEY_ANTI_THIEF_PASSWORD kotlin  KEY_ANTI_THIEF_SOUND_ENABLED kotlin  KEY_CHARGE_ALARM_ENABLED kotlin  KEY_CHARGE_NOTIFICATION_ENABLED kotlin  KEY_CONSENT_FLOW_USER_GEOGRAPHY kotlin  KEY_CURRENT_SESSION kotlin  KEY_DISCHARGE_ALARM_ENABLED kotlin  KEY_DISCHARGE_ALARM_PERCENT kotlin  "KEY_DISCHARGE_NOTIFICATION_ENABLED kotlin  KEY_DONT_DISTURB_CHARGE_ENABLED kotlin  !KEY_DONT_DISTURB_CHARGE_FROM_TIME kotlin  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME kotlin  "KEY_DONT_DISTURB_DISCHARGE_ENABLED kotlin  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME kotlin  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME kotlin  KEY_LANGUAGE kotlin  KEY_NOTIFY_FULL_CHARGE_ENABLED kotlin  KEY_PRIVACY_POLICY_ACCEPTED kotlin  KEY_SCREEN_OFF_RATE kotlin  KEY_SCREEN_ON_RATE kotlin  KEY_SHOWED_START_PAGE kotlin  KEY_VIBRATION_CHARGE_ENABLED kotlin  KEY_VIBRATION_DISCHARGE_ENABLED kotlin  KEY_VIBRATION_ENABLED kotlin  KEY_VIDEO_PATH kotlin  Lazy kotlin  	Lifecycle kotlin  Log kotlin  Long kotlin  	LongArray kotlin  NewFragmentDischargeBinding kotlin  Number kotlin  OptIn kotlin  
PATTERN_CLICK kotlin  PATTERN_DOUBLE_CLICK kotlin  
PATTERN_ERROR kotlin  PATTERN_SUCCESS kotlin  PATTERN_TICK kotlin  PATTERN_WARNING kotlin  
PREFS_NAME kotlin  Pair kotlin  ReplaceWith kotlin  Runnable kotlin  ScreenStateTimeTracker kotlin  SingletonComponent kotlin  String kotlin  TAG kotlin  Unit kotlin  UnstableApi kotlin  android kotlin  com kotlin  delay kotlin  distinctUntilChanged kotlin  flow kotlin  getValue kotlin  gson kotlin  lazy kotlin  longArrayOf kotlin  
mutableListOf kotlin  prefs kotlin  provideDelegate kotlin  screenStateTimeTracker kotlin  withContext kotlin  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  first kotlin.Pair  second kotlin.Pair  Activity kotlin.annotation  ActivityAnimationBinding kotlin.annotation  ActivityChargingOverlayBinding kotlin.annotation  ActivityEnterPasswordBinding kotlin.annotation  ActivityMainBinding kotlin.annotation  ActivitySplashBinding kotlin.annotation  ActivityStartingBinding kotlin.annotation  Class kotlin.annotation  Context kotlin.annotation  DialogSelectBatteryAlarmBinding kotlin.annotation  "DialogSelectBatteryAlarmLowBinding kotlin.annotation  Dispatchers kotlin.annotation  FlowPreview kotlin.annotation  FragmentAnimationGridBinding kotlin.annotation  FragmentChargeBinding kotlin.annotation  FragmentHealthBinding kotlin.annotation  FragmentSettingsBinding kotlin.annotation  KEY_ANIMATION_OVERLAY_ENABLED kotlin.annotation  "KEY_ANIMATION_OVERLAY_TIME_ENABLED kotlin.annotation  KEY_ANTI_THIEF_ALERT_ACTIVE kotlin.annotation  KEY_ANTI_THIEF_ENABLED kotlin.annotation  KEY_ANTI_THIEF_PASSWORD kotlin.annotation  KEY_ANTI_THIEF_SOUND_ENABLED kotlin.annotation  KEY_CHARGE_ALARM_ENABLED kotlin.annotation  KEY_CHARGE_NOTIFICATION_ENABLED kotlin.annotation  KEY_CONSENT_FLOW_USER_GEOGRAPHY kotlin.annotation  KEY_CURRENT_SESSION kotlin.annotation  KEY_DISCHARGE_ALARM_ENABLED kotlin.annotation  KEY_DISCHARGE_ALARM_PERCENT kotlin.annotation  "KEY_DISCHARGE_NOTIFICATION_ENABLED kotlin.annotation  KEY_DONT_DISTURB_CHARGE_ENABLED kotlin.annotation  !KEY_DONT_DISTURB_CHARGE_FROM_TIME kotlin.annotation  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME kotlin.annotation  "KEY_DONT_DISTURB_DISCHARGE_ENABLED kotlin.annotation  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME kotlin.annotation  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME kotlin.annotation  KEY_LANGUAGE kotlin.annotation  KEY_NOTIFY_FULL_CHARGE_ENABLED kotlin.annotation  KEY_PRIVACY_POLICY_ACCEPTED kotlin.annotation  KEY_SCREEN_OFF_RATE kotlin.annotation  KEY_SCREEN_ON_RATE kotlin.annotation  KEY_SHOWED_START_PAGE kotlin.annotation  KEY_VIBRATION_CHARGE_ENABLED kotlin.annotation  KEY_VIBRATION_DISCHARGE_ENABLED kotlin.annotation  KEY_VIBRATION_ENABLED kotlin.annotation  KEY_VIDEO_PATH kotlin.annotation  	Lifecycle kotlin.annotation  Log kotlin.annotation  NewFragmentDischargeBinding kotlin.annotation  
PATTERN_CLICK kotlin.annotation  PATTERN_DOUBLE_CLICK kotlin.annotation  
PATTERN_ERROR kotlin.annotation  PATTERN_SUCCESS kotlin.annotation  PATTERN_TICK kotlin.annotation  PATTERN_WARNING kotlin.annotation  
PREFS_NAME kotlin.annotation  Pair kotlin.annotation  ReplaceWith kotlin.annotation  Runnable kotlin.annotation  ScreenStateTimeTracker kotlin.annotation  SingletonComponent kotlin.annotation  TAG kotlin.annotation  UnstableApi kotlin.annotation  android kotlin.annotation  com kotlin.annotation  delay kotlin.annotation  distinctUntilChanged kotlin.annotation  flow kotlin.annotation  getValue kotlin.annotation  gson kotlin.annotation  lazy kotlin.annotation  longArrayOf kotlin.annotation  
mutableListOf kotlin.annotation  prefs kotlin.annotation  provideDelegate kotlin.annotation  screenStateTimeTracker kotlin.annotation  withContext kotlin.annotation  Activity kotlin.collections  ActivityAnimationBinding kotlin.collections  ActivityChargingOverlayBinding kotlin.collections  ActivityEnterPasswordBinding kotlin.collections  ActivityMainBinding kotlin.collections  ActivitySplashBinding kotlin.collections  ActivityStartingBinding kotlin.collections  Class kotlin.collections  Context kotlin.collections  DialogSelectBatteryAlarmBinding kotlin.collections  "DialogSelectBatteryAlarmLowBinding kotlin.collections  Dispatchers kotlin.collections  FlowPreview kotlin.collections  FragmentAnimationGridBinding kotlin.collections  FragmentChargeBinding kotlin.collections  FragmentHealthBinding kotlin.collections  FragmentSettingsBinding kotlin.collections  KEY_ANIMATION_OVERLAY_ENABLED kotlin.collections  "KEY_ANIMATION_OVERLAY_TIME_ENABLED kotlin.collections  KEY_ANTI_THIEF_ALERT_ACTIVE kotlin.collections  KEY_ANTI_THIEF_ENABLED kotlin.collections  KEY_ANTI_THIEF_PASSWORD kotlin.collections  KEY_ANTI_THIEF_SOUND_ENABLED kotlin.collections  KEY_CHARGE_ALARM_ENABLED kotlin.collections  KEY_CHARGE_NOTIFICATION_ENABLED kotlin.collections  KEY_CONSENT_FLOW_USER_GEOGRAPHY kotlin.collections  KEY_CURRENT_SESSION kotlin.collections  KEY_DISCHARGE_ALARM_ENABLED kotlin.collections  KEY_DISCHARGE_ALARM_PERCENT kotlin.collections  "KEY_DISCHARGE_NOTIFICATION_ENABLED kotlin.collections  KEY_DONT_DISTURB_CHARGE_ENABLED kotlin.collections  !KEY_DONT_DISTURB_CHARGE_FROM_TIME kotlin.collections  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME kotlin.collections  "KEY_DONT_DISTURB_DISCHARGE_ENABLED kotlin.collections  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME kotlin.collections  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME kotlin.collections  KEY_LANGUAGE kotlin.collections  KEY_NOTIFY_FULL_CHARGE_ENABLED kotlin.collections  KEY_PRIVACY_POLICY_ACCEPTED kotlin.collections  KEY_SCREEN_OFF_RATE kotlin.collections  KEY_SCREEN_ON_RATE kotlin.collections  KEY_SHOWED_START_PAGE kotlin.collections  KEY_VIBRATION_CHARGE_ENABLED kotlin.collections  KEY_VIBRATION_DISCHARGE_ENABLED kotlin.collections  KEY_VIBRATION_ENABLED kotlin.collections  KEY_VIDEO_PATH kotlin.collections  	Lifecycle kotlin.collections  List kotlin.collections  Log kotlin.collections  MutableList kotlin.collections  NewFragmentDischargeBinding kotlin.collections  
PATTERN_CLICK kotlin.collections  PATTERN_DOUBLE_CLICK kotlin.collections  
PATTERN_ERROR kotlin.collections  PATTERN_SUCCESS kotlin.collections  PATTERN_TICK kotlin.collections  PATTERN_WARNING kotlin.collections  
PREFS_NAME kotlin.collections  Pair kotlin.collections  ReplaceWith kotlin.collections  Runnable kotlin.collections  ScreenStateTimeTracker kotlin.collections  SingletonComponent kotlin.collections  TAG kotlin.collections  UnstableApi kotlin.collections  android kotlin.collections  com kotlin.collections  delay kotlin.collections  distinctUntilChanged kotlin.collections  flow kotlin.collections  getValue kotlin.collections  gson kotlin.collections  lazy kotlin.collections  longArrayOf kotlin.collections  
mutableListOf kotlin.collections  prefs kotlin.collections  provideDelegate kotlin.collections  screenStateTimeTracker kotlin.collections  withContext kotlin.collections  Activity kotlin.comparisons  ActivityAnimationBinding kotlin.comparisons  ActivityChargingOverlayBinding kotlin.comparisons  ActivityEnterPasswordBinding kotlin.comparisons  ActivityMainBinding kotlin.comparisons  ActivitySplashBinding kotlin.comparisons  ActivityStartingBinding kotlin.comparisons  Class kotlin.comparisons  Context kotlin.comparisons  DialogSelectBatteryAlarmBinding kotlin.comparisons  "DialogSelectBatteryAlarmLowBinding kotlin.comparisons  Dispatchers kotlin.comparisons  FlowPreview kotlin.comparisons  FragmentAnimationGridBinding kotlin.comparisons  FragmentChargeBinding kotlin.comparisons  FragmentHealthBinding kotlin.comparisons  FragmentSettingsBinding kotlin.comparisons  KEY_ANIMATION_OVERLAY_ENABLED kotlin.comparisons  "KEY_ANIMATION_OVERLAY_TIME_ENABLED kotlin.comparisons  KEY_ANTI_THIEF_ALERT_ACTIVE kotlin.comparisons  KEY_ANTI_THIEF_ENABLED kotlin.comparisons  KEY_ANTI_THIEF_PASSWORD kotlin.comparisons  KEY_ANTI_THIEF_SOUND_ENABLED kotlin.comparisons  KEY_CHARGE_ALARM_ENABLED kotlin.comparisons  KEY_CHARGE_NOTIFICATION_ENABLED kotlin.comparisons  KEY_CONSENT_FLOW_USER_GEOGRAPHY kotlin.comparisons  KEY_CURRENT_SESSION kotlin.comparisons  KEY_DISCHARGE_ALARM_ENABLED kotlin.comparisons  KEY_DISCHARGE_ALARM_PERCENT kotlin.comparisons  "KEY_DISCHARGE_NOTIFICATION_ENABLED kotlin.comparisons  KEY_DONT_DISTURB_CHARGE_ENABLED kotlin.comparisons  !KEY_DONT_DISTURB_CHARGE_FROM_TIME kotlin.comparisons  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME kotlin.comparisons  "KEY_DONT_DISTURB_DISCHARGE_ENABLED kotlin.comparisons  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME kotlin.comparisons  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME kotlin.comparisons  KEY_LANGUAGE kotlin.comparisons  KEY_NOTIFY_FULL_CHARGE_ENABLED kotlin.comparisons  KEY_PRIVACY_POLICY_ACCEPTED kotlin.comparisons  KEY_SCREEN_OFF_RATE kotlin.comparisons  KEY_SCREEN_ON_RATE kotlin.comparisons  KEY_SHOWED_START_PAGE kotlin.comparisons  KEY_VIBRATION_CHARGE_ENABLED kotlin.comparisons  KEY_VIBRATION_DISCHARGE_ENABLED kotlin.comparisons  KEY_VIBRATION_ENABLED kotlin.comparisons  KEY_VIDEO_PATH kotlin.comparisons  	Lifecycle kotlin.comparisons  Log kotlin.comparisons  NewFragmentDischargeBinding kotlin.comparisons  
PATTERN_CLICK kotlin.comparisons  PATTERN_DOUBLE_CLICK kotlin.comparisons  
PATTERN_ERROR kotlin.comparisons  PATTERN_SUCCESS kotlin.comparisons  PATTERN_TICK kotlin.comparisons  PATTERN_WARNING kotlin.comparisons  
PREFS_NAME kotlin.comparisons  Pair kotlin.comparisons  ReplaceWith kotlin.comparisons  Runnable kotlin.comparisons  ScreenStateTimeTracker kotlin.comparisons  SingletonComponent kotlin.comparisons  TAG kotlin.comparisons  UnstableApi kotlin.comparisons  android kotlin.comparisons  com kotlin.comparisons  delay kotlin.comparisons  distinctUntilChanged kotlin.comparisons  flow kotlin.comparisons  getValue kotlin.comparisons  gson kotlin.comparisons  lazy kotlin.comparisons  longArrayOf kotlin.comparisons  
mutableListOf kotlin.comparisons  prefs kotlin.comparisons  provideDelegate kotlin.comparisons  screenStateTimeTracker kotlin.comparisons  withContext kotlin.comparisons  SuspendFunction1 kotlin.coroutines  Activity 	kotlin.io  ActivityAnimationBinding 	kotlin.io  ActivityChargingOverlayBinding 	kotlin.io  ActivityEnterPasswordBinding 	kotlin.io  ActivityMainBinding 	kotlin.io  ActivitySplashBinding 	kotlin.io  ActivityStartingBinding 	kotlin.io  Class 	kotlin.io  Context 	kotlin.io  DialogSelectBatteryAlarmBinding 	kotlin.io  "DialogSelectBatteryAlarmLowBinding 	kotlin.io  Dispatchers 	kotlin.io  FlowPreview 	kotlin.io  FragmentAnimationGridBinding 	kotlin.io  FragmentChargeBinding 	kotlin.io  FragmentHealthBinding 	kotlin.io  FragmentSettingsBinding 	kotlin.io  KEY_ANIMATION_OVERLAY_ENABLED 	kotlin.io  "KEY_ANIMATION_OVERLAY_TIME_ENABLED 	kotlin.io  KEY_ANTI_THIEF_ALERT_ACTIVE 	kotlin.io  KEY_ANTI_THIEF_ENABLED 	kotlin.io  KEY_ANTI_THIEF_PASSWORD 	kotlin.io  KEY_ANTI_THIEF_SOUND_ENABLED 	kotlin.io  KEY_CHARGE_ALARM_ENABLED 	kotlin.io  KEY_CHARGE_NOTIFICATION_ENABLED 	kotlin.io  KEY_CONSENT_FLOW_USER_GEOGRAPHY 	kotlin.io  KEY_CURRENT_SESSION 	kotlin.io  KEY_DISCHARGE_ALARM_ENABLED 	kotlin.io  KEY_DISCHARGE_ALARM_PERCENT 	kotlin.io  "KEY_DISCHARGE_NOTIFICATION_ENABLED 	kotlin.io  KEY_DONT_DISTURB_CHARGE_ENABLED 	kotlin.io  !KEY_DONT_DISTURB_CHARGE_FROM_TIME 	kotlin.io  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME 	kotlin.io  "KEY_DONT_DISTURB_DISCHARGE_ENABLED 	kotlin.io  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME 	kotlin.io  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME 	kotlin.io  KEY_LANGUAGE 	kotlin.io  KEY_NOTIFY_FULL_CHARGE_ENABLED 	kotlin.io  KEY_PRIVACY_POLICY_ACCEPTED 	kotlin.io  KEY_SCREEN_OFF_RATE 	kotlin.io  KEY_SCREEN_ON_RATE 	kotlin.io  KEY_SHOWED_START_PAGE 	kotlin.io  KEY_VIBRATION_CHARGE_ENABLED 	kotlin.io  KEY_VIBRATION_DISCHARGE_ENABLED 	kotlin.io  KEY_VIBRATION_ENABLED 	kotlin.io  KEY_VIDEO_PATH 	kotlin.io  	Lifecycle 	kotlin.io  Log 	kotlin.io  NewFragmentDischargeBinding 	kotlin.io  
PATTERN_CLICK 	kotlin.io  PATTERN_DOUBLE_CLICK 	kotlin.io  
PATTERN_ERROR 	kotlin.io  PATTERN_SUCCESS 	kotlin.io  PATTERN_TICK 	kotlin.io  PATTERN_WARNING 	kotlin.io  
PREFS_NAME 	kotlin.io  Pair 	kotlin.io  ReplaceWith 	kotlin.io  Runnable 	kotlin.io  ScreenStateTimeTracker 	kotlin.io  SingletonComponent 	kotlin.io  TAG 	kotlin.io  UnstableApi 	kotlin.io  android 	kotlin.io  com 	kotlin.io  delay 	kotlin.io  distinctUntilChanged 	kotlin.io  flow 	kotlin.io  getValue 	kotlin.io  gson 	kotlin.io  lazy 	kotlin.io  longArrayOf 	kotlin.io  
mutableListOf 	kotlin.io  prefs 	kotlin.io  provideDelegate 	kotlin.io  screenStateTimeTracker 	kotlin.io  withContext 	kotlin.io  Activity 
kotlin.jvm  ActivityAnimationBinding 
kotlin.jvm  ActivityChargingOverlayBinding 
kotlin.jvm  ActivityEnterPasswordBinding 
kotlin.jvm  ActivityMainBinding 
kotlin.jvm  ActivitySplashBinding 
kotlin.jvm  ActivityStartingBinding 
kotlin.jvm  Class 
kotlin.jvm  Context 
kotlin.jvm  DialogSelectBatteryAlarmBinding 
kotlin.jvm  "DialogSelectBatteryAlarmLowBinding 
kotlin.jvm  Dispatchers 
kotlin.jvm  FlowPreview 
kotlin.jvm  FragmentAnimationGridBinding 
kotlin.jvm  FragmentChargeBinding 
kotlin.jvm  FragmentHealthBinding 
kotlin.jvm  FragmentSettingsBinding 
kotlin.jvm  KEY_ANIMATION_OVERLAY_ENABLED 
kotlin.jvm  "KEY_ANIMATION_OVERLAY_TIME_ENABLED 
kotlin.jvm  KEY_ANTI_THIEF_ALERT_ACTIVE 
kotlin.jvm  KEY_ANTI_THIEF_ENABLED 
kotlin.jvm  KEY_ANTI_THIEF_PASSWORD 
kotlin.jvm  KEY_ANTI_THIEF_SOUND_ENABLED 
kotlin.jvm  KEY_CHARGE_ALARM_ENABLED 
kotlin.jvm  KEY_CHARGE_NOTIFICATION_ENABLED 
kotlin.jvm  KEY_CONSENT_FLOW_USER_GEOGRAPHY 
kotlin.jvm  KEY_CURRENT_SESSION 
kotlin.jvm  KEY_DISCHARGE_ALARM_ENABLED 
kotlin.jvm  KEY_DISCHARGE_ALARM_PERCENT 
kotlin.jvm  "KEY_DISCHARGE_NOTIFICATION_ENABLED 
kotlin.jvm  KEY_DONT_DISTURB_CHARGE_ENABLED 
kotlin.jvm  !KEY_DONT_DISTURB_CHARGE_FROM_TIME 
kotlin.jvm  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME 
kotlin.jvm  "KEY_DONT_DISTURB_DISCHARGE_ENABLED 
kotlin.jvm  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME 
kotlin.jvm  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME 
kotlin.jvm  KEY_LANGUAGE 
kotlin.jvm  KEY_NOTIFY_FULL_CHARGE_ENABLED 
kotlin.jvm  KEY_PRIVACY_POLICY_ACCEPTED 
kotlin.jvm  KEY_SCREEN_OFF_RATE 
kotlin.jvm  KEY_SCREEN_ON_RATE 
kotlin.jvm  KEY_SHOWED_START_PAGE 
kotlin.jvm  KEY_VIBRATION_CHARGE_ENABLED 
kotlin.jvm  KEY_VIBRATION_DISCHARGE_ENABLED 
kotlin.jvm  KEY_VIBRATION_ENABLED 
kotlin.jvm  KEY_VIDEO_PATH 
kotlin.jvm  	Lifecycle 
kotlin.jvm  Log 
kotlin.jvm  NewFragmentDischargeBinding 
kotlin.jvm  
PATTERN_CLICK 
kotlin.jvm  PATTERN_DOUBLE_CLICK 
kotlin.jvm  
PATTERN_ERROR 
kotlin.jvm  PATTERN_SUCCESS 
kotlin.jvm  PATTERN_TICK 
kotlin.jvm  PATTERN_WARNING 
kotlin.jvm  
PREFS_NAME 
kotlin.jvm  Pair 
kotlin.jvm  ReplaceWith 
kotlin.jvm  Runnable 
kotlin.jvm  ScreenStateTimeTracker 
kotlin.jvm  SingletonComponent 
kotlin.jvm  TAG 
kotlin.jvm  UnstableApi 
kotlin.jvm  android 
kotlin.jvm  com 
kotlin.jvm  delay 
kotlin.jvm  distinctUntilChanged 
kotlin.jvm  flow 
kotlin.jvm  getValue 
kotlin.jvm  gson 
kotlin.jvm  lazy 
kotlin.jvm  longArrayOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  prefs 
kotlin.jvm  provideDelegate 
kotlin.jvm  screenStateTimeTracker 
kotlin.jvm  withContext 
kotlin.jvm  Activity 
kotlin.ranges  ActivityAnimationBinding 
kotlin.ranges  ActivityChargingOverlayBinding 
kotlin.ranges  ActivityEnterPasswordBinding 
kotlin.ranges  ActivityMainBinding 
kotlin.ranges  ActivitySplashBinding 
kotlin.ranges  ActivityStartingBinding 
kotlin.ranges  Class 
kotlin.ranges  Context 
kotlin.ranges  DialogSelectBatteryAlarmBinding 
kotlin.ranges  "DialogSelectBatteryAlarmLowBinding 
kotlin.ranges  Dispatchers 
kotlin.ranges  FlowPreview 
kotlin.ranges  FragmentAnimationGridBinding 
kotlin.ranges  FragmentChargeBinding 
kotlin.ranges  FragmentHealthBinding 
kotlin.ranges  FragmentSettingsBinding 
kotlin.ranges  KEY_ANIMATION_OVERLAY_ENABLED 
kotlin.ranges  "KEY_ANIMATION_OVERLAY_TIME_ENABLED 
kotlin.ranges  KEY_ANTI_THIEF_ALERT_ACTIVE 
kotlin.ranges  KEY_ANTI_THIEF_ENABLED 
kotlin.ranges  KEY_ANTI_THIEF_PASSWORD 
kotlin.ranges  KEY_ANTI_THIEF_SOUND_ENABLED 
kotlin.ranges  KEY_CHARGE_ALARM_ENABLED 
kotlin.ranges  KEY_CHARGE_NOTIFICATION_ENABLED 
kotlin.ranges  KEY_CONSENT_FLOW_USER_GEOGRAPHY 
kotlin.ranges  KEY_CURRENT_SESSION 
kotlin.ranges  KEY_DISCHARGE_ALARM_ENABLED 
kotlin.ranges  KEY_DISCHARGE_ALARM_PERCENT 
kotlin.ranges  "KEY_DISCHARGE_NOTIFICATION_ENABLED 
kotlin.ranges  KEY_DONT_DISTURB_CHARGE_ENABLED 
kotlin.ranges  !KEY_DONT_DISTURB_CHARGE_FROM_TIME 
kotlin.ranges  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME 
kotlin.ranges  "KEY_DONT_DISTURB_DISCHARGE_ENABLED 
kotlin.ranges  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME 
kotlin.ranges  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME 
kotlin.ranges  KEY_LANGUAGE 
kotlin.ranges  KEY_NOTIFY_FULL_CHARGE_ENABLED 
kotlin.ranges  KEY_PRIVACY_POLICY_ACCEPTED 
kotlin.ranges  KEY_SCREEN_OFF_RATE 
kotlin.ranges  KEY_SCREEN_ON_RATE 
kotlin.ranges  KEY_SHOWED_START_PAGE 
kotlin.ranges  KEY_VIBRATION_CHARGE_ENABLED 
kotlin.ranges  KEY_VIBRATION_DISCHARGE_ENABLED 
kotlin.ranges  KEY_VIBRATION_ENABLED 
kotlin.ranges  KEY_VIDEO_PATH 
kotlin.ranges  	Lifecycle 
kotlin.ranges  Log 
kotlin.ranges  NewFragmentDischargeBinding 
kotlin.ranges  
PATTERN_CLICK 
kotlin.ranges  PATTERN_DOUBLE_CLICK 
kotlin.ranges  
PATTERN_ERROR 
kotlin.ranges  PATTERN_SUCCESS 
kotlin.ranges  PATTERN_TICK 
kotlin.ranges  PATTERN_WARNING 
kotlin.ranges  
PREFS_NAME 
kotlin.ranges  Pair 
kotlin.ranges  ReplaceWith 
kotlin.ranges  Runnable 
kotlin.ranges  ScreenStateTimeTracker 
kotlin.ranges  SingletonComponent 
kotlin.ranges  TAG 
kotlin.ranges  UnstableApi 
kotlin.ranges  android 
kotlin.ranges  com 
kotlin.ranges  delay 
kotlin.ranges  distinctUntilChanged 
kotlin.ranges  flow 
kotlin.ranges  getValue 
kotlin.ranges  gson 
kotlin.ranges  lazy 
kotlin.ranges  longArrayOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  prefs 
kotlin.ranges  provideDelegate 
kotlin.ranges  screenStateTimeTracker 
kotlin.ranges  withContext 
kotlin.ranges  KClass kotlin.reflect  Activity kotlin.sequences  ActivityAnimationBinding kotlin.sequences  ActivityChargingOverlayBinding kotlin.sequences  ActivityEnterPasswordBinding kotlin.sequences  ActivityMainBinding kotlin.sequences  ActivitySplashBinding kotlin.sequences  ActivityStartingBinding kotlin.sequences  Class kotlin.sequences  Context kotlin.sequences  DialogSelectBatteryAlarmBinding kotlin.sequences  "DialogSelectBatteryAlarmLowBinding kotlin.sequences  Dispatchers kotlin.sequences  FlowPreview kotlin.sequences  FragmentAnimationGridBinding kotlin.sequences  FragmentChargeBinding kotlin.sequences  FragmentHealthBinding kotlin.sequences  FragmentSettingsBinding kotlin.sequences  KEY_ANIMATION_OVERLAY_ENABLED kotlin.sequences  "KEY_ANIMATION_OVERLAY_TIME_ENABLED kotlin.sequences  KEY_ANTI_THIEF_ALERT_ACTIVE kotlin.sequences  KEY_ANTI_THIEF_ENABLED kotlin.sequences  KEY_ANTI_THIEF_PASSWORD kotlin.sequences  KEY_ANTI_THIEF_SOUND_ENABLED kotlin.sequences  KEY_CHARGE_ALARM_ENABLED kotlin.sequences  KEY_CHARGE_NOTIFICATION_ENABLED kotlin.sequences  KEY_CONSENT_FLOW_USER_GEOGRAPHY kotlin.sequences  KEY_CURRENT_SESSION kotlin.sequences  KEY_DISCHARGE_ALARM_ENABLED kotlin.sequences  KEY_DISCHARGE_ALARM_PERCENT kotlin.sequences  "KEY_DISCHARGE_NOTIFICATION_ENABLED kotlin.sequences  KEY_DONT_DISTURB_CHARGE_ENABLED kotlin.sequences  !KEY_DONT_DISTURB_CHARGE_FROM_TIME kotlin.sequences  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME kotlin.sequences  "KEY_DONT_DISTURB_DISCHARGE_ENABLED kotlin.sequences  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME kotlin.sequences  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME kotlin.sequences  KEY_LANGUAGE kotlin.sequences  KEY_NOTIFY_FULL_CHARGE_ENABLED kotlin.sequences  KEY_PRIVACY_POLICY_ACCEPTED kotlin.sequences  KEY_SCREEN_OFF_RATE kotlin.sequences  KEY_SCREEN_ON_RATE kotlin.sequences  KEY_SHOWED_START_PAGE kotlin.sequences  KEY_VIBRATION_CHARGE_ENABLED kotlin.sequences  KEY_VIBRATION_DISCHARGE_ENABLED kotlin.sequences  KEY_VIBRATION_ENABLED kotlin.sequences  KEY_VIDEO_PATH kotlin.sequences  	Lifecycle kotlin.sequences  Log kotlin.sequences  NewFragmentDischargeBinding kotlin.sequences  
PATTERN_CLICK kotlin.sequences  PATTERN_DOUBLE_CLICK kotlin.sequences  
PATTERN_ERROR kotlin.sequences  PATTERN_SUCCESS kotlin.sequences  PATTERN_TICK kotlin.sequences  PATTERN_WARNING kotlin.sequences  
PREFS_NAME kotlin.sequences  Pair kotlin.sequences  ReplaceWith kotlin.sequences  Runnable kotlin.sequences  ScreenStateTimeTracker kotlin.sequences  SingletonComponent kotlin.sequences  TAG kotlin.sequences  UnstableApi kotlin.sequences  android kotlin.sequences  com kotlin.sequences  delay kotlin.sequences  distinctUntilChanged kotlin.sequences  flow kotlin.sequences  getValue kotlin.sequences  gson kotlin.sequences  lazy kotlin.sequences  longArrayOf kotlin.sequences  
mutableListOf kotlin.sequences  prefs kotlin.sequences  provideDelegate kotlin.sequences  screenStateTimeTracker kotlin.sequences  withContext kotlin.sequences  Activity kotlin.text  ActivityAnimationBinding kotlin.text  ActivityChargingOverlayBinding kotlin.text  ActivityEnterPasswordBinding kotlin.text  ActivityMainBinding kotlin.text  ActivitySplashBinding kotlin.text  ActivityStartingBinding kotlin.text  Class kotlin.text  Context kotlin.text  DialogSelectBatteryAlarmBinding kotlin.text  "DialogSelectBatteryAlarmLowBinding kotlin.text  Dispatchers kotlin.text  FlowPreview kotlin.text  FragmentAnimationGridBinding kotlin.text  FragmentChargeBinding kotlin.text  FragmentHealthBinding kotlin.text  FragmentSettingsBinding kotlin.text  KEY_ANIMATION_OVERLAY_ENABLED kotlin.text  "KEY_ANIMATION_OVERLAY_TIME_ENABLED kotlin.text  KEY_ANTI_THIEF_ALERT_ACTIVE kotlin.text  KEY_ANTI_THIEF_ENABLED kotlin.text  KEY_ANTI_THIEF_PASSWORD kotlin.text  KEY_ANTI_THIEF_SOUND_ENABLED kotlin.text  KEY_CHARGE_ALARM_ENABLED kotlin.text  KEY_CHARGE_NOTIFICATION_ENABLED kotlin.text  KEY_CONSENT_FLOW_USER_GEOGRAPHY kotlin.text  KEY_CURRENT_SESSION kotlin.text  KEY_DISCHARGE_ALARM_ENABLED kotlin.text  KEY_DISCHARGE_ALARM_PERCENT kotlin.text  "KEY_DISCHARGE_NOTIFICATION_ENABLED kotlin.text  KEY_DONT_DISTURB_CHARGE_ENABLED kotlin.text  !KEY_DONT_DISTURB_CHARGE_FROM_TIME kotlin.text  "KEY_DONT_DISTURB_CHARGE_UNTIL_TIME kotlin.text  "KEY_DONT_DISTURB_DISCHARGE_ENABLED kotlin.text  $KEY_DONT_DISTURB_DISCHARGE_FROM_TIME kotlin.text  %KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME kotlin.text  KEY_LANGUAGE kotlin.text  KEY_NOTIFY_FULL_CHARGE_ENABLED kotlin.text  KEY_PRIVACY_POLICY_ACCEPTED kotlin.text  KEY_SCREEN_OFF_RATE kotlin.text  KEY_SCREEN_ON_RATE kotlin.text  KEY_SHOWED_START_PAGE kotlin.text  KEY_VIBRATION_CHARGE_ENABLED kotlin.text  KEY_VIBRATION_DISCHARGE_ENABLED kotlin.text  KEY_VIBRATION_ENABLED kotlin.text  KEY_VIDEO_PATH kotlin.text  	Lifecycle kotlin.text  Log kotlin.text  NewFragmentDischargeBinding kotlin.text  
PATTERN_CLICK kotlin.text  PATTERN_DOUBLE_CLICK kotlin.text  
PATTERN_ERROR kotlin.text  PATTERN_SUCCESS kotlin.text  PATTERN_TICK kotlin.text  PATTERN_WARNING kotlin.text  
PREFS_NAME kotlin.text  Pair kotlin.text  ReplaceWith kotlin.text  Runnable kotlin.text  ScreenStateTimeTracker kotlin.text  SingletonComponent kotlin.text  TAG kotlin.text  UnstableApi kotlin.text  android kotlin.text  com kotlin.text  delay kotlin.text  distinctUntilChanged kotlin.text  flow kotlin.text  getValue kotlin.text  gson kotlin.text  lazy kotlin.text  longArrayOf kotlin.text  
mutableListOf kotlin.text  prefs kotlin.text  provideDelegate kotlin.text  screenStateTimeTracker kotlin.text  withContext kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  FlowPreview kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  withContext kotlinx.coroutines  KEY_CURRENT_SESSION !kotlinx.coroutines.CoroutineScope  KEY_SCREEN_OFF_RATE !kotlinx.coroutines.CoroutineScope  KEY_SCREEN_ON_RATE !kotlinx.coroutines.CoroutineScope  getGSON !kotlinx.coroutines.CoroutineScope  getGson !kotlinx.coroutines.CoroutineScope  getPREFS !kotlinx.coroutines.CoroutineScope  getPrefs !kotlinx.coroutines.CoroutineScope  gson !kotlinx.coroutines.CoroutineScope  prefs !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  
SharedFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  distinctUntilChanged kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  distinctUntilChanged kotlinx.coroutines.flow.Flow  getDISTINCTUntilChanged kotlinx.coroutines.flow.Flow  getDistinctUntilChanged kotlinx.coroutines.flow.Flow  delay %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  getDELAY %kotlinx.coroutines.flow.FlowCollector  getDelay %kotlinx.coroutines.flow.FlowCollector  getSCREENStateTimeTracker %kotlinx.coroutines.flow.FlowCollector  getScreenStateTimeTracker %kotlinx.coroutines.flow.FlowCollector  screenStateTimeTracker %kotlinx.coroutines.flow.FlowCollector  Bundle android.app.Dialog  Canvas android.graphics  Rect android.graphics  Canvas android.view.View  Canvas android.widget.ProgressBar  AppCompatActivity androidx.appcompat.app  Int (androidx.recyclerview.widget.ListAdapter  	ViewGroup (androidx.recyclerview.widget.ListAdapter  Adapter )androidx.recyclerview.widget.RecyclerView  ItemDecoration )androidx.recyclerview.widget.RecyclerView  State )androidx.recyclerview.widget.RecyclerView  AnimationViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  Rect 8androidx.recyclerview.widget.RecyclerView.ItemDecoration  RecyclerView 8androidx.recyclerview.widget.RecyclerView.ItemDecoration  View 8androidx.recyclerview.widget.RecyclerView.ItemDecoration  PagerAdapter androidx.viewpager.widget  Any &androidx.viewpager.widget.PagerAdapter  Boolean &androidx.viewpager.widget.PagerAdapter  Int &androidx.viewpager.widget.PagerAdapter  View &androidx.viewpager.widget.PagerAdapter  	ViewGroup &androidx.viewpager.widget.PagerAdapter  Bundle 2com.tqhit.battery.one.activity.debug.DebugActivity  Any 'com.tqhit.battery.one.activity.starting  Boolean 'com.tqhit.battery.one.activity.starting  Int 'com.tqhit.battery.one.activity.starting  Any ;com.tqhit.battery.one.activity.starting.StartingViewAdapter  Boolean ;com.tqhit.battery.one.activity.starting.StartingViewAdapter  Int ;com.tqhit.battery.one.activity.starting.StartingViewAdapter  View ;com.tqhit.battery.one.activity.starting.StartingViewAdapter  	ViewGroup ;com.tqhit.battery.one.activity.starting.StartingViewAdapter  Canvas <com.tqhit.battery.one.component.progress.VerticalProgressBar  Rect Scom.tqhit.battery.one.features.emoji.presentation.gallery.GridSpacingItemDecoration  RecyclerView Scom.tqhit.battery.one.features.emoji.presentation.gallery.GridSpacingItemDecoration  View Scom.tqhit.battery.one.features.emoji.presentation.gallery.GridSpacingItemDecoration  Int Ucom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter  	ViewGroup Ucom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter  Bundle Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  Bundle ^com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog.Companion  AnimationViewHolder 5com.tqhit.battery.one.fragment.main.animation.adapter  Int 5com.tqhit.battery.one.fragment.main.animation.adapter  AnimationViewHolder Fcom.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter  Int Fcom.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter  	ViewGroup Fcom.tqhit.battery.one.fragment.main.animation.adapter.AnimationAdapter  CategoryViewHolder Ecom.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter  Int Ecom.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter  	ViewGroup Ecom.tqhit.battery.one.fragment.main.animation.adapter.CategoryAdapter  Rect Ocom.tqhit.battery.one.fragment.main.animation.adapter.GridSpacingItemDecoration  RecyclerView Ocom.tqhit.battery.one.fragment.main.animation.adapter.GridSpacingItemDecoration  View Ocom.tqhit.battery.one.fragment.main.animation.adapter.GridSpacingItemDecoration  String $com.tqhit.battery.one.manager.charge  String 2com.tqhit.battery.one.manager.charge.ChargeSession  String <com.tqhit.battery.one.manager.charge.ChargeSession.Companion  String 'com.tqhit.battery.one.manager.discharge  String 8com.tqhit.battery.one.manager.discharge.DischargeSession  String Bcom.tqhit.battery.one.manager.discharge.DischargeSession.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     