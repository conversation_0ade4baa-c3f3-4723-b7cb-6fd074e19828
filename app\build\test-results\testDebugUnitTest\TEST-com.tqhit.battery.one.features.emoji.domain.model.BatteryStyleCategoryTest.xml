<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" tests="20" skipped="0" failures="0" errors="0" timestamp="2025-06-20T07:45:18" hostname="DESKTOP-KBSUI08" time="0.002">
  <properties/>
  <testcase name="test isFeatured returns true only for HOT category" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test fromString with valid enum name" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test getDisplayText returns formatted text" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test fromString with blank string returns default" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test findByDisplayName with case insensitive match" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test all categories have unique sort orders" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test fromString with empty string returns default" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test CHARACTER category properties" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test findByDisplayName with exact match" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test fromString with display name fallback" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test HEART category properties" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test getMainFilterCategories returns expected categories" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test fromString with null returns default" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test findByDisplayName with non-matching name returns null" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test getDefault returns CHARACTER" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test fromString with invalid value returns default" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test getAllSorted returns categories in sort order" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.001"/>
  <testcase name="test HOT category properties" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test fromString with lowercase enum name" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <testcase name="test all categories have required properties" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
