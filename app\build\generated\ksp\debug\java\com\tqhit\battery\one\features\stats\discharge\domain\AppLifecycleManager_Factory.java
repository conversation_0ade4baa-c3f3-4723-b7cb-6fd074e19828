package com.tqhit.battery.one.features.stats.discharge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppLifecycleManager_Factory implements Factory<AppLifecycleManager> {
  @Override
  public AppLifecycleManager get() {
    return newInstance();
  }

  public static AppLifecycleManager_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static AppLifecycleManager newInstance() {
    return new AppLifecycleManager();
  }

  private static final class InstanceHolder {
    static final AppLifecycleManager_Factory INSTANCE = new AppLifecycleManager_Factory();
  }
}
