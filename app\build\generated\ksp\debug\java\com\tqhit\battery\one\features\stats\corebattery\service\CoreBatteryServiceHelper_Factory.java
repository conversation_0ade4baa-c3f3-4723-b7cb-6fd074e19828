package com.tqhit.battery.one.features.stats.corebattery.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CoreBatteryServiceHelper_Factory implements Factory<CoreBatteryServiceHelper> {
  private final Provider<Context> contextProvider;

  public CoreBatteryServiceHelper_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CoreBatteryServiceHelper get() {
    return newInstance(contextProvider.get());
  }

  public static CoreBatteryServiceHelper_Factory create(Provider<Context> contextProvider) {
    return new CoreBatteryServiceHelper_Factory(contextProvider);
  }

  public static CoreBatteryServiceHelper newInstance(Context context) {
    return new CoreBatteryServiceHelper(context);
  }
}
