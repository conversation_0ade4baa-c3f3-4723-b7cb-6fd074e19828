package com.tqhit.battery.one.fragment.main;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SettingsFragment_MembersInjector implements MembersInjector<SettingsFragment> {
  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public SettingsFragment_MembersInjector(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<PreferencesHelper> preferencesHelperProvider) {
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  public static MembersInjector<SettingsFragment> create(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new SettingsFragment_MembersInjector(applovinInterstitialAdManagerProvider, preferencesHelperProvider);
  }

  @Override
  public void injectMembers(SettingsFragment instance) {
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectPreferencesHelper(instance, preferencesHelperProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.SettingsFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(SettingsFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.SettingsFragment.preferencesHelper")
  public static void injectPreferencesHelper(SettingsFragment instance,
      PreferencesHelper preferencesHelper) {
    instance.preferencesHelper = preferencesHelper;
  }
}
