package com.tqhit.battery.one.features.stats.discharge.presentation;

import android.content.Context;
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager;
import com.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialogFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ActivityContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class InfoButtonManager_Factory implements Factory<InfoButtonManager> {
  private final Provider<Context> contextProvider;

  private final Provider<AppPowerConsumptionDialogFactory> dialogFactoryProvider;

  private final Provider<UsageStatsPermissionManager> permissionManagerProvider;

  public InfoButtonManager_Factory(Provider<Context> contextProvider,
      Provider<AppPowerConsumptionDialogFactory> dialogFactoryProvider,
      Provider<UsageStatsPermissionManager> permissionManagerProvider) {
    this.contextProvider = contextProvider;
    this.dialogFactoryProvider = dialogFactoryProvider;
    this.permissionManagerProvider = permissionManagerProvider;
  }

  @Override
  public InfoButtonManager get() {
    return newInstance(contextProvider.get(), dialogFactoryProvider.get(), permissionManagerProvider.get());
  }

  public static InfoButtonManager_Factory create(Provider<Context> contextProvider,
      Provider<AppPowerConsumptionDialogFactory> dialogFactoryProvider,
      Provider<UsageStatsPermissionManager> permissionManagerProvider) {
    return new InfoButtonManager_Factory(contextProvider, dialogFactoryProvider, permissionManagerProvider);
  }

  public static InfoButtonManager newInstance(Context context,
      AppPowerConsumptionDialogFactory dialogFactory,
      UsageStatsPermissionManager permissionManager) {
    return new InfoButtonManager(context, dialogFactory, permissionManager);
  }
}
