package com.tqhit.battery.one.features.stats.discharge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ScreenTimeValidationService_Factory implements Factory<ScreenTimeValidationService> {
  private final Provider<AppLifecycleManager> appLifecycleManagerProvider;

  public ScreenTimeValidationService_Factory(
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    this.appLifecycleManagerProvider = appLifecycleManagerProvider;
  }

  @Override
  public ScreenTimeValidationService get() {
    return newInstance(appLifecycleManagerProvider.get());
  }

  public static ScreenTimeValidationService_Factory create(
      Provider<AppLifecycleManager> appLifecycleManagerProvider) {
    return new ScreenTimeValidationService_Factory(appLifecycleManagerProvider);
  }

  public static ScreenTimeValidationService newInstance(AppLifecycleManager appLifecycleManager) {
    return new ScreenTimeValidationService(appLifecycleManager);
  }
}
