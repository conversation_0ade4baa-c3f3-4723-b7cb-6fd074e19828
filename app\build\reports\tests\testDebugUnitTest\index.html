<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">118</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">45</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">37.053s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">61%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Packages</a>
</li>
<li>
<a href="#tab2">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html">Phase0IntegrationTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html#Phase 0 - directory structure verification">Phase 0 - directory structure verification</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html">Phase0IntegrationTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html#Phase 0 - emoji module structure is properly created">Phase 0 - emoji module structure is properly created</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html">Phase0IntegrationTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html#Phase 0 - module follows stats architecture pattern">Phase 0 - module follows stats architecture pattern</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html">Phase0IntegrationTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html#Phase 0 - module has correct Hilt annotations">Phase 0 - module has correct Hilt annotations</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test clearCache clears all cached data">test clearCache clears all cached data</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test getAllStyles returns empty list when both remote and local fail">test getAllStyles returns empty list when both remote and local fail</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test getPopularStyles filters correctly">test getPopularStyles filters correctly</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">BatteryStyleRepositoryImplTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html#test getStylesByCategory filters correctly">test getStylesByCategory filters correctly</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest.html">EmojiBatteryDIModuleTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest.html#module class exists and is accessible">module class exists and is accessible</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest.html">EmojiBatteryDIModuleTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest.html#module class is properly configured">module class is properly configured</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest.html">EmojiBatteryDIModuleTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest.html#module follows abstract class pattern">module follows abstract class pattern</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html">GetBatteryStylesUseCaseTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html#getAllStyles returns empty list on exception">getAllStyles returns empty list on exception</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html">GetBatteryStylesUseCaseTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html#getCurrentStyles returns empty list on exception">getCurrentStyles returns empty list on exception</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html">GetBatteryStylesUseCaseTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html#getStylesByCategory returns empty list on exception">getStylesByCategory returns empty list on exception</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html">GetBatteryStylesUseCaseTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html#hasCachedData returns false on exception">hasCachedData returns false on exception</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html">GetBatteryStylesUseCaseTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html#refreshStyles returns false on exception">refreshStyles returns false on exception</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#ClearAllFilters event resets all filters">ClearAllFilters event resets all filters</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#DismissError event clears error message">DismissError event clears error message</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#FilterByCategory event filters styles correctly">FilterByCategory event filters styles correctly</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#LoadInitialData event loads styles">LoadInitialData event loads styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#RefreshData event triggers refresh">RefreshData event triggers refresh</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#SearchStyles event filters by search query">SearchStyles event filters by search query</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#SelectStyle event updates selected style">SelectStyle event updates selected style</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#ToggleShowOnlyFree event filters free styles">ToggleShowOnlyFree event filters free styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#ToggleShowOnlyPopular event filters popular styles">ToggleShowOnlyPopular event filters popular styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#ToggleShowOnlyPremium event filters premium styles">ToggleShowOnlyPremium event filters premium styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#battery state changes are handled correctly">battery state changes are handled correctly</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#error handling works correctly">error handling works correctly</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#initial state is correct">initial state is correct</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#multiple filter combinations work correctly">multiple filter combinations work correctly</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#action button is hidden for free styles">action button is hidden for free styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#action button is visible for premium styles">action button is visible for premium styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#adapter creates correct number of view holders">adapter creates correct number of view holders</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#adapter handles empty list correctly">adapter handles empty list correctly</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#adapter handles list updates correctly">adapter handles list updates correctly</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#adapter returns correct item at position">adapter returns correct item at position</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#category text includes premium status">category text includes premium status</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#popular badge is hidden for non-popular styles">popular badge is hidden for non-popular styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#popular badge is visible for popular styles">popular badge is visible for popular styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#premium badge is hidden for free styles">premium badge is hidden for free styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#premium badge is visible for premium styles">premium badge is visible for premium styles</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#premium unlock click triggers callback">premium unlock click triggers callback</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#style click triggers callback">style click triggers callback</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#style long click triggers callback">style long click triggers callback</a>
</li>
<li>
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">BatteryStyleAdapterTest</a>.
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html#view holder binds style data correctly">view holder binds style data correctly</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="failures">
<a href="packages/com.tqhit.battery.one.features.emoji.html">com.tqhit.battery.one.features.emoji</a>
</td>
<td>4</td>
<td>4</td>
<td>0</td>
<td>34.245s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.tqhit.battery.one.features.emoji.data.repository.html">com.tqhit.battery.one.features.emoji.data.repository</a>
</td>
<td>14</td>
<td>4</td>
<td>0</td>
<td>1.690s</td>
<td class="failures">71%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.tqhit.battery.one.features.emoji.di.html">com.tqhit.battery.one.features.emoji.di</a>
</td>
<td>3</td>
<td>3</td>
<td>0</td>
<td>0.128s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.tqhit.battery.one.features.emoji.domain.model.html">com.tqhit.battery.one.features.emoji.domain.model</a>
</td>
<td>49</td>
<td>0</td>
<td>0</td>
<td>0.003s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.tqhit.battery.one.features.emoji.domain.use_case.html">com.tqhit.battery.one.features.emoji.domain.use_case</a>
</td>
<td>19</td>
<td>5</td>
<td>0</td>
<td>0.379s</td>
<td class="failures">73%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.tqhit.battery.one.features.emoji.presentation.gallery.html">com.tqhit.battery.one.features.emoji.presentation.gallery</a>
</td>
<td>14</td>
<td>14</td>
<td>0</td>
<td>0.250s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.html">com.tqhit.battery.one.features.emoji.presentation.gallery.adapter</a>
</td>
<td>15</td>
<td>15</td>
<td>0</td>
<td>0.358s</td>
<td class="failures">0%</td>
</tr>
</tbody>
</table>
</div>
<div id="tab2" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="failures">
<a href="classes/com.tqhit.battery.one.features.emoji.Phase0IntegrationTest.html">com.tqhit.battery.one.features.emoji.Phase0IntegrationTest</a>
</td>
<td>4</td>
<td>4</td>
<td>0</td>
<td>34.245s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest.html">com.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest</a>
</td>
<td>14</td>
<td>4</td>
<td>0</td>
<td>1.690s</td>
<td class="failures">71%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest.html">com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest</a>
</td>
<td>3</td>
<td>3</td>
<td>0</td>
<td>0.128s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest.html">com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest</a>
</td>
<td>20</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest.html">com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest</a>
</td>
<td>16</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest.html">com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest</a>
</td>
<td>13</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest.html">com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest</a>
</td>
<td>19</td>
<td>5</td>
<td>0</td>
<td>0.379s</td>
<td class="failures">73%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest</a>
</td>
<td>14</td>
<td>14</td>
<td>0</td>
<td>0.250s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest.html">com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest</a>
</td>
<td>15</td>
<td>15</td>
<td>0</td>
<td>0.358s</td>
<td class="failures">0%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jun 20, 2025, 2:45:19 PM</p>
</div>
</div>
</body>
</html>
