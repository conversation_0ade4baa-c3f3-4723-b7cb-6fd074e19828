package com.tqhit.battery.one.features.charge.presentation

import android.content.Context
import com.tqhit.battery.one.features.stats.charge.data.StatsChargeStatus
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository
import com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase
import com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel
import com.tqhit.battery.one.repository.AppRepository
import io.mockk.every
import io.mockk.mockk
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog
import java.lang.reflect.Method

@RunWith(RobolectricTestRunner::class)
@Config(manifest = Config.NONE, sdk = [28])
class NewChargeViewModelTest {

    // Mocks
    private lateinit var appRepository: AppRepository
    private lateinit var chargeDataRepository: ChargeDataRepository
    private lateinit var manageChargeSessionUseCase: ManageChargeSessionUseCase
    private lateinit var calculateChargeEstimatesUseCase: CalculateChargeEstimatesUseCase
    private lateinit var context: Context
    private lateinit var viewModel: NewChargeViewModel
    
    @Before
    fun setup() {
        // Configure Shadow Log to avoid Log.d issues
        ShadowLog.stream = System.out
        
        // Initialize mocks
        appRepository = mockk(relaxed = true)
        chargeDataRepository = mockk(relaxed = true)
        manageChargeSessionUseCase = mockk(relaxed = true)
        calculateChargeEstimatesUseCase = mockk(relaxed = true)
        context = mockk(relaxed = true)
        
        // Create the view model
        viewModel = NewChargeViewModel(
            appRepository,
            chargeDataRepository,
            manageChargeSessionUseCase,
            calculateChargeEstimatesUseCase,
            context
        )
    }
    
    @Test
    fun test_charging_positiveCurrentAndVoltage_calculatesCorrectPowerText() {
        // Create a test NewChargeStatus with positive current and voltage
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 2000000L, // 2A
            voltageMillivolts = 5000, // 5V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the power text is calculated correctly: (2A * 5V = 10W)
        assertEquals("10.00 W", uiState.powerText)
    }
    
    @Test
    fun test_charging_zeroCurrent_showsDashPowerText() {
        // Create a test NewChargeStatus with zero current
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 0L,
            voltageMillivolts = 5000, // 5V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the power text shows a dash
        assertEquals("-", uiState.powerText)
    }
    
    @Test
    fun test_charging_zeroVoltage_showsDashPowerText() {
        // Create a test NewChargeStatus with zero voltage
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1000000L, // 1A
            voltageMillivolts = 0,
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the power text shows a dash
        assertEquals("-", uiState.powerText)
    }
    
    @Test
    fun test_charging_verySmallCurrentAndVoltage_calculatesCorrectPowerText() {
        // Create a test NewChargeStatus with very small current and voltage
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1000L, // 1mA
            voltageMillivolts = 1000, // 1V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the power text is calculated correctly: (1mA * 1V = 0.001W), which should be "0.00 W"
        assertEquals("0.00 W", uiState.powerText)
    }
    
    @Test
    fun test_discharging_negativeCurrent_calculatesNegativePowerText() {
        // Create a test NewChargeStatus with negative current (discharging)
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -500000L, // -0.5A
            voltageMillivolts = 3800, // 3.8V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the power text is calculated correctly: (-0.5A * 3.8V = -1.9W)
        assertEquals("-1.90 W", uiState.powerText)
    }
    
    @Test
    fun test_notCharging_butValidCurrentVoltage_calculatesPowerText() {
        // Create a test NewChargeStatus with not charging but valid current and voltage
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = 100000L, // 0.1A
            voltageMillivolts = 4000, // 4V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the power text is calculated correctly: (0.1A * 4V = 0.4W)
        assertEquals("0.40 W", uiState.powerText)
    }
    
    @Test
    fun test_amperage_formatting_positive() {
        // Create a test NewChargeStatus with positive current
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1500000L, // 1.5A
            voltageMillivolts = 5000, // 5V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the amperage text is formatted correctly
        assertEquals("1500.0 mA", uiState.amperageText)
    }

    @Test
    fun test_amperage_formatting_negative() {
        // Create a test NewChargeStatus with negative current
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -500000L, // -0.5A
            voltageMillivolts = 3800, // 3.8V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the amperage text is formatted correctly
        assertEquals("-500.0 mA", uiState.amperageText)
    }

    @Test
    fun test_voltage_formatting() {
        // Create a test NewChargeStatus with standard voltage
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1000000L, // 1A
            voltageMillivolts = 4200, // 4.2V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the voltage text is formatted correctly
        assertEquals("4.2 V", uiState.voltageText)
    }

    @Test
    fun test_temperature_formatting() {
        // Create a test NewChargeStatus with standard temperature
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 1000000L, // 1A
            voltageMillivolts = 4200, // 4.2V
            temperatureCelsius = 35.5f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the temperature text is formatted correctly
        assertEquals("35.5°C", uiState.temperatureText)
    }

    @Test
    fun test_ui_stats_consistency_with_small_current() {
        // Create a test NewChargeStatus with a small current (less than 1000)
        // This would be scaled up by 1000 in the AndroidBatteryStatsDataSource
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = true,
            pluggedSource = 1,
            currentMicroAmperes = 500000L, // Simulating already scaled up from 500mA
            voltageMillivolts = 5000, // 5V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify all the stats are consistent with the input values
        assertEquals("5.0 V", uiState.voltageText)
        assertEquals("500.0 mA", uiState.amperageText)
        assertEquals("2.50 W", uiState.powerText) // 500mA * 5V = 2.5W
        assertEquals("25.0°C", uiState.temperatureText)
    }

    @Test
    fun test_small_and_negative_current_power_calculation() {
        // Create a test NewChargeStatus with a small negative current
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -500000L, // -500mA (after scaling)
            voltageMillivolts = 3800, // 3.8V
            temperatureCelsius = 25.0f
        )
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the power calculation with the small current (should be -500mA * 3.8V = -1.9W)
        assertEquals("-1.90 W", uiState.powerText)
    }

    @Test
    fun test_dischargeRateText_calculation() {
        // Create a test NewChargeStatus with negative current while discharging
        val status = NewChargeStatus(
            percentage = 50,
            isCharging = false,
            pluggedSource = 0,
            currentMicroAmperes = -400000L, // -400mA
            voltageMillivolts = 3700, // 3.7V
            temperatureCelsius = 25.0f
        )
        
        // Mock battery capacity for discharge rate calculation
        every { appRepository.getBatteryCapacity() } returns 4000
        
        // Use reflection to access the private createUiState method
        val createUiStateMethod = getPrivateCreateUiStateMethod()
        val uiState = createUiStateMethod.invoke(viewModel, status, null, null) as ChargeUiState
        
        // Verify the discharge rate calculation
        // Discharge rate should be (400mA / 4000mAh) * 100 = 10%/h
        assertTrue(uiState.dischargeRateText.contains("-10.0%/h"))
    }
    
    /**
     * Helper method to get access to the private createUiState method
     */
    private fun getPrivateCreateUiStateMethod(): Method {
        val method = NewChargeViewModel::class.java.getDeclaredMethod(
            "createUiState",
            NewChargeStatus::class.java,
            com.tqhit.battery.one.features.charge.data.model.NewChargeSession::class.java,
            com.tqhit.battery.one.features.charge.data.model.ChargeEstimates::class.java
        )
        method.isAccessible = true
        return method
    }
} 