package com.tqhit.battery.one.features.emoji.domain.use_case

import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.model.UserPreferences
import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for loading and observing user customization data.
 * 
 * Provides reactive access to customization data with business logic for
 * data enrichment, validation, and status calculation. Combines data from
 * multiple repositories to provide complete customization state.
 * 
 * Key responsibilities:
 * - Provide reactive streams of customization data
 * - Enrich customization data with style information
 * - Calculate feature readiness and status
 * - Handle data validation and error recovery
 * - Provide filtered and processed data views
 */
@Singleton
class LoadCustomizationUseCase @Inject constructor(
    private val customizationRepository: CustomizationRepository,
    private val batteryStyleRepository: BatteryStyleRepository
) {
    
    companion object {
        private const val TAG = "LoadCustomizationUseCase"
    }
    
    /**
     * Reactive stream of complete user customization state.
     * Includes enriched data with style information and status calculation.
     */
    val userCustomizationFlow: Flow<EnrichedUserCustomization> = combine(
        customizationRepository.userCustomizationFlow,
        batteryStyleRepository.batteryStylesFlow
    ) { userCustomization, availableStyles ->
        try {
            enrichUserCustomization(userCustomization, availableStyles)
        } catch (e: Exception) {
            Log.e(TAG, "Error enriching user customization", e)
            EnrichedUserCustomization(
                userCustomization = userCustomization,
                selectedStyle = null,
                isSelectedStyleAvailable = false,
                featureStatus = userCustomization.getFeatureStatus(),
                availableStylesCount = availableStyles.size
            )
        }
    }
    
    /**
     * Reactive stream of customization configuration only.
     */
    val customizationConfigFlow: Flow<CustomizationConfig> = customizationRepository.customizationConfigFlow
    
    /**
     * Reactive stream of user preferences only.
     */
    val userPreferencesFlow: Flow<UserPreferences> = customizationRepository.userPreferencesFlow
    
    /**
     * Reactive stream of feature readiness status.
     */
    val featureStatusFlow: Flow<FeatureStatus> = userCustomizationFlow.map { enriched ->
        enriched.featureStatus
    }
    
    /**
     * Reactive stream indicating if the feature is ready to use.
     */
    val isFeatureReadyFlow: Flow<Boolean> = userCustomizationFlow.map { enriched ->
        enriched.userCustomization.isFeatureReady()
    }
    
    /**
     * Gets the current customization configuration synchronously.
     * 
     * @return Current CustomizationConfig or default if none exists
     */
    suspend fun getCurrentCustomizationConfig(): CustomizationConfig {
        return try {
            Log.d(TAG, "LOAD_CONFIG: Loading current customization config")
            val config = customizationRepository.getCurrentCustomizationConfig()
            Log.d(TAG, "LOAD_CONFIG: Loaded config for style: ${config.selectedStyleId}")
            config
        } catch (e: Exception) {
            Log.e(TAG, "LOAD_CONFIG: Exception loading customization config", e)
            CustomizationConfig.createDefault()
        }
    }
    
    /**
     * Gets the current user customization state synchronously.
     * 
     * @return Current UserCustomization or default if none exists
     */
    suspend fun getCurrentUserCustomization(): UserCustomization {
        return try {
            Log.d(TAG, "LOAD_USER: Loading current user customization")
            val userCustomization = customizationRepository.getCurrentUserCustomization()
            Log.d(TAG, "LOAD_USER: Loaded user customization with feature enabled: ${userCustomization.customizationConfig.isFeatureEnabled}")
            userCustomization
        } catch (e: Exception) {
            Log.e(TAG, "LOAD_USER: Exception loading user customization", e)
            UserCustomization.createDefault()
        }
    }
    
    /**
     * Gets the current enriched user customization with style information.
     * 
     * @return EnrichedUserCustomization with complete data
     */
    suspend fun getCurrentEnrichedUserCustomization(): EnrichedUserCustomization {
        return try {
            Log.d(TAG, "LOAD_ENRICHED: Loading enriched user customization")
            val userCustomization = getCurrentUserCustomization()
            val availableStyles = batteryStyleRepository.batteryStylesFlow.first()
            
            val enriched = enrichUserCustomization(userCustomization, availableStyles)
            Log.d(TAG, "LOAD_ENRICHED: Loaded enriched customization with ${availableStyles.size} available styles")
            enriched
        } catch (e: Exception) {
            Log.e(TAG, "LOAD_ENRICHED: Exception loading enriched customization", e)
            val userCustomization = UserCustomization.createDefault()
            EnrichedUserCustomization(
                userCustomization = userCustomization,
                selectedStyle = null,
                isSelectedStyleAvailable = false,
                featureStatus = userCustomization.getFeatureStatus(),
                availableStylesCount = 0
            )
        }
    }
    
    /**
     * Gets the currently selected battery style if available.
     * 
     * @return BatteryStyle if selected and available, null otherwise
     */
    suspend fun getCurrentSelectedStyle(): BatteryStyle? {
        return try {
            Log.d(TAG, "LOAD_SELECTED_STYLE: Loading currently selected style")
            val config = getCurrentCustomizationConfig()
            
            if (config.selectedStyleId.isBlank()) {
                Log.d(TAG, "LOAD_SELECTED_STYLE: No style selected")
                return null
            }
            
            val availableStyles = batteryStyleRepository.batteryStylesFlow.first()
            val selectedStyle = availableStyles.find { it.id == config.selectedStyleId }
            
            if (selectedStyle != null) {
                Log.d(TAG, "LOAD_SELECTED_STYLE: Found selected style: ${selectedStyle.name}")
            } else {
                Log.w(TAG, "LOAD_SELECTED_STYLE: Selected style not found: ${config.selectedStyleId}")
            }
            
            selectedStyle
        } catch (e: Exception) {
            Log.e(TAG, "LOAD_SELECTED_STYLE: Exception loading selected style", e)
            null
        }
    }
    
    /**
     * Gets user preferences synchronously.
     * 
     * @return Current UserPreferences or default if none exists
     */
    suspend fun getCurrentUserPreferences(): UserPreferences {
        return try {
            Log.d(TAG, "LOAD_PREFERENCES: Loading user preferences")
            customizationRepository.userPreferencesFlow.first()
        } catch (e: Exception) {
            Log.e(TAG, "LOAD_PREFERENCES: Exception loading user preferences", e)
            UserPreferences.createDefault()
        }
    }
    
    /**
     * Checks if the feature is currently ready to use.
     * 
     * @return true if feature is ready, false otherwise
     */
    suspend fun isFeatureReady(): Boolean {
        return try {
            val userCustomization = getCurrentUserCustomization()
            val isReady = userCustomization.isFeatureReady()
            Log.d(TAG, "FEATURE_READY_CHECK: Feature ready status: $isReady")
            isReady
        } catch (e: Exception) {
            Log.e(TAG, "FEATURE_READY_CHECK: Exception checking feature readiness", e)
            false
        }
    }
    
    /**
     * Gets the current feature status.
     * 
     * @return FeatureStatus indicating current state
     */
    suspend fun getFeatureStatus(): FeatureStatus {
        return try {
            val userCustomization = getCurrentUserCustomization()
            val status = userCustomization.getFeatureStatus()
            Log.d(TAG, "FEATURE_STATUS: Current status: ${status.displayName}")
            status
        } catch (e: Exception) {
            Log.e(TAG, "FEATURE_STATUS: Exception getting feature status", e)
            FeatureStatus.CONFIGURATION_INVALID
        }
    }
    
    /**
     * Validates the current configuration and returns any issues found.
     * 
     * @return List of validation issues, empty if configuration is valid
     */
    suspend fun validateCurrentConfiguration(): List<String> {
        return try {
            Log.d(TAG, "VALIDATE: Validating current configuration")
            val result = customizationRepository.validateAndFixConfiguration()
            
            if (result.isSuccess) {
                val issues = result.getOrNull() ?: emptyList()
                Log.d(TAG, "VALIDATE: Validation completed with ${issues.size} issues")
                issues
            } else {
                Log.e(TAG, "VALIDATE: Validation failed", result.exceptionOrNull())
                listOf("Configuration validation failed: ${result.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "VALIDATE: Exception during validation", e)
            listOf("Configuration validation error: ${e.message}")
        }
    }
    
    /**
     * Enriches user customization with additional data and calculations.
     */
    private fun enrichUserCustomization(
        userCustomization: UserCustomization,
        availableStyles: List<BatteryStyle>
    ): EnrichedUserCustomization {
        val config = userCustomization.customizationConfig
        val selectedStyle = if (config.selectedStyleId.isNotBlank()) {
            availableStyles.find { it.id == config.selectedStyleId }
        } else {
            null
        }
        
        val isSelectedStyleAvailable = selectedStyle != null
        val featureStatus = userCustomization.getFeatureStatus()
        
        return EnrichedUserCustomization(
            userCustomization = userCustomization,
            selectedStyle = selectedStyle,
            isSelectedStyleAvailable = isSelectedStyleAvailable,
            featureStatus = featureStatus,
            availableStylesCount = availableStyles.size
        )
    }
}

/**
 * Enriched user customization data with additional computed information.
 * 
 * @param userCustomization The base user customization data
 * @param selectedStyle The currently selected battery style (if available)
 * @param isSelectedStyleAvailable Whether the selected style is still available
 * @param featureStatus Current feature status
 * @param availableStylesCount Total number of available styles
 */
data class EnrichedUserCustomization(
    val userCustomization: UserCustomization,
    val selectedStyle: BatteryStyle?,
    val isSelectedStyleAvailable: Boolean,
    val featureStatus: FeatureStatus,
    val availableStylesCount: Int
) {
    
    /**
     * Checks if the configuration needs attention (missing style, invalid config, etc.).
     */
    fun needsAttention(): Boolean {
        return !isSelectedStyleAvailable ||
               !userCustomization.customizationConfig.isValid() ||
               !featureStatus.isOperational
    }
    
    /**
     * Gets a user-friendly status message.
     */
    fun getStatusMessage(): String {
        return when {
            !isSelectedStyleAvailable -> "Selected style is no longer available"
            !userCustomization.customizationConfig.isValid() -> "Configuration is invalid"
            else -> featureStatus.getDescription()
        }
    }
}
