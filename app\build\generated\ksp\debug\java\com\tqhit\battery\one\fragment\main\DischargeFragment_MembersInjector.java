package com.tqhit.battery.one.fragment.main;

import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.service.VibrationService;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DischargeFragment_MembersInjector implements MembersInjector<DischargeFragment> {
  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<VibrationService> vibrationServiceProvider;

  public DischargeFragment_MembersInjector(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<VibrationService> vibrationServiceProvider) {
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.vibrationServiceProvider = vibrationServiceProvider;
  }

  public static MembersInjector<DischargeFragment> create(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<VibrationService> vibrationServiceProvider) {
    return new DischargeFragment_MembersInjector(applovinInterstitialAdManagerProvider, vibrationServiceProvider);
  }

  @Override
  public void injectMembers(DischargeFragment instance) {
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectVibrationService(instance, vibrationServiceProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.DischargeFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(DischargeFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.DischargeFragment.vibrationService")
  public static void injectVibrationService(DischargeFragment instance,
      VibrationService vibrationService) {
    instance.vibrationService = vibrationService;
  }
}
