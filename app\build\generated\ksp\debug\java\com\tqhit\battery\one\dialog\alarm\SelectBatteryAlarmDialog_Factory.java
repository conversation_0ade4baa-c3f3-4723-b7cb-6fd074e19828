package com.tqhit.battery.one.dialog.alarm;

import android.content.Context;
import androidx.activity.result.ActivityResultLauncher;
import com.tqhit.battery.one.service.VibrationService;
import com.tqhit.battery.one.viewmodel.AppViewModel;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ActivityContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SelectBatteryAlarmDialog_Factory implements Factory<SelectBatteryAlarmDialog> {
  private final Provider<Context> contextProvider;

  private final Provider<ActivityResultLauncher<String>> permissionLauncherProvider;

  private final Provider<AppViewModel> appViewModelProvider;

  private final Provider<VibrationService> vibrationServiceProvider;

  public SelectBatteryAlarmDialog_Factory(Provider<Context> contextProvider,
      Provider<ActivityResultLauncher<String>> permissionLauncherProvider,
      Provider<AppViewModel> appViewModelProvider,
      Provider<VibrationService> vibrationServiceProvider) {
    this.contextProvider = contextProvider;
    this.permissionLauncherProvider = permissionLauncherProvider;
    this.appViewModelProvider = appViewModelProvider;
    this.vibrationServiceProvider = vibrationServiceProvider;
  }

  @Override
  public SelectBatteryAlarmDialog get() {
    return newInstance(contextProvider.get(), permissionLauncherProvider.get(), appViewModelProvider.get(), vibrationServiceProvider.get());
  }

  public static SelectBatteryAlarmDialog_Factory create(Provider<Context> contextProvider,
      Provider<ActivityResultLauncher<String>> permissionLauncherProvider,
      Provider<AppViewModel> appViewModelProvider,
      Provider<VibrationService> vibrationServiceProvider) {
    return new SelectBatteryAlarmDialog_Factory(contextProvider, permissionLauncherProvider, appViewModelProvider, vibrationServiceProvider);
  }

  public static SelectBatteryAlarmDialog newInstance(Context context,
      ActivityResultLauncher<String> permissionLauncher, AppViewModel appViewModel,
      VibrationService vibrationService) {
    return new SelectBatteryAlarmDialog(context, permissionLauncher, appViewModel, vibrationService);
  }
}
