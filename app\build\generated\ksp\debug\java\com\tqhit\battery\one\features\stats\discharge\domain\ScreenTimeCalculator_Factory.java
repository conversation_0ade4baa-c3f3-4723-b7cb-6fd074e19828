package com.tqhit.battery.one.features.stats.discharge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ScreenTimeCalculator_Factory implements Factory<ScreenTimeCalculator> {
  private final Provider<TimeConverter> timeConverterProvider;

  public ScreenTimeCalculator_Factory(Provider<TimeConverter> timeConverterProvider) {
    this.timeConverterProvider = timeConverterProvider;
  }

  @Override
  public ScreenTimeCalculator get() {
    return newInstance(timeConverterProvider.get());
  }

  public static ScreenTimeCalculator_Factory create(Provider<TimeConverter> timeConverterProvider) {
    return new ScreenTimeCalculator_Factory(timeConverterProvider);
  }

  public static ScreenTimeCalculator newInstance(TimeConverter timeConverter) {
    return new ScreenTimeCalculator(timeConverter);
  }
}
