package com.tqhit.battery.one.fragment.main;

import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.service.VibrationService;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ChargeFragment_MembersInjector implements MembersInjector<ChargeFragment> {
  private final Provider<VibrationService> vibrationServiceProvider;

  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  public ChargeFragment_MembersInjector(Provider<VibrationService> vibrationServiceProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider) {
    this.vibrationServiceProvider = vibrationServiceProvider;
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
  }

  public static MembersInjector<ChargeFragment> create(
      Provider<VibrationService> vibrationServiceProvider,
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider) {
    return new ChargeFragment_MembersInjector(vibrationServiceProvider, applovinInterstitialAdManagerProvider);
  }

  @Override
  public void injectMembers(ChargeFragment instance) {
    injectVibrationService(instance, vibrationServiceProvider.get());
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.ChargeFragment.vibrationService")
  public static void injectVibrationService(ChargeFragment instance,
      VibrationService vibrationService) {
    instance.vibrationService = vibrationService;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.ChargeFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(ChargeFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }
}
