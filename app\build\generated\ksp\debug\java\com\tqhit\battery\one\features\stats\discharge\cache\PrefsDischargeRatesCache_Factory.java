package com.tqhit.battery.one.features.stats.discharge.cache;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class PrefsDischargeRatesCache_Factory implements Factory<PrefsDischargeRatesCache> {
  private final Provider<Context> contextProvider;

  public PrefsDischargeRatesCache_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public PrefsDischargeRatesCache get() {
    return newInstance(contextProvider.get());
  }

  public static PrefsDischargeRatesCache_Factory create(Provider<Context> contextProvider) {
    return new PrefsDischargeRatesCache_Factory(contextProvider);
  }

  public static PrefsDischargeRatesCache newInstance(Context context) {
    return new PrefsDischargeRatesCache(context);
  }
}
