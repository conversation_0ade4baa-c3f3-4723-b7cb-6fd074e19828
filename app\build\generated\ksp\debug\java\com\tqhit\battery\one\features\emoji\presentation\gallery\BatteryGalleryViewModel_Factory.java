package com.tqhit.battery.one.features.emoji.presentation.gallery;

import com.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryGalleryViewModel_Factory implements Factory<BatteryGalleryViewModel> {
  private final Provider<GetBatteryStylesUseCase> getBatteryStylesUseCaseProvider;

  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  public BatteryGalleryViewModel_Factory(
      Provider<GetBatteryStylesUseCase> getBatteryStylesUseCaseProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    this.getBatteryStylesUseCaseProvider = getBatteryStylesUseCaseProvider;
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @Override
  public BatteryGalleryViewModel get() {
    return newInstance(getBatteryStylesUseCaseProvider.get(), coreBatteryStatsProvider.get());
  }

  public static BatteryGalleryViewModel_Factory create(
      Provider<GetBatteryStylesUseCase> getBatteryStylesUseCaseProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    return new BatteryGalleryViewModel_Factory(getBatteryStylesUseCaseProvider, coreBatteryStatsProvider);
  }

  public static BatteryGalleryViewModel newInstance(GetBatteryStylesUseCase getBatteryStylesUseCase,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    return new BatteryGalleryViewModel(getBatteryStylesUseCase, coreBatteryStatsProvider);
  }
}
