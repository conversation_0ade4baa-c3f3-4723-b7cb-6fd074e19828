package com.tqhit.battery.one.features.emoji.presentation.customize

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.FeatureStatus
import com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.use_case.EnrichedUserCustomization

/**
 * UI state for the customization screen.
 * 
 * Represents the complete state of the customization interface including:
 * - Current customization configuration and preview data
 * - Available styles and selection state
 * - UI interaction states (loading, editing, saving)
 * - Error handling and validation states
 * - Live preview and battery data integration
 * 
 * Follows the MVI pattern established in Phase 2 with immutable state
 * and comprehensive state management for complex UI interactions.
 */
data class CustomizeState(
    // Core customization data
    val currentCustomization: UserCustomization = UserCustomization.createDefault(),
    val enrichedCustomization: EnrichedUserCustomization? = null,
    val selectedStyle: BatteryStyle? = null,
    val availableStyles: List<BatteryStyle> = emptyList(),
    
    // Style selection state
    val availableBatteryStyles: List<BatteryStyle> = emptyList(),
    val availableEmojiStyles: List<BatteryStyle> = emptyList(),
    val selectedBatteryStyleId: String = "",
    val selectedEmojiStyleId: String = "",
    
    // Configuration editing state
    val editingConfig: BatteryStyleConfig = BatteryStyleConfig(),
    val isConfigModified: Boolean = false,
    val previewBatteryLevel: Int = 50, // For live preview
    
    // UI interaction states
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val isLoadingStyles: Boolean = false,
    val showColorPicker: Boolean = false,
    val showPositionSelector: Boolean = false,
    
    // Error and validation states
    val errorMessage: String? = null,
    val validationErrors: List<String> = emptyList(),
    val hasUnsavedChanges: Boolean = false,
    
    // Feature and permission states
    val featureStatus: FeatureStatus = FeatureStatus.CONFIGURATION_INVALID,
    val canApplyChanges: Boolean = false,
    val requiresPermissions: Boolean = false,
    
    // Preview and display states
    val showLivePreview: Boolean = true,
    val previewUpdateTimestamp: Long = 0L,
    val isPreviewLoading: Boolean = false
) {
    
    /**
     * Checks if the screen is in a loading state.
     */
    fun isAnyLoading(): Boolean {
        return isLoading || isSaving || isLoadingStyles || isPreviewLoading
    }
    
    /**
     * Checks if there are any errors to display.
     */
    fun hasErrors(): Boolean {
        return errorMessage != null || validationErrors.isNotEmpty()
    }
    
    /**
     * Gets the current configuration for preview purposes.
     */
    fun getPreviewConfig(): CustomizationConfig {
        return currentCustomization.customizationConfig.copy(
            styleConfig = editingConfig
        )
    }
    
    /**
     * Checks if the current configuration is valid for saving.
     */
    fun canSave(): Boolean {
        return !isAnyLoading() &&
               isConfigModified &&
               validationErrors.isEmpty() &&
               editingConfig.isValid()
    }
    
    /**
     * Checks if the feature can be enabled with current configuration.
     */
    fun canEnableFeature(): Boolean {
        return !requiresPermissions &&
               selectedStyle != null &&
               editingConfig.isValid() &&
               featureStatus != FeatureStatus.PERMISSIONS_REQUIRED
    }
    
    /**
     * Gets the display title for the current style.
     */
    fun getStyleDisplayTitle(): String {
        return selectedStyle?.name ?: "No Style Selected"
    }
    
    /**
     * Gets user-friendly error message for display.
     */
    fun getDisplayErrorMessage(): String? {
        return when {
            errorMessage != null -> errorMessage
            validationErrors.isNotEmpty() -> validationErrors.first()
            else -> null
        }
    }
    
    /**
     * Checks if the style selection is complete.
     */
    fun hasCompleteStyleSelection(): Boolean {
        return selectedBatteryStyleId.isNotBlank() && selectedEmojiStyleId.isNotBlank()
    }
    
    /**
     * Gets the current overlay position for display.
     */
    fun getCurrentOverlayPosition(): OverlayPosition {
        return currentCustomization.customizationConfig.overlayPosition
    }
    
    /**
     * Checks if premium features are being used.
     */
    fun usesPremiumFeatures(): Boolean {
        return selectedStyle?.isPremium == true
    }
    
    /**
     * Creates a copy with updated customization data.
     */
    fun withCustomization(customization: UserCustomization): CustomizeState {
        return copy(
            currentCustomization = customization,
            editingConfig = customization.customizationConfig.styleConfig,
            selectedBatteryStyleId = customization.customizationConfig.selectedStyleId,
            selectedEmojiStyleId = customization.customizationConfig.selectedStyleId,
            featureStatus = customization.getFeatureStatus(),
            requiresPermissions = !customization.hasAllRequiredPermissions(),
            canApplyChanges = customization.isFeatureReady(),
            previewUpdateTimestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * Creates a copy with updated enriched customization data.
     */
    fun withEnrichedCustomization(enriched: EnrichedUserCustomization): CustomizeState {
        return copy(
            enrichedCustomization = enriched,
            selectedStyle = enriched.selectedStyle,
            featureStatus = enriched.featureStatus,
            previewUpdateTimestamp = System.currentTimeMillis()
        ).withCustomization(enriched.userCustomization)
    }
    
    /**
     * Creates a copy with updated available styles.
     */
    fun withAvailableStyles(styles: List<BatteryStyle>): CustomizeState {
        // Separate battery and emoji styles for selection
        val batteryStyles = styles.filter { it.batteryImageUrl.isNotBlank() }
        val emojiStyles = styles.filter { it.emojiImageUrl.isNotBlank() }
        
        return copy(
            availableStyles = styles,
            availableBatteryStyles = batteryStyles,
            availableEmojiStyles = emojiStyles,
            isLoadingStyles = false
        )
    }
    
    /**
     * Creates a copy with updated editing configuration.
     */
    fun withEditingConfig(config: BatteryStyleConfig): CustomizeState {
        val isModified = config != currentCustomization.customizationConfig.styleConfig
        return copy(
            editingConfig = config,
            isConfigModified = isModified,
            hasUnsavedChanges = isModified,
            previewUpdateTimestamp = System.currentTimeMillis()
        )
    }
    
    /**
     * Creates a copy with updated loading states.
     */
    fun withLoadingState(
        isLoading: Boolean = this.isLoading,
        isSaving: Boolean = this.isSaving,
        isLoadingStyles: Boolean = this.isLoadingStyles,
        isPreviewLoading: Boolean = this.isPreviewLoading
    ): CustomizeState {
        return copy(
            isLoading = isLoading,
            isSaving = isSaving,
            isLoadingStyles = isLoadingStyles,
            isPreviewLoading = isPreviewLoading
        )
    }
    
    /**
     * Creates a copy with updated error state.
     */
    fun withError(
        errorMessage: String? = null,
        validationErrors: List<String> = emptyList()
    ): CustomizeState {
        return copy(
            errorMessage = errorMessage,
            validationErrors = validationErrors,
            isLoading = false,
            isSaving = false,
            isPreviewLoading = false
        )
    }
    
    /**
     * Creates a copy with cleared errors.
     */
    fun withClearedErrors(): CustomizeState {
        return copy(
            errorMessage = null,
            validationErrors = emptyList()
        )
    }
    
    /**
     * Creates a copy with updated UI interaction states.
     */
    fun withUIState(
        showColorPicker: Boolean = this.showColorPicker,
        showPositionSelector: Boolean = this.showPositionSelector,
        showLivePreview: Boolean = this.showLivePreview,
        previewBatteryLevel: Int = this.previewBatteryLevel
    ): CustomizeState {
        return copy(
            showColorPicker = showColorPicker,
            showPositionSelector = showPositionSelector,
            showLivePreview = showLivePreview,
            previewBatteryLevel = previewBatteryLevel,
            previewUpdateTimestamp = if (previewBatteryLevel != this.previewBatteryLevel) {
                System.currentTimeMillis()
            } else {
                this.previewUpdateTimestamp
            }
        )
    }
    
    /**
     * Creates a copy with style selection updated.
     */
    fun withStyleSelection(
        batteryStyleId: String = this.selectedBatteryStyleId,
        emojiStyleId: String = this.selectedEmojiStyleId
    ): CustomizeState {
        val hasChanges = batteryStyleId != this.selectedBatteryStyleId || 
                        emojiStyleId != this.selectedEmojiStyleId
        
        return copy(
            selectedBatteryStyleId = batteryStyleId,
            selectedEmojiStyleId = emojiStyleId,
            hasUnsavedChanges = hasChanges || this.hasUnsavedChanges,
            previewUpdateTimestamp = if (hasChanges) System.currentTimeMillis() else this.previewUpdateTimestamp
        )
    }
    
    companion object {
        /**
         * Creates the initial state for the customization screen.
         */
        fun createInitial(): CustomizeState {
            return CustomizeState(
                isLoading = true,
                isLoadingStyles = true,
                showLivePreview = true,
                previewBatteryLevel = 50
            )
        }
    }
}
