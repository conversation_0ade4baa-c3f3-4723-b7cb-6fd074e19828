<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tqhit.battery.one.features.emoji.Phase0IntegrationTest" tests="4" skipped="0" failures="4" errors="0" timestamp="2025-06-20T07:44:41" hostname="DESKTOP-KBSUI08" time="34.246">
  <properties/>
  <testcase name="Phase 0 - emoji module structure is properly created" classname="com.tqhit.battery.one.features.emoji.Phase0IntegrationTest" time="34.153">
    <failure message="java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first." type="java.lang.IllegalStateException">java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</failure>
  </testcase>
  <testcase name="Phase 0 - directory structure verification" classname="com.tqhit.battery.one.features.emoji.Phase0IntegrationTest" time="0.037">
    <failure message="java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first." type="java.lang.IllegalStateException">java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</failure>
  </testcase>
  <testcase name="Phase 0 - module follows stats architecture pattern" classname="com.tqhit.battery.one.features.emoji.Phase0IntegrationTest" time="0.03">
    <failure message="java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first." type="java.lang.IllegalStateException">java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</failure>
  </testcase>
  <testcase name="Phase 0 - module has correct Hilt annotations" classname="com.tqhit.battery.one.features.emoji.Phase0IntegrationTest" time="0.025">
    <failure message="java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first." type="java.lang.IllegalStateException">java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</failure>
  </testcase>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
