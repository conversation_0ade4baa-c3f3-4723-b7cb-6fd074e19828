/ Header Record For PersistentHashMapValueStorage; :app/src/test/java/com/tqhit/battery/one/ExampleUnitTest.ktP Oapp/src/test/java/com/tqhit/battery/one/features/emoji/Phase0IntegrationTest.kti happ/src/test/java/com/tqhit/battery/one/features/emoji/data/repository/BatteryStyleRepositoryImplTest.ktV Uapp/src/test/java/com/tqhit/battery/one/features/emoji/di/EmojiBatteryDIModuleTest.kt` _app/src/test/java/com/tqhit/battery/one/features/emoji/domain/model/BatteryStyleCategoryTest.kt^ ]app/src/test/java/com/tqhit/battery/one/features/emoji/domain/model/BatteryStyleConfigTest.ktX Wapp/src/test/java/com/tqhit/battery/one/features/emoji/domain/model/BatteryStyleTest.ktf eapp/src/test/java/com/tqhit/battery/one/features/emoji/domain/use_case/GetBatteryStylesUseCaseTest.ktk japp/src/test/java/com/tqhit/battery/one/features/emoji/presentation/gallery/BatteryGalleryViewModelTest.kto napp/src/test/java/com/tqhit/battery/one/features/emoji/presentation/gallery/adapter/BatteryStyleAdapterTest.ktS Rapp/src/test/java/com/tqhit/battery/one/features/navigation/NavigationStateTest.ktT Sapp/src/test/java/com/tqhit/battery/one/features/stats/UnifiedBatteryServiceTest.ktj iapp/src/test/java/com/tqhit/battery/one/features/stats/charge/CalculateSimpleChargeEstimateUseCaseTest.kt\ [app/src/test/java/com/tqhit/battery/one/features/stats/charge/PowerCalculationManualTest.ktb aapp/src/test/java/com/tqhit/battery/one/features/stats/charge/StatsChargeSessionStatisticsTest.ktX Wapp/src/test/java/com/tqhit/battery/one/features/stats/charge/StatsChargeSessionTest.ktj iapp/src/test/java/com/tqhit/battery/one/features/stats/charge/StatsChargeViewModelPowerCalculationTest.kt\ [app/src/test/java/com/tqhit/battery/one/features/stats/corebattery/CoreBatteryStatusTest.kt` _app/src/test/java/com/tqhit/battery/one/features/stats/discharge/ScreenTimeGapValidationTest.ktc bapp/src/test/java/com/tqhit/battery/one/features/stats/discharge/domain/AppLifecycleManagerTest.ktc bapp/src/test/java/com/tqhit/battery/one/features/stats/discharge/domain/DischargeCalculatorTest.kti happ/src/test/java/com/tqhit/battery/one/features/stats/discharge/domain/EnhancedScreenTimeTrackerTest.ktf eapp/src/test/java/com/tqhit/battery/one/features/stats/discharge/domain/FullSessionReEstimatorTest.ktk japp/src/test/java/com/tqhit/battery/one/features/stats/discharge/domain/ScreenTimeValidationServiceTest.kt] \app/src/test/java/com/tqhit/battery/one/features/stats/discharge/domain/TimeConverterTest.kti happ/src/test/java/com/tqhit/battery/one/features/stats/discharge/integration/ScreenTimeConstraintTest.ktt sapp/src/test/java/com/tqhit/battery/one/features/stats/discharge/integration/ScreenTimeEstimationIntegrationTest.ktj iapp/src/test/java/com/tqhit/battery/one/features/stats/discharge/integration/ScreenTimeOscillationTest.ktr qapp/src/test/java/com/tqhit/battery/one/features/stats/discharge/integration/ScreenTimeUiUpdateIntegrationTest.ktn mapp/src/test/java/com/tqhit/battery/one/features/stats/discharge/repository/DischargeSessionRepositoryTest.ktW Vapp/src/test/java/com/tqhit/battery/one/features/stats/health/data/HealthStatusTest.ktj iapp/src/test/java/com/tqhit/battery/one/features/stats/health/domain/CalculateBatteryHealthUseCaseTest.kt` _app/src/test/java/com/tqhit/battery/one/features/stats/health/repository/HealthChartDataTest.ktQ Papp/src/test/java/com/tqhit/battery/one/utils/BackgroundPermissionManagerTest.ktP Oapp/src/test/java/com/tqhit/battery/one/features/emoji/Phase0IntegrationTest.kti happ/src/test/java/com/tqhit/battery/one/features/emoji/data/repository/BatteryStyleRepositoryImplTest.ktV Uapp/src/test/java/com/tqhit/battery/one/features/emoji/di/EmojiBatteryDIModuleTest.ktf eapp/src/test/java/com/tqhit/battery/one/features/emoji/domain/use_case/GetBatteryStylesUseCaseTest.ktk japp/src/test/java/com/tqhit/battery/one/features/emoji/presentation/gallery/BatteryGalleryViewModelTest.kto napp/src/test/java/com/tqhit/battery/one/features/emoji/presentation/gallery/adapter/BatteryStyleAdapterTest.kt