package com.tqhit.battery.one.features.stats.discharge.datasource;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ScreenStateReceiver_Factory implements Factory<ScreenStateReceiver> {
  private final Provider<Context> contextProvider;

  public ScreenStateReceiver_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ScreenStateReceiver get() {
    return newInstance(contextProvider.get());
  }

  public static ScreenStateReceiver_Factory create(Provider<Context> contextProvider) {
    return new ScreenStateReceiver_Factory(contextProvider);
  }

  public static ScreenStateReceiver newInstance(Context context) {
    return new ScreenStateReceiver(context);
  }
}
