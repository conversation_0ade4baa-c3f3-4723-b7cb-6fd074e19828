package com.tqhit.battery.one.features.stats.discharge.service;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = EnhancedDischargeTimerService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
@Generated("dagger.hilt.android.processor.internal.androidentrypoint.InjectorEntryPointGenerator")
public interface EnhancedDischargeTimerService_GeneratedInjector {
  void injectEnhancedDischargeTimerService(
      EnhancedDischargeTimerService enhancedDischargeTimerService);
}
