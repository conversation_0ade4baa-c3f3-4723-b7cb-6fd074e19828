package com.tqhit.battery.one.features.emoji.data.repository

import android.content.Context
import android.content.res.AssetManager
import android.util.Log
import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.io.ByteArrayInputStream
import java.io.IOException

/**
 * Unit tests for BatteryStyleRepositoryImpl.
 * Tests Firebase Remote Config integration, local fallback, and caching behavior.
 */
@OptIn(ExperimentalCoroutinesApi::class)
class BatteryStyleRepositoryImplTest {
    
    private lateinit var repository: BatteryStyleRepositoryImpl
    private lateinit var mockContext: Context
    private lateinit var mockAssetManager: AssetManager
    private lateinit var mockRemoteConfigHelper: FirebaseRemoteConfigHelper
    private lateinit var gson: Gson
    
    private val testStylesJson = """
        [
            {
                "id": "test_heart",
                "name": "Test Heart",
                "category": "HEART",
                "batteryImageUrl": "https://example.com/battery_heart.png",
                "emojiImageUrl": "https://example.com/emoji_heart.png",
                "isPremium": false,
                "isPopular": true
            },
            {
                "id": "test_cute",
                "name": "Test Cute",
                "category": "CUTE",
                "batteryImageUrl": "https://example.com/battery_cute.png",
                "emojiImageUrl": "https://example.com/emoji_cute.png",
                "isPremium": true,
                "isPopular": false
            }
        ]
    """.trimIndent()
    
    @Before
    fun setUp() {
        // Mock Android Log to prevent runtime exceptions
        mockkStatic(Log::class)
        every { Log.d(any<String>(), any<String>()) } returns 0
        every { Log.e(any<String>(), any<String>()) } returns 0
        every { Log.w(any<String>(), any<String>()) } returns 0
        every { Log.i(any<String>(), any<String>()) } returns 0
        every { Log.v(any<String>(), any<String>()) } returns 0

        mockContext = mockk()
        mockAssetManager = mockk()
        mockRemoteConfigHelper = mockk()
        gson = Gson()

        // Setup context and asset manager mocks
        every { mockContext.assets } returns mockAssetManager
        
        repository = BatteryStyleRepositoryImpl(
            context = mockContext,
            remoteConfigHelper = mockRemoteConfigHelper,
            gson = gson
        )
    }
    
    @Test
    fun `test getAllStyles fetches from remote config successfully`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        val styles = repository.getAllStyles(forceRefresh = true)
        
        // Then
        assertEquals(2, styles.size)
        
        val heartStyle = styles.find { it.id == "test_heart" }
        assertNotNull(heartStyle)
        assertEquals("Test Heart", heartStyle!!.name)
        assertEquals(BatteryStyleCategory.HEART, heartStyle.category)
        assertFalse(heartStyle.isPremium)
        assertTrue(heartStyle.isPopular)
        
        val cuteStyle = styles.find { it.id == "test_cute" }
        assertNotNull(cuteStyle)
        assertEquals("Test Cute", cuteStyle!!.name)
        assertEquals(BatteryStyleCategory.CUTE, cuteStyle.category)
        assertTrue(cuteStyle.isPremium)
        assertFalse(cuteStyle.isPopular)
    }
    
    @Test
    fun `test getAllStyles falls back to local JSON when remote config fails`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns ""
        every { mockAssetManager.open("emoji_battery_styles.json") } returns 
            ByteArrayInputStream(testStylesJson.toByteArray())
        
        // When
        val styles = repository.getAllStyles(forceRefresh = true)
        
        // Then
        assertEquals(2, styles.size)
        verify { mockAssetManager.open("emoji_battery_styles.json") }
    }
    
    @Test
    fun `test getAllStyles returns empty list when both remote and local fail`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns ""
        every { mockAssetManager.open("emoji_battery_styles.json") } throws IOException("File not found")
        
        // When
        val styles = repository.getAllStyles(forceRefresh = true)
        
        // Then
        assertEquals(0, styles.size)
    }
    
    @Test
    fun `test getStylesByCategory filters correctly`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        val heartStyles = repository.getStylesByCategory(BatteryStyleCategory.HEART, forceRefresh = true)
        val cuteStyles = repository.getStylesByCategory(BatteryStyleCategory.CUTE, forceRefresh = true)
        val characterStyles = repository.getStylesByCategory(BatteryStyleCategory.CHARACTER, forceRefresh = true)
        
        // Then
        assertEquals(1, heartStyles.size)
        assertEquals("test_heart", heartStyles.first().id)
        
        assertEquals(1, cuteStyles.size)
        assertEquals("test_cute", cuteStyles.first().id)
        
        assertEquals(0, characterStyles.size)
    }
    
    @Test
    fun `test getPopularStyles filters correctly`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        val popularStyles = repository.getPopularStyles(forceRefresh = true)
        
        // Then
        assertEquals(1, popularStyles.size)
        assertEquals("test_heart", popularStyles.first().id)
        assertTrue(popularStyles.first().isPopular)
    }
    
    @Test
    fun `test getPremiumStyles filters correctly`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        val premiumStyles = repository.getPremiumStyles(forceRefresh = true)
        
        // Then
        assertEquals(1, premiumStyles.size)
        assertEquals("test_cute", premiumStyles.first().id)
        assertTrue(premiumStyles.first().isPremium)
    }
    
    @Test
    fun `test getFreeStyles filters correctly`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        val freeStyles = repository.getFreeStyles(forceRefresh = true)
        
        // Then
        assertEquals(1, freeStyles.size)
        assertEquals("test_heart", freeStyles.first().id)
        assertFalse(freeStyles.first().isPremium)
    }
    
    @Test
    fun `test searchStyles filters by name correctly`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        val heartSearchResults = repository.searchStyles("heart", forceRefresh = true)
        val cuteSearchResults = repository.searchStyles("cute", forceRefresh = true)
        val noResults = repository.searchStyles("nonexistent", forceRefresh = true)
        
        // Then
        assertEquals(1, heartSearchResults.size)
        assertEquals("test_heart", heartSearchResults.first().id)
        
        assertEquals(1, cuteSearchResults.size)
        assertEquals("test_cute", cuteSearchResults.first().id)
        
        assertEquals(0, noResults.size)
    }
    
    @Test
    fun `test getStyleById returns correct style`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        repository.getAllStyles(forceRefresh = true) // Load data first
        
        // When
        val heartStyle = repository.getStyleById("test_heart")
        val cuteStyle = repository.getStyleById("test_cute")
        val nonExistentStyle = repository.getStyleById("nonexistent")
        
        // Then
        assertNotNull(heartStyle)
        assertEquals("Test Heart", heartStyle!!.name)
        
        assertNotNull(cuteStyle)
        assertEquals("Test Cute", cuteStyle!!.name)
        
        assertNull(nonExistentStyle)
    }
    
    @Test
    fun `test batteryStylesFlow emits updated data`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        repository.refreshStyles()
        val emittedStyles = repository.batteryStylesFlow.first()
        
        // Then
        assertEquals(2, emittedStyles.size)
        assertTrue(emittedStyles.any { it.id == "test_heart" })
        assertTrue(emittedStyles.any { it.id == "test_cute" })
    }
    
    @Test
    fun `test isLoadingFlow emits correct loading states`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        val initialLoadingState = repository.isLoadingFlow.first()
        
        // Then - Initial state should be false after initialization
        assertFalse(initialLoadingState)
    }
    
    @Test
    fun `test hasCachedData returns correct state`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When - Before loading data
        val beforeLoading = repository.hasCachedData()
        
        // Load data
        repository.getAllStyles(forceRefresh = true)
        
        // After loading data
        val afterLoading = repository.hasCachedData()
        
        // Then
        assertFalse(beforeLoading)
        assertTrue(afterLoading)
    }
    
    @Test
    fun `test getCurrentStyles returns cached data`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        
        // When
        val beforeLoading = repository.getCurrentStyles()
        repository.getAllStyles(forceRefresh = true)
        val afterLoading = repository.getCurrentStyles()
        
        // Then
        assertEquals(0, beforeLoading.size)
        assertEquals(2, afterLoading.size)
    }
    
    @Test
    fun `test clearCache clears all cached data`() = runTest {
        // Given
        every { mockRemoteConfigHelper.getString("emoji_battery_styles") } returns testStylesJson
        repository.getAllStyles(forceRefresh = true) // Load data first
        
        // When
        val beforeClear = repository.hasCachedData()
        repository.clearCache()
        val afterClear = repository.hasCachedData()
        val stylesAfterClear = repository.getCurrentStyles()
        
        // Then
        assertTrue(beforeClear)
        assertFalse(afterClear)
        assertEquals(0, stylesAfterClear.size)
    }
}
