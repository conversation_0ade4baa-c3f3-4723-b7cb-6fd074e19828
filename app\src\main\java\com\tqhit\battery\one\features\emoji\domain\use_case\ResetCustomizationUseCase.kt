package com.tqhit.battery.one.features.emoji.domain.use_case

import android.util.Log
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.model.UserPreferences
import com.tqhit.battery.one.features.emoji.domain.repository.CustomizationRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for resetting user customization data to defaults.
 * 
 * Provides various reset operations for troubleshooting, user preference,
 * or data cleanup scenarios. Handles selective reset options to preserve
 * user preferences when appropriate.
 * 
 * Key responsibilities:
 * - Reset customization configuration to defaults
 * - Reset complete user customization state
 * - Selective reset with preservation options
 * - Clear all data for complete cleanup
 * - Backup and restore functionality
 */
@Singleton
class ResetCustomizationUseCase @Inject constructor(
    private val customizationRepository: CustomizationRepository
) {
    
    companion object {
        private const val TAG = "ResetCustomizationUseCase"
    }
    
    /**
     * Resets customization configuration to defaults while preserving other data.
     * Keeps user preferences, permissions, and usage history intact.
     * 
     * @return Result indicating success or failure with error details
     */
    suspend fun resetCustomizationConfig(): Result<Unit> {
        return try {
            Log.d(TAG, "RESET_CONFIG: Resetting customization configuration to defaults")
            
            // Get current user customization
            val currentCustomization = customizationRepository.getCurrentUserCustomization()
            
            // Create updated customization with default config
            val updatedCustomization = currentCustomization.copy(
                customizationConfig = CustomizationConfig.createDefault()
            )
            
            // Save the updated customization
            val result = customizationRepository.saveUserCustomization(updatedCustomization)
            
            if (result.isSuccess) {
                Log.d(TAG, "RESET_CONFIG: Successfully reset customization configuration")
            } else {
                Log.e(TAG, "RESET_CONFIG: Failed to reset customization configuration", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            Log.e(TAG, "RESET_CONFIG: Exception resetting customization configuration", e)
            Result.failure(ResetCustomizationException("Failed to reset customization configuration", e))
        }
    }
    
    /**
     * Resets all customization data to defaults.
     * 
     * @param preservePreferences Whether to keep user preferences (default: true)
     * @param preserveUsageHistory Whether to keep usage history (default: false)
     * @return Result indicating success or failure with error details
     */
    suspend fun resetToDefaults(
        preservePreferences: Boolean = true,
        preserveUsageHistory: Boolean = false
    ): Result<Unit> {
        return try {
            Log.d(TAG, "RESET_DEFAULTS: Resetting to defaults (preservePreferences=$preservePreferences, preserveUsageHistory=$preserveUsageHistory)")
            
            val result = if (preservePreferences || preserveUsageHistory) {
                // Selective reset - preserve specified data
                val currentCustomization = customizationRepository.getCurrentUserCustomization()
                val defaultCustomization = UserCustomization.createDefault()
                
                val resetCustomization = defaultCustomization.copy(
                    userPreferences = if (preservePreferences) currentCustomization.userPreferences else defaultCustomization.userPreferences,
                    usageHistory = if (preserveUsageHistory) currentCustomization.usageHistory else defaultCustomization.usageHistory
                )
                
                customizationRepository.saveUserCustomization(resetCustomization)
            } else {
                // Complete reset using repository method
                customizationRepository.resetToDefaults(preservePreferences = false)
            }
            
            if (result.isSuccess) {
                Log.d(TAG, "RESET_DEFAULTS: Successfully reset to defaults")
            } else {
                Log.e(TAG, "RESET_DEFAULTS: Failed to reset to defaults", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            Log.e(TAG, "RESET_DEFAULTS: Exception resetting to defaults", e)
            Result.failure(ResetCustomizationException("Failed to reset to defaults", e))
        }
    }
    
    /**
     * Resets only user preferences to defaults.
     * Preserves customization configuration and usage history.
     * 
     * @return Result indicating success or failure with error details
     */
    suspend fun resetUserPreferences(): Result<Unit> {
        return try {
            Log.d(TAG, "RESET_PREFERENCES: Resetting user preferences to defaults")
            
            val defaultPreferences = UserPreferences.createDefault()
            val result = customizationRepository.saveUserPreferences(defaultPreferences)
            
            if (result.isSuccess) {
                Log.d(TAG, "RESET_PREFERENCES: Successfully reset user preferences")
            } else {
                Log.e(TAG, "RESET_PREFERENCES: Failed to reset user preferences", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            Log.e(TAG, "RESET_PREFERENCES: Exception resetting user preferences", e)
            Result.failure(ResetCustomizationException("Failed to reset user preferences", e))
        }
    }
    
    /**
     * Clears all customization data completely.
     * This is a destructive operation that removes all stored data.
     * 
     * @return Result indicating success or failure with error details
     */
    suspend fun clearAllData(): Result<Unit> {
        return try {
            Log.d(TAG, "CLEAR_ALL: Clearing all customization data")
            
            val result = customizationRepository.clearAllData()
            
            if (result.isSuccess) {
                Log.d(TAG, "CLEAR_ALL: Successfully cleared all data")
            } else {
                Log.e(TAG, "CLEAR_ALL: Failed to clear all data", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            Log.e(TAG, "CLEAR_ALL: Exception clearing all data", e)
            Result.failure(ResetCustomizationException("Failed to clear all data", e))
        }
    }
    
    /**
     * Disables the emoji battery feature and resets configuration.
     * Useful for troubleshooting or when user wants to disable completely.
     * 
     * @param resetConfiguration Whether to also reset the configuration (default: false)
     * @return Result indicating success or failure with error details
     */
    suspend fun disableFeature(resetConfiguration: Boolean = false): Result<Unit> {
        return try {
            Log.d(TAG, "DISABLE_FEATURE: Disabling feature (resetConfiguration=$resetConfiguration)")
            
            if (resetConfiguration) {
                // Reset configuration and disable feature
                val result = resetCustomizationConfig()
                if (result.isFailure) {
                    return result
                }
            } else {
                // Just disable the feature
                val result = customizationRepository.setFeatureEnabled(false)
                if (result.isFailure) {
                    return result
                }
            }
            
            Log.d(TAG, "DISABLE_FEATURE: Successfully disabled feature")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "DISABLE_FEATURE: Exception disabling feature", e)
            Result.failure(ResetCustomizationException("Failed to disable feature", e))
        }
    }
    
    /**
     * Creates a backup of current customization data before reset.
     * Returns the backup data that can be used for restoration.
     * 
     * @return Result with backup data or error
     */
    suspend fun createBackupBeforeReset(): Result<String> {
        return try {
            Log.d(TAG, "CREATE_BACKUP: Creating backup before reset")
            
            val result = customizationRepository.exportCustomizationData()
            
            if (result.isSuccess) {
                Log.d(TAG, "CREATE_BACKUP: Successfully created backup")
            } else {
                Log.e(TAG, "CREATE_BACKUP: Failed to create backup", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            Log.e(TAG, "CREATE_BACKUP: Exception creating backup", e)
            Result.failure(ResetCustomizationException("Failed to create backup", e))
        }
    }
    
    /**
     * Restores customization data from backup.
     * 
     * @param backupData The backup data to restore
     * @param overwriteExisting Whether to overwrite existing data (default: true)
     * @return Result indicating success or failure with error details
     */
    suspend fun restoreFromBackup(
        backupData: String,
        overwriteExisting: Boolean = true
    ): Result<Unit> {
        return try {
            Log.d(TAG, "RESTORE_BACKUP: Restoring from backup (overwrite=$overwriteExisting)")
            
            val result = customizationRepository.importCustomizationData(backupData, overwriteExisting)
            
            if (result.isSuccess) {
                Log.d(TAG, "RESTORE_BACKUP: Successfully restored from backup")
            } else {
                Log.e(TAG, "RESTORE_BACKUP: Failed to restore from backup", result.exceptionOrNull())
            }
            
            result
        } catch (e: Exception) {
            Log.e(TAG, "RESTORE_BACKUP: Exception restoring from backup", e)
            Result.failure(ResetCustomizationException("Failed to restore from backup", e))
        }
    }
    
    /**
     * Performs a safe reset with automatic backup.
     * Creates a backup before resetting, allowing for easy restoration if needed.
     * 
     * @param preservePreferences Whether to keep user preferences
     * @param preserveUsageHistory Whether to keep usage history
     * @return Result with backup data or error
     */
    suspend fun safeResetWithBackup(
        preservePreferences: Boolean = true,
        preserveUsageHistory: Boolean = false
    ): Result<String> {
        return try {
            Log.d(TAG, "SAFE_RESET: Performing safe reset with backup")
            
            // Create backup first
            val backupResult = createBackupBeforeReset()
            if (backupResult.isFailure) {
                return backupResult
            }
            
            // Perform reset
            val resetResult = resetToDefaults(preservePreferences, preserveUsageHistory)
            if (resetResult.isFailure) {
                Log.e(TAG, "SAFE_RESET: Reset failed, backup available for restoration")
                return Result.failure(ResetCustomizationException("Reset failed but backup created", resetResult.exceptionOrNull()))
            }
            
            Log.d(TAG, "SAFE_RESET: Successfully completed safe reset with backup")
            backupResult
        } catch (e: Exception) {
            Log.e(TAG, "SAFE_RESET: Exception during safe reset", e)
            Result.failure(ResetCustomizationException("Failed to perform safe reset", e))
        }
    }
}

/**
 * Exception thrown by ResetCustomizationUseCase operations.
 */
class ResetCustomizationException(message: String, cause: Throwable? = null) : Exception(message, cause)
