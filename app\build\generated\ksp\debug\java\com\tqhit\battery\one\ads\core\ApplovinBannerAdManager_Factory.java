package com.tqhit.battery.one.ads.core;

import com.tqhit.adlib.sdk.analytics.AnalyticsTracker;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ApplovinBannerAdManager_Factory implements Factory<ApplovinBannerAdManager> {
  private final Provider<AnalyticsTracker> analyticsTrackerProvider;

  public ApplovinBannerAdManager_Factory(Provider<AnalyticsTracker> analyticsTrackerProvider) {
    this.analyticsTrackerProvider = analyticsTrackerProvider;
  }

  @Override
  public ApplovinBannerAdManager get() {
    return newInstance(analyticsTrackerProvider.get());
  }

  public static ApplovinBannerAdManager_Factory create(
      Provider<AnalyticsTracker> analyticsTrackerProvider) {
    return new ApplovinBannerAdManager_Factory(analyticsTrackerProvider);
  }

  public static ApplovinBannerAdManager newInstance(AnalyticsTracker analyticsTracker) {
    return new ApplovinBannerAdManager(analyticsTracker);
  }
}
