<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" tests="16" skipped="0" failures="0" errors="0" timestamp="2025-06-20T07:45:18" hostname="DESKTOP-KBSUI08" time="0.001">
  <properties/>
  <testcase name="test validated clamps font size to minimum" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test isValid returns true for maximum valid values" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test isValid returns false for emoji scale above maximum" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test validated clamps emoji scale to maximum" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test isValid returns false for emoji scale below minimum" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test validated clamps emoji scale to minimum" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test default battery style config creation" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test custom battery style config creation" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test validated preserves valid values" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test validated clamps both values when both are invalid" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test isValid returns false for font size above maximum" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test isValid returns false for font size below minimum" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test isValid returns true for minimum valid values" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test isValid returns true for valid config" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test createDefault returns valid default config" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.0"/>
  <testcase name="test validated clamps font size to maximum" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
