package com.tqhit.battery.one.features.stats.discharge.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class EnhancedDischargeTimerServiceHelper_Factory implements Factory<EnhancedDischargeTimerServiceHelper> {
  private final Provider<Context> contextProvider;

  public EnhancedDischargeTimerServiceHelper_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public EnhancedDischargeTimerServiceHelper get() {
    return newInstance(contextProvider.get());
  }

  public static EnhancedDischargeTimerServiceHelper_Factory create(
      Provider<Context> contextProvider) {
    return new EnhancedDischargeTimerServiceHelper_Factory(contextProvider);
  }

  public static EnhancedDischargeTimerServiceHelper newInstance(Context context) {
    return new EnhancedDischargeTimerServiceHelper(context);
  }
}
