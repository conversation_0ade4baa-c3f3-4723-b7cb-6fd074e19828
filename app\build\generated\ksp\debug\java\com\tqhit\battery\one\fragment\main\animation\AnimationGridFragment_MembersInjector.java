package com.tqhit.battery.one.fragment.main.animation;

import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationGridFragment_MembersInjector implements MembersInjector<AnimationGridFragment> {
  private final Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider;

  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  public AnimationGridFragment_MembersInjector(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<AppRepository> appRepositoryProvider) {
    this.applovinInterstitialAdManagerProvider = applovinInterstitialAdManagerProvider;
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static MembersInjector<AnimationGridFragment> create(
      Provider<ApplovinInterstitialAdManager> applovinInterstitialAdManagerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<AppRepository> appRepositoryProvider) {
    return new AnimationGridFragment_MembersInjector(applovinInterstitialAdManagerProvider, remoteConfigHelperProvider, appRepositoryProvider);
  }

  @Override
  public void injectMembers(AnimationGridFragment instance) {
    injectApplovinInterstitialAdManager(instance, applovinInterstitialAdManagerProvider.get());
    injectRemoteConfigHelper(instance, remoteConfigHelperProvider.get());
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.applovinInterstitialAdManager")
  public static void injectApplovinInterstitialAdManager(AnimationGridFragment instance,
      ApplovinInterstitialAdManager applovinInterstitialAdManager) {
    instance.applovinInterstitialAdManager = applovinInterstitialAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.remoteConfigHelper")
  public static void injectRemoteConfigHelper(AnimationGridFragment instance,
      FirebaseRemoteConfigHelper remoteConfigHelper) {
    instance.remoteConfigHelper = remoteConfigHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.fragment.main.animation.AnimationGridFragment.appRepository")
  public static void injectAppRepository(AnimationGridFragment instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
