package com.tqhit.battery.one.features.stats.charge.presentation;

import com.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase;
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class StatsChargeViewModel_Factory implements Factory<StatsChargeViewModel> {
  private final Provider<StatsChargeRepository> statsChargeRepositoryProvider;

  private final Provider<CalculateSimpleChargeEstimateUseCase> calculateSimpleChargeEstimateUseCaseProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  public StatsChargeViewModel_Factory(Provider<StatsChargeRepository> statsChargeRepositoryProvider,
      Provider<CalculateSimpleChargeEstimateUseCase> calculateSimpleChargeEstimateUseCaseProvider,
      Provider<AppRepository> appRepositoryProvider) {
    this.statsChargeRepositoryProvider = statsChargeRepositoryProvider;
    this.calculateSimpleChargeEstimateUseCaseProvider = calculateSimpleChargeEstimateUseCaseProvider;
    this.appRepositoryProvider = appRepositoryProvider;
  }

  @Override
  public StatsChargeViewModel get() {
    return newInstance(statsChargeRepositoryProvider.get(), calculateSimpleChargeEstimateUseCaseProvider.get(), appRepositoryProvider.get());
  }

  public static StatsChargeViewModel_Factory create(
      Provider<StatsChargeRepository> statsChargeRepositoryProvider,
      Provider<CalculateSimpleChargeEstimateUseCase> calculateSimpleChargeEstimateUseCaseProvider,
      Provider<AppRepository> appRepositoryProvider) {
    return new StatsChargeViewModel_Factory(statsChargeRepositoryProvider, calculateSimpleChargeEstimateUseCaseProvider, appRepositoryProvider);
  }

  public static StatsChargeViewModel newInstance(StatsChargeRepository statsChargeRepository,
      CalculateSimpleChargeEstimateUseCase calculateSimpleChargeEstimateUseCase,
      AppRepository appRepository) {
    return new StatsChargeViewModel(statsChargeRepository, calculateSimpleChargeEstimateUseCase, appRepository);
  }
}
