package com.tqhit.battery.one.features.stats.charge.presentation;

import com.tqhit.battery.one.service.VibrationService;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class StatsChargeFragment_MembersInjector implements MembersInjector<StatsChargeFragment> {
  private final Provider<VibrationService> vibrationServiceProvider;

  public StatsChargeFragment_MembersInjector(Provider<VibrationService> vibrationServiceProvider) {
    this.vibrationServiceProvider = vibrationServiceProvider;
  }

  public static MembersInjector<StatsChargeFragment> create(
      Provider<VibrationService> vibrationServiceProvider) {
    return new StatsChargeFragment_MembersInjector(vibrationServiceProvider);
  }

  @Override
  public void injectMembers(StatsChargeFragment instance) {
    injectVibrationService(instance, vibrationServiceProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment.vibrationService")
  public static void injectVibrationService(StatsChargeFragment instance,
      VibrationService vibrationService) {
    instance.vibrationService = vibrationService;
  }
}
