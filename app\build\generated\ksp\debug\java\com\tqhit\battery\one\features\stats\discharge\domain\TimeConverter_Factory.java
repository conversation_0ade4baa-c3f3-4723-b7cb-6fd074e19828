package com.tqhit.battery.one.features.stats.discharge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class TimeConverter_Factory implements Factory<TimeConverter> {
  @Override
  public TimeConverter get() {
    return newInstance();
  }

  public static TimeConverter_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static TimeConverter newInstance() {
    return new TimeConverter();
  }

  private static final class InstanceHolder {
    static final TimeConverter_Factory INSTANCE = new TimeConverter_Factory();
  }
}
