package com.tqhit.battery.one.service;

import com.tqhit.battery.one.repository.AppRepository;
import com.tqhit.battery.one.repository.BatteryRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryMonitorService_MembersInjector implements MembersInjector<BatteryMonitorService> {
  private final Provider<BatteryRepository> batteryRepositoryProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  public BatteryMonitorService_MembersInjector(
      Provider<BatteryRepository> batteryRepositoryProvider,
      Provider<AppRepository> appRepositoryProvider) {
    this.batteryRepositoryProvider = batteryRepositoryProvider;
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static MembersInjector<BatteryMonitorService> create(
      Provider<BatteryRepository> batteryRepositoryProvider,
      Provider<AppRepository> appRepositoryProvider) {
    return new BatteryMonitorService_MembersInjector(batteryRepositoryProvider, appRepositoryProvider);
  }

  @Override
  public void injectMembers(BatteryMonitorService instance) {
    injectBatteryRepository(instance, batteryRepositoryProvider.get());
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.BatteryMonitorService.batteryRepository")
  public static void injectBatteryRepository(BatteryMonitorService instance,
      BatteryRepository batteryRepository) {
    instance.batteryRepository = batteryRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.service.BatteryMonitorService.appRepository")
  public static void injectAppRepository(BatteryMonitorService instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
