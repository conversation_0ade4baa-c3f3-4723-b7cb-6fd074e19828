package com.tqhit.battery.one.features.stats.discharge.domain;

import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class FullSessionReEstimator_Factory implements Factory<FullSessionReEstimator> {
  private final Provider<ScreenTimeCalculator> screenTimeCalculatorProvider;

  private final Provider<DischargeRatesCache> dischargeRatesCacheProvider;

  private final Provider<TimeConverter> timeConverterProvider;

  private final Provider<DischargeRateCalculator> dischargeRateCalculatorProvider;

  public FullSessionReEstimator_Factory(Provider<ScreenTimeCalculator> screenTimeCalculatorProvider,
      Provider<DischargeRatesCache> dischargeRatesCacheProvider,
      Provider<TimeConverter> timeConverterProvider,
      Provider<DischargeRateCalculator> dischargeRateCalculatorProvider) {
    this.screenTimeCalculatorProvider = screenTimeCalculatorProvider;
    this.dischargeRatesCacheProvider = dischargeRatesCacheProvider;
    this.timeConverterProvider = timeConverterProvider;
    this.dischargeRateCalculatorProvider = dischargeRateCalculatorProvider;
  }

  @Override
  public FullSessionReEstimator get() {
    return newInstance(screenTimeCalculatorProvider.get(), dischargeRatesCacheProvider.get(), timeConverterProvider.get(), dischargeRateCalculatorProvider.get());
  }

  public static FullSessionReEstimator_Factory create(
      Provider<ScreenTimeCalculator> screenTimeCalculatorProvider,
      Provider<DischargeRatesCache> dischargeRatesCacheProvider,
      Provider<TimeConverter> timeConverterProvider,
      Provider<DischargeRateCalculator> dischargeRateCalculatorProvider) {
    return new FullSessionReEstimator_Factory(screenTimeCalculatorProvider, dischargeRatesCacheProvider, timeConverterProvider, dischargeRateCalculatorProvider);
  }

  public static FullSessionReEstimator newInstance(ScreenTimeCalculator screenTimeCalculator,
      DischargeRatesCache dischargeRatesCache, TimeConverter timeConverter,
      DischargeRateCalculator dischargeRateCalculator) {
    return new FullSessionReEstimator(screenTimeCalculator, dischargeRatesCache, timeConverter, dischargeRateCalculator);
  }
}
