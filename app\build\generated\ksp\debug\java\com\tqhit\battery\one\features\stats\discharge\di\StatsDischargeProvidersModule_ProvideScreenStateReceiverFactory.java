package com.tqhit.battery.one.features.stats.discharge.di;

import android.content.Context;
import com.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory implements Factory<ScreenStateReceiver> {
  private final Provider<Context> contextProvider;

  public StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory(
      Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ScreenStateReceiver get() {
    return provideScreenStateReceiver(contextProvider.get());
  }

  public static StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory create(
      Provider<Context> contextProvider) {
    return new StatsDischargeProvidersModule_ProvideScreenStateReceiverFactory(contextProvider);
  }

  public static ScreenStateReceiver provideScreenStateReceiver(Context context) {
    return Preconditions.checkNotNullFromProvides(StatsDischargeProvidersModule.INSTANCE.provideScreenStateReceiver(context));
  }
}
