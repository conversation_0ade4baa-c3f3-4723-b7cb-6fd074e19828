package com.tqhit.battery.one.features.stats.discharge.domain;

import com.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class GapEstimationCalculator_Factory implements Factory<GapEstimationCalculator> {
  private final Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider;

  private final Provider<ScreenTimeCalculator> screenTimeCalculatorProvider;

  private final Provider<DischargeRatesCache> dischargeRatesCacheProvider;

  private final Provider<TimeConverter> timeConverterProvider;

  private final Provider<DischargeRateCalculator> dischargeRateCalculatorProvider;

  public GapEstimationCalculator_Factory(
      Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider,
      Provider<ScreenTimeCalculator> screenTimeCalculatorProvider,
      Provider<DischargeRatesCache> dischargeRatesCacheProvider,
      Provider<TimeConverter> timeConverterProvider,
      Provider<DischargeRateCalculator> dischargeRateCalculatorProvider) {
    this.sessionMetricsCalculatorProvider = sessionMetricsCalculatorProvider;
    this.screenTimeCalculatorProvider = screenTimeCalculatorProvider;
    this.dischargeRatesCacheProvider = dischargeRatesCacheProvider;
    this.timeConverterProvider = timeConverterProvider;
    this.dischargeRateCalculatorProvider = dischargeRateCalculatorProvider;
  }

  @Override
  public GapEstimationCalculator get() {
    return newInstance(sessionMetricsCalculatorProvider.get(), screenTimeCalculatorProvider.get(), dischargeRatesCacheProvider.get(), timeConverterProvider.get(), dischargeRateCalculatorProvider.get());
  }

  public static GapEstimationCalculator_Factory create(
      Provider<SessionMetricsCalculator> sessionMetricsCalculatorProvider,
      Provider<ScreenTimeCalculator> screenTimeCalculatorProvider,
      Provider<DischargeRatesCache> dischargeRatesCacheProvider,
      Provider<TimeConverter> timeConverterProvider,
      Provider<DischargeRateCalculator> dischargeRateCalculatorProvider) {
    return new GapEstimationCalculator_Factory(sessionMetricsCalculatorProvider, screenTimeCalculatorProvider, dischargeRatesCacheProvider, timeConverterProvider, dischargeRateCalculatorProvider);
  }

  public static GapEstimationCalculator newInstance(
      SessionMetricsCalculator sessionMetricsCalculator, ScreenTimeCalculator screenTimeCalculator,
      DischargeRatesCache dischargeRatesCache, TimeConverter timeConverter,
      DischargeRateCalculator dischargeRateCalculator) {
    return new GapEstimationCalculator(sessionMetricsCalculator, screenTimeCalculator, dischargeRatesCache, timeConverter, dischargeRateCalculator);
  }
}
