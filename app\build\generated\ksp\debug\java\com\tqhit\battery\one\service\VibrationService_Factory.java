package com.tqhit.battery.one.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class VibrationService_Factory implements Factory<VibrationService> {
  private final Provider<Context> contextProvider;

  public VibrationService_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public VibrationService get() {
    return newInstance(contextProvider.get());
  }

  public static VibrationService_Factory create(Provider<Context> contextProvider) {
    return new VibrationService_Factory(contextProvider);
  }

  public static VibrationService newInstance(Context context) {
    return new VibrationService(context);
  }
}
