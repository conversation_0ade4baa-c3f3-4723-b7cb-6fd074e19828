package com.tqhit.battery.one.features.emoji.presentation.gallery;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.FragmentComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = EmojiBatteryFragment.class
)
@GeneratedEntryPoint
@InstallIn(FragmentComponent.class)
@Generated("dagger.hilt.android.processor.internal.androidentrypoint.InjectorEntryPointGenerator")
public interface EmojiBatteryFragment_GeneratedInjector {
  void injectEmojiBatteryFragment(EmojiBatteryFragment emojiBatteryFragment);
}
