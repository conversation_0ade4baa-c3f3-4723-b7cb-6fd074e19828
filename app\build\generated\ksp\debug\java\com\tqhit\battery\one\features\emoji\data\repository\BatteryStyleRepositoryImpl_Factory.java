package com.tqhit.battery.one.features.emoji.data.repository;

import android.content.Context;
import com.google.gson.Gson;
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryStyleRepositoryImpl_Factory implements Factory<BatteryStyleRepositoryImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<Gson> gsonProvider;

  public BatteryStyleRepositoryImpl_Factory(Provider<Context> contextProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<Gson> gsonProvider) {
    this.contextProvider = contextProvider;
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.gsonProvider = gsonProvider;
  }

  @Override
  public BatteryStyleRepositoryImpl get() {
    return newInstance(contextProvider.get(), remoteConfigHelperProvider.get(), gsonProvider.get());
  }

  public static BatteryStyleRepositoryImpl_Factory create(Provider<Context> contextProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<Gson> gsonProvider) {
    return new BatteryStyleRepositoryImpl_Factory(contextProvider, remoteConfigHelperProvider, gsonProvider);
  }

  public static BatteryStyleRepositoryImpl newInstance(Context context,
      FirebaseRemoteConfigHelper remoteConfigHelper, Gson gson) {
    return new BatteryStyleRepositoryImpl(context, remoteConfigHelper, gson);
  }
}
