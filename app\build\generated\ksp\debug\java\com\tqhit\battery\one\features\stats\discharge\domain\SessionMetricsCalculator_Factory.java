package com.tqhit.battery.one.features.stats.discharge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SessionMetricsCalculator_Factory implements Factory<SessionMetricsCalculator> {
  private final Provider<TimeConverter> timeConverterProvider;

  private final Provider<DischargeRateCalculator> dischargeRateCalculatorProvider;

  public SessionMetricsCalculator_Factory(Provider<TimeConverter> timeConverterProvider,
      Provider<DischargeRateCalculator> dischargeRateCalculatorProvider) {
    this.timeConverterProvider = timeConverterProvider;
    this.dischargeRateCalculatorProvider = dischargeRateCalculatorProvider;
  }

  @Override
  public SessionMetricsCalculator get() {
    return newInstance(timeConverterProvider.get(), dischargeRateCalculatorProvider.get());
  }

  public static SessionMetricsCalculator_Factory create(
      Provider<TimeConverter> timeConverterProvider,
      Provider<DischargeRateCalculator> dischargeRateCalculatorProvider) {
    return new SessionMetricsCalculator_Factory(timeConverterProvider, dischargeRateCalculatorProvider);
  }

  public static SessionMetricsCalculator newInstance(TimeConverter timeConverter,
      DischargeRateCalculator dischargeRateCalculator) {
    return new SessionMetricsCalculator(timeConverter, dischargeRateCalculator);
  }
}
