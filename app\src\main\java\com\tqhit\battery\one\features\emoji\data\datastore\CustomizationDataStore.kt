package com.tqhit.battery.one.features.emoji.data.datastore

import android.content.Context
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig
import com.tqhit.battery.one.features.emoji.domain.model.ColorPalette
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.domain.model.OverlayPosition
import com.tqhit.battery.one.features.emoji.domain.model.UserCustomization
import com.tqhit.battery.one.features.emoji.domain.model.UserPreferences
import com.tqhit.battery.one.features.emoji.domain.model.UsageHistory
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * DataStore wrapper for emoji battery customization data.
 * 
 * Provides type-safe, reactive access to user customization settings using
 * Jetpack DataStore Preferences. Handles serialization/deserialization of
 * complex objects and provides error recovery mechanisms.
 * 
 * Key features:
 * - Reactive Flow-based data access
 * - Type-safe preference keys
 * - JSON serialization for complex objects
 * - Error handling and recovery
 * - Atomic updates and transactions
 */
@Singleton
class CustomizationDataStore @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) {
    
    companion object {
        private const val TAG = "CustomizationDataStore"
        private const val DATASTORE_NAME = "emoji_customization_preferences"
        
        // CustomizationConfig keys
        private val KEY_SELECTED_STYLE_ID = stringPreferencesKey("selected_style_id")
        private val KEY_SELECTED_BATTERY_IMAGE_URL = stringPreferencesKey("selected_battery_image_url")
        private val KEY_SELECTED_EMOJI_IMAGE_URL = stringPreferencesKey("selected_emoji_image_url")
        private val KEY_SHOW_EMOJI = booleanPreferencesKey("show_emoji")
        private val KEY_SHOW_PERCENTAGE = booleanPreferencesKey("show_percentage")
        private val KEY_PERCENTAGE_FONT_SIZE_DP = intPreferencesKey("percentage_font_size_dp")
        private val KEY_EMOJI_SIZE_SCALE = stringPreferencesKey("emoji_size_scale") // Float as String
        private val KEY_PERCENTAGE_COLOR = intPreferencesKey("percentage_color")
        private val KEY_IS_FEATURE_ENABLED = booleanPreferencesKey("is_feature_enabled")
        private val KEY_OVERLAY_POSITION = stringPreferencesKey("overlay_position")
        private val KEY_LAST_MODIFIED_TIMESTAMP = longPreferencesKey("last_modified_timestamp")
        
        // Permission state keys
        private val KEY_HAS_ACCESSIBILITY_PERMISSION = booleanPreferencesKey("has_accessibility_permission")
        private val KEY_HAS_OVERLAY_PERMISSION = booleanPreferencesKey("has_overlay_permission")
        private val KEY_IS_ACCESSIBILITY_SERVICE_ENABLED = booleanPreferencesKey("is_accessibility_service_enabled")
        
        // UserPreferences keys
        private val KEY_SHOW_ONBOARDING_TIPS = booleanPreferencesKey("show_onboarding_tips")
        private val KEY_ENABLE_HAPTIC_FEEDBACK = booleanPreferencesKey("enable_haptic_feedback")
        private val KEY_AUTO_SAVE_CHANGES = booleanPreferencesKey("auto_save_changes")
        private val KEY_SHOW_PREVIEW_IN_GALLERY = booleanPreferencesKey("show_preview_in_gallery")
        private val KEY_PREFERRED_COLOR_PALETTE = stringPreferencesKey("preferred_color_palette")
        
        // UsageHistory keys
        private val KEY_TOTAL_CONFIGURATION_CHANGES = intPreferencesKey("total_configuration_changes")
        private val KEY_TOTAL_STYLES_USED = intPreferencesKey("total_styles_used")
        private val KEY_FEATURE_ENABLED_DURATION_MS = longPreferencesKey("feature_enabled_duration_ms")
        private val KEY_LAST_USED_TIMESTAMP = longPreferencesKey("last_used_timestamp")
        private val KEY_FAVORITE_STYLE_IDS = stringPreferencesKey("favorite_style_ids") // JSON array
    }
    
    // DataStore instance
    private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = DATASTORE_NAME)
    
    /**
     * Reactive flow of complete user customization state.
     */
    val userCustomizationFlow: Flow<UserCustomization> = context.dataStore.data
        .map { preferences ->
            try {
                mapPreferencesToUserCustomization(preferences)
            } catch (e: Exception) {
                Log.e(TAG, "Error mapping preferences to UserCustomization", e)
                UserCustomization.createDefault()
            }
        }
        .catch { exception ->
            Log.e(TAG, "Error reading UserCustomization from DataStore", exception)
            emit(UserCustomization.createDefault())
        }
    
    /**
     * Reactive flow of customization configuration only.
     */
    val customizationConfigFlow: Flow<CustomizationConfig> = context.dataStore.data
        .map { preferences ->
            try {
                mapPreferencesToCustomizationConfig(preferences)
            } catch (e: Exception) {
                Log.e(TAG, "Error mapping preferences to CustomizationConfig", e)
                CustomizationConfig.createDefault()
            }
        }
        .catch { exception ->
            Log.e(TAG, "Error reading CustomizationConfig from DataStore", exception)
            emit(CustomizationConfig.createDefault())
        }
    
    /**
     * Reactive flow of user preferences only.
     */
    val userPreferencesFlow: Flow<UserPreferences> = context.dataStore.data
        .map { preferences ->
            try {
                mapPreferencesToUserPreferences(preferences)
            } catch (e: Exception) {
                Log.e(TAG, "Error mapping preferences to UserPreferences", e)
                UserPreferences.createDefault()
            }
        }
        .catch { exception ->
            Log.e(TAG, "Error reading UserPreferences from DataStore", exception)
            emit(UserPreferences.createDefault())
        }
    
    /**
     * Saves complete user customization state to DataStore.
     */
    suspend fun saveUserCustomization(userCustomization: UserCustomization): Result<Unit> {
        return try {
            context.dataStore.edit { preferences ->
                mapUserCustomizationToPreferences(userCustomization, preferences)
            }
            Log.d(TAG, "Successfully saved UserCustomization")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save UserCustomization", e)
            Result.failure(e)
        }
    }
    
    /**
     * Saves customization configuration only.
     */
    suspend fun saveCustomizationConfig(config: CustomizationConfig): Result<Unit> {
        return try {
            context.dataStore.edit { preferences ->
                mapCustomizationConfigToPreferences(config, preferences)
            }
            Log.d(TAG, "Successfully saved CustomizationConfig")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save CustomizationConfig", e)
            Result.failure(e)
        }
    }
    
    /**
     * Saves user preferences only.
     */
    suspend fun saveUserPreferences(userPreferences: UserPreferences): Result<Unit> {
        return try {
            context.dataStore.edit { preferences ->
                mapUserPreferencesToPreferences(userPreferences, preferences)
            }
            Log.d(TAG, "Successfully saved UserPreferences")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save UserPreferences", e)
            Result.failure(e)
        }
    }
    
    /**
     * Updates permission states only.
     */
    suspend fun updatePermissionStates(
        hasAccessibilityPermission: Boolean,
        hasOverlayPermission: Boolean,
        isServiceEnabled: Boolean
    ): Result<Unit> {
        return try {
            context.dataStore.edit { preferences ->
                preferences[KEY_HAS_ACCESSIBILITY_PERMISSION] = hasAccessibilityPermission
                preferences[KEY_HAS_OVERLAY_PERMISSION] = hasOverlayPermission
                preferences[KEY_IS_ACCESSIBILITY_SERVICE_ENABLED] = isServiceEnabled
            }
            Log.d(TAG, "Successfully updated permission states")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to update permission states", e)
            Result.failure(e)
        }
    }
    
    /**
     * Clears all stored data.
     */
    suspend fun clearAllData(): Result<Unit> {
        return try {
            context.dataStore.edit { preferences ->
                preferences.clear()
            }
            Log.d(TAG, "Successfully cleared all data")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear all data", e)
            Result.failure(e)
        }
    }

    /**
     * Maps DataStore preferences to UserCustomization object.
     */
    private fun mapPreferencesToUserCustomization(preferences: Preferences): UserCustomization {
        val customizationConfig = mapPreferencesToCustomizationConfig(preferences)
        val userPreferences = mapPreferencesToUserPreferences(preferences)
        val usageHistory = mapPreferencesToUsageHistory(preferences)

        return UserCustomization(
            customizationConfig = customizationConfig,
            hasAccessibilityPermission = preferences[KEY_HAS_ACCESSIBILITY_PERMISSION] ?: false,
            hasOverlayPermission = preferences[KEY_HAS_OVERLAY_PERMISSION] ?: false,
            isAccessibilityServiceEnabled = preferences[KEY_IS_ACCESSIBILITY_SERVICE_ENABLED] ?: false,
            userPreferences = userPreferences,
            usageHistory = usageHistory
        )
    }

    /**
     * Maps DataStore preferences to CustomizationConfig object.
     */
    private fun mapPreferencesToCustomizationConfig(preferences: Preferences): CustomizationConfig {
        val styleConfig = BatteryStyleConfig(
            showEmoji = preferences[KEY_SHOW_EMOJI] ?: true,
            showPercentage = preferences[KEY_SHOW_PERCENTAGE] ?: true,
            percentageFontSizeDp = preferences[KEY_PERCENTAGE_FONT_SIZE_DP] ?: 14,
            emojiSizeScale = preferences[KEY_EMOJI_SIZE_SCALE]?.toFloatOrNull() ?: 1.0f,
            percentageColor = preferences[KEY_PERCENTAGE_COLOR] ?: 0xFFFFFFFF.toInt()
        )

        val overlayPosition = try {
            OverlayPosition.valueOf(preferences[KEY_OVERLAY_POSITION] ?: OverlayPosition.TOP_CENTER.name)
        } catch (e: Exception) {
            OverlayPosition.TOP_CENTER
        }

        return CustomizationConfig(
            selectedStyleId = preferences[KEY_SELECTED_STYLE_ID] ?: "",
            selectedBatteryImageUrl = preferences[KEY_SELECTED_BATTERY_IMAGE_URL] ?: "",
            selectedEmojiImageUrl = preferences[KEY_SELECTED_EMOJI_IMAGE_URL] ?: "",
            styleConfig = styleConfig,
            isFeatureEnabled = preferences[KEY_IS_FEATURE_ENABLED] ?: false,
            overlayPosition = overlayPosition,
            lastModifiedTimestamp = preferences[KEY_LAST_MODIFIED_TIMESTAMP] ?: System.currentTimeMillis()
        )
    }

    /**
     * Maps DataStore preferences to UserPreferences object.
     */
    private fun mapPreferencesToUserPreferences(preferences: Preferences): UserPreferences {
        val colorPalette = try {
            ColorPalette.valueOf(preferences[KEY_PREFERRED_COLOR_PALETTE] ?: ColorPalette.DEFAULT.name)
        } catch (e: Exception) {
            ColorPalette.DEFAULT
        }

        return UserPreferences(
            showOnboardingTips = preferences[KEY_SHOW_ONBOARDING_TIPS] ?: true,
            enableHapticFeedback = preferences[KEY_ENABLE_HAPTIC_FEEDBACK] ?: true,
            autoSaveChanges = preferences[KEY_AUTO_SAVE_CHANGES] ?: true,
            showPreviewInGallery = preferences[KEY_SHOW_PREVIEW_IN_GALLERY] ?: true,
            preferredColorPalette = colorPalette
        )
    }

    /**
     * Maps DataStore preferences to UsageHistory object.
     */
    private fun mapPreferencesToUsageHistory(preferences: Preferences): UsageHistory {
        val favoriteStyleIds = try {
            val json = preferences[KEY_FAVORITE_STYLE_IDS] ?: "[]"
            gson.fromJson<List<String>>(json, object : TypeToken<List<String>>() {}.type) ?: emptyList()
        } catch (e: Exception) {
            Log.w(TAG, "Failed to parse favorite style IDs", e)
            emptyList()
        }

        return UsageHistory(
            totalConfigurationChanges = preferences[KEY_TOTAL_CONFIGURATION_CHANGES] ?: 0,
            totalStylesUsed = preferences[KEY_TOTAL_STYLES_USED] ?: 0,
            featureEnabledDurationMs = preferences[KEY_FEATURE_ENABLED_DURATION_MS] ?: 0L,
            lastUsedTimestamp = preferences[KEY_LAST_USED_TIMESTAMP] ?: 0L,
            favoriteStyleIds = favoriteStyleIds
        )
    }

    /**
     * Maps UserCustomization object to DataStore preferences.
     */
    private fun mapUserCustomizationToPreferences(
        userCustomization: UserCustomization,
        preferences: MutablePreferences
    ) {
        mapCustomizationConfigToPreferences(userCustomization.customizationConfig, preferences)
        mapUserPreferencesToPreferences(userCustomization.userPreferences, preferences)
        mapUsageHistoryToPreferences(userCustomization.usageHistory, preferences)

        preferences[KEY_HAS_ACCESSIBILITY_PERMISSION] = userCustomization.hasAccessibilityPermission
        preferences[KEY_HAS_OVERLAY_PERMISSION] = userCustomization.hasOverlayPermission
        preferences[KEY_IS_ACCESSIBILITY_SERVICE_ENABLED] = userCustomization.isAccessibilityServiceEnabled
    }

    /**
     * Maps CustomizationConfig object to DataStore preferences.
     */
    private fun mapCustomizationConfigToPreferences(
        config: CustomizationConfig,
        preferences: MutablePreferences
    ) {
        preferences[KEY_SELECTED_STYLE_ID] = config.selectedStyleId
        preferences[KEY_SELECTED_BATTERY_IMAGE_URL] = config.selectedBatteryImageUrl
        preferences[KEY_SELECTED_EMOJI_IMAGE_URL] = config.selectedEmojiImageUrl
        preferences[KEY_SHOW_EMOJI] = config.styleConfig.showEmoji
        preferences[KEY_SHOW_PERCENTAGE] = config.styleConfig.showPercentage
        preferences[KEY_PERCENTAGE_FONT_SIZE_DP] = config.styleConfig.percentageFontSizeDp
        preferences[KEY_EMOJI_SIZE_SCALE] = config.styleConfig.emojiSizeScale.toString()
        preferences[KEY_PERCENTAGE_COLOR] = config.styleConfig.percentageColor
        preferences[KEY_IS_FEATURE_ENABLED] = config.isFeatureEnabled
        preferences[KEY_OVERLAY_POSITION] = config.overlayPosition.name
        preferences[KEY_LAST_MODIFIED_TIMESTAMP] = config.lastModifiedTimestamp
    }

    /**
     * Maps UserPreferences object to DataStore preferences.
     */
    private fun mapUserPreferencesToPreferences(
        userPreferences: UserPreferences,
        preferences: MutablePreferences
    ) {
        preferences[KEY_SHOW_ONBOARDING_TIPS] = userPreferences.showOnboardingTips
        preferences[KEY_ENABLE_HAPTIC_FEEDBACK] = userPreferences.enableHapticFeedback
        preferences[KEY_AUTO_SAVE_CHANGES] = userPreferences.autoSaveChanges
        preferences[KEY_SHOW_PREVIEW_IN_GALLERY] = userPreferences.showPreviewInGallery
        preferences[KEY_PREFERRED_COLOR_PALETTE] = userPreferences.preferredColorPalette.name
    }

    /**
     * Maps UsageHistory object to DataStore preferences.
     */
    private fun mapUsageHistoryToPreferences(
        usageHistory: UsageHistory,
        preferences: MutablePreferences
    ) {
        preferences[KEY_TOTAL_CONFIGURATION_CHANGES] = usageHistory.totalConfigurationChanges
        preferences[KEY_TOTAL_STYLES_USED] = usageHistory.totalStylesUsed
        preferences[KEY_FEATURE_ENABLED_DURATION_MS] = usageHistory.featureEnabledDurationMs
        preferences[KEY_LAST_USED_TIMESTAMP] = usageHistory.lastUsedTimestamp

        try {
            val json = gson.toJson(usageHistory.favoriteStyleIds)
            preferences[KEY_FAVORITE_STYLE_IDS] = json
        } catch (e: Exception) {
            Log.w(TAG, "Failed to serialize favorite style IDs", e)
            preferences[KEY_FAVORITE_STYLE_IDS] = "[]"
        }
    }
}
