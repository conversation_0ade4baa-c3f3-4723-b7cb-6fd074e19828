package com.tqhit.battery.one.features.charge.datasource

import android.content.Context
import android.content.Intent
import android.os.BatteryManager
import com.tqhit.battery.one.features.charge.data.model.NewChargeStatus
import io.mockk.*
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(RobolectricTestRunner::class)
@Config(manifest = Config.NONE, sdk = [28])
class AndroidBatteryStatsDataSourceTest {

    // Mocks
    private lateinit var context: Context
    private lateinit var intent: Intent
    private lateinit var batteryManager: BatteryManager
    private lateinit var dataSource: AndroidBatteryStatsDataSource
    
    @Before
    fun setup() {
        // Configure Shadow Log to avoid Log.d issues
        ShadowLog.stream = System.out
        
        // Initialize mocks
        context = mockk(relaxed = true)
        intent = mockk(relaxed = true)
        batteryManager = mockk(relaxed = true)
        
        // Mock context.getSystemService
        every { context.getSystemService(Context.BATTERY_SERVICE) } returns batteryManager
        
        // Create the data source
        dataSource = AndroidBatteryStatsDataSource(context)
    }
    
    @Test
    fun test_onReceive_validBatteryChangedIntent_emitsCorrectChargeStatus() = runBlocking {
        // Mock Intent extras
        every { intent.action } returns Intent.ACTION_BATTERY_CHANGED
        every { intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1) } returns BatteryManager.BATTERY_STATUS_CHARGING
        every { intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1) } returns BatteryManager.BATTERY_PLUGGED_AC
        every { intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) } returns 50
        every { intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1) } returns 100
        every { intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0) } returns 4200
        every { intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0) } returns 300
        
        // Mock BatteryManager.getIntProperty
        every { batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW) } returns 1500000
        
        // Mock context.registerReceiver
        every { context.registerReceiver(null, any()) } returns intent
        
        // Call the method being tested
        val status = dataSource.getCurrentBatteryStatus()
        
        // Verify the charge status properties
        assertEquals(50, status.percentage)
        assertEquals(true, status.isCharging)
        assertEquals(BatteryManager.BATTERY_PLUGGED_AC, status.pluggedSource)
        assertEquals(1500000L, status.currentMicroAmperes)
        assertEquals(4200, status.voltageMillivolts)
        assertEquals(30.0f, status.temperatureCelsius)
    }
    
    @Test
    fun test_onReceive_currentNowIsZero_emitsCorrectChargeStatus() = runBlocking {
        // Mock Intent extras
        every { intent.action } returns Intent.ACTION_BATTERY_CHANGED
        every { intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1) } returns BatteryManager.BATTERY_STATUS_CHARGING
        every { intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1) } returns BatteryManager.BATTERY_PLUGGED_AC
        every { intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) } returns 50
        every { intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1) } returns 100
        every { intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0) } returns 4200
        every { intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0) } returns 300
        
        // Mock BatteryManager.getIntProperty to return 0
        every { batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW) } returns 0
        
        // Mock context.registerReceiver
        every { context.registerReceiver(null, any()) } returns intent
        
        // Call the method being tested
        val status = dataSource.getCurrentBatteryStatus()
        
        // Verify the charge status properties, particularly currentMicroAmperes = 0
        assertEquals(0L, status.currentMicroAmperes)
    }
    
    @Test
    fun test_onReceive_voltageIsZero_emitsCorrectChargeStatus() = runBlocking {
        // Mock Intent extras
        every { intent.action } returns Intent.ACTION_BATTERY_CHANGED
        every { intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1) } returns BatteryManager.BATTERY_STATUS_CHARGING
        every { intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1) } returns BatteryManager.BATTERY_PLUGGED_AC
        every { intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) } returns 50
        every { intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1) } returns 100
        every { intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0) } returns 0
        every { intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0) } returns 300
        
        // Mock BatteryManager.getIntProperty
        every { batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW) } returns 1500000
        
        // Mock context.registerReceiver
        every { context.registerReceiver(null, any()) } returns intent
        
        // Call the method being tested
        val status = dataSource.getCurrentBatteryStatus()
        
        // Verify the charge status properties, particularly voltageMillivolts = 0
        assertEquals(0, status.voltageMillivolts)
    }
    
    @Test
    fun test_onReceive_currentNowIsSmallPositive_scalesValueUpBy1000() = runBlocking {
        // Mock Intent extras
        every { intent.action } returns Intent.ACTION_BATTERY_CHANGED
        every { intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1) } returns BatteryManager.BATTERY_STATUS_CHARGING
        every { intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1) } returns BatteryManager.BATTERY_PLUGGED_AC
        every { intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) } returns 50
        every { intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1) } returns 100
        every { intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0) } returns 4200
        every { intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0) } returns 300
        
        // Mock BatteryManager.getIntProperty to return a small positive value (500)
        every { batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW) } returns 500
        
        // Mock context.registerReceiver
        every { context.registerReceiver(null, any()) } returns intent
        
        // Call the method being tested
        val status = dataSource.getCurrentBatteryStatus()
        
        // Verify the charge status scales the current value by 1000
        assertEquals(500000L, status.currentMicroAmperes) // Now 500 * 1000 = 500,000
    }
    
    @Test
    fun test_onReceive_currentNowIsSmallNegative_scalesValueUpBy1000() = runBlocking {
        // Mock Intent extras
        every { intent.action } returns Intent.ACTION_BATTERY_CHANGED
        every { intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1) } returns BatteryManager.BATTERY_STATUS_DISCHARGING
        every { intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1) } returns 0
        every { intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) } returns 50
        every { intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1) } returns 100
        every { intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0) } returns 3800
        every { intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0) } returns 300
        
        // Mock BatteryManager.getIntProperty to return a small negative value (-500)
        every { batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW) } returns -500
        
        // Mock context.registerReceiver
        every { context.registerReceiver(null, any()) } returns intent
        
        // Call the method being tested
        val status = dataSource.getCurrentBatteryStatus()
        
        // Verify the charge status scales the current value by 1000 while preserving the sign
        assertEquals(-500000L, status.currentMicroAmperes) // Now -500 * 1000 = -500,000
    }
} 