  Context android.content  Intent android.content  SharedPreferences android.content  Editor !android.content.SharedPreferences  PackageManager android.content.pm  AssetManager android.content.res  BatteryManager 
android.os  PowerManager 
android.os  FragmentManager androidx.fragment.app  FragmentTransaction androidx.fragment.app  LifecycleOwner androidx.lifecycle  BottomNavigationView ,com.google.android.material.bottomnavigation  Gson com.google.gson  FirebaseRemoteConfigHelper com.tqhit.adlib.sdk.firebase  data %com.tqhit.battery.one.features.charge  domain %com.tqhit.battery.one.features.charge  ChargeNotificationManager ,com.tqhit.battery.one.features.charge.common  
AppRepository Jcom.tqhit.battery.one.features.charge.common.ChargeNotificationManagerTest  ChargeNotificationManager Jcom.tqhit.battery.one.features.charge.common.ChargeNotificationManagerTest  Context Jcom.tqhit.battery.one.features.charge.common.ChargeNotificationManagerTest  AndroidBatteryStatsDataSource 0com.tqhit.battery.one.features.charge.datasource  AndroidBatteryStatsDataSource Rcom.tqhit.battery.one.features.charge.datasource.AndroidBatteryStatsDataSourceTest  BatteryManager Rcom.tqhit.battery.one.features.charge.datasource.AndroidBatteryStatsDataSourceTest  Context Rcom.tqhit.battery.one.features.charge.datasource.AndroidBatteryStatsDataSourceTest  Intent Rcom.tqhit.battery.one.features.charge.datasource.AndroidBatteryStatsDataSourceTest  CalculateChargeEstimatesUseCase 2com.tqhit.battery.one.features.charge.presentation  ChargeDataRepository 2com.tqhit.battery.one.features.charge.presentation  ManageChargeSessionUseCase 2com.tqhit.battery.one.features.charge.presentation  NewChargeViewModel 2com.tqhit.battery.one.features.charge.presentation  
AppRepository Icom.tqhit.battery.one.features.charge.presentation.NewChargeViewModelTest  CalculateChargeEstimatesUseCase Icom.tqhit.battery.one.features.charge.presentation.NewChargeViewModelTest  ChargeDataRepository Icom.tqhit.battery.one.features.charge.presentation.NewChargeViewModelTest  Context Icom.tqhit.battery.one.features.charge.presentation.NewChargeViewModelTest  ManageChargeSessionUseCase Icom.tqhit.battery.one.features.charge.presentation.NewChargeViewModelTest  NewChargeViewModel Icom.tqhit.battery.one.features.charge.presentation.NewChargeViewModelTest  BatteryStyleRepositoryImpl 4com.tqhit.battery.one.features.emoji.data.repository  OptIn 4com.tqhit.battery.one.features.emoji.data.repository  AssetManager Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  BatteryStyleRepositoryImpl Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  Context Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  FirebaseRemoteConfigHelper Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  Gson Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  BatteryStyle 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleRepository 6com.tqhit.battery.one.features.emoji.domain.repository  GetBatteryStylesUseCase 4com.tqhit.battery.one.features.emoji.domain.use_case  BatteryStyleRepository Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  GetBatteryStylesUseCase Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  BatteryGalleryViewModel 9com.tqhit.battery.one.features.emoji.presentation.gallery  OptIn 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryViewModel Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  CoreBatteryStatsProvider Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  GetBatteryStylesUseCase Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  Rule Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  BatteryStyleAdapter Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  String Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  Unit Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  BatteryStyle Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  BatteryStyleAdapter Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  Context Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  String Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  Unit Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  DynamicNavigationManager )com.tqhit.battery.one.features.navigation  BottomNavigationView Qcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerPerformanceTest  CoreBatteryStatsProvider Qcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerPerformanceTest  DynamicNavigationManager Qcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerPerformanceTest  FragmentManager Qcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerPerformanceTest  FragmentTransaction Qcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerPerformanceTest  LifecycleOwner Qcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerPerformanceTest  BottomNavigationView Fcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerTest  CoreBatteryStatsProvider Fcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerTest  DynamicNavigationManager Fcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerTest  FragmentManager Fcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerTest  FragmentTransaction Fcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerTest  LifecycleOwner Fcom.tqhit.battery.one.features.navigation.DynamicNavigationManagerTest  OptIn $com.tqhit.battery.one.features.stats  Context >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  CoreBatteryStatsProvider >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  'UnifiedBatteryNotificationServiceHelper >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  
AppRepository Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  $CalculateSimpleChargeEstimateUseCase Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  StatsChargeRepository Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  StatsChargeViewModel Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  $CalculateSimpleChargeEstimateUseCase 2com.tqhit.battery.one.features.stats.charge.domain  StatsChargeViewModel 8com.tqhit.battery.one.features.stats.charge.presentation  StatsChargeRepository 6com.tqhit.battery.one.features.stats.charge.repository  CoreBatteryStatsProvider 5com.tqhit.battery.one.features.stats.corebattery.data  CoreBatteryStatsProvider 7com.tqhit.battery.one.features.stats.corebattery.domain  ScreenStateTimeTracker Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  CurrentSessionCache 4com.tqhit.battery.one.features.stats.discharge.cache  DischargeRatesCache 4com.tqhit.battery.one.features.stats.discharge.cache  ScreenStateReceiver 9com.tqhit.battery.one.features.stats.discharge.datasource  AppLifecycleManager 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeRateCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  FullSessionReEstimator 5com.tqhit.battery.one.features.stats.discharge.domain  GapEstimationCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  OptIn 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenStateTimeTracker 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeValidationService 5com.tqhit.battery.one.features.stats.discharge.domain  SessionManager 5com.tqhit.battery.one.features.stats.discharge.domain  SessionMetricsCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  
TimeConverter 5com.tqhit.battery.one.features.stats.discharge.domain  AppLifecycleManager Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  LifecycleOwner Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  DischargeCalculator Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest  ScreenStateTimeTracker Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  DischargeRateCalculator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  DischargeRatesCache Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  FullSessionReEstimator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  ScreenTimeCalculator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  
TimeConverter Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  AppLifecycleManager Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  ScreenTimeValidationService Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  
TimeConverter Gcom.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest  FullSessionReEstimator :com.tqhit.battery.one.features.stats.discharge.integration  GapEstimationCalculator :com.tqhit.battery.one.features.stats.discharge.integration  OptIn :com.tqhit.battery.one.features.stats.discharge.integration  SessionManager :com.tqhit.battery.one.features.stats.discharge.integration  SessionMetricsCalculator :com.tqhit.battery.one.features.stats.discharge.integration  Context ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  CurrentSessionCache ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  DischargeSessionRepository ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  FullSessionReEstimator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  GapEstimationCalculator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  PowerManager ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  ScreenStateReceiver ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  SessionManager ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  SessionMetricsCalculator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  AppLifecycleManager \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  ScreenTimeValidationService \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  DischargeSessionRepository 9com.tqhit.battery.one.features.stats.discharge.repository  FullSessionReEstimator 9com.tqhit.battery.one.features.stats.discharge.repository  GapEstimationCalculator 9com.tqhit.battery.one.features.stats.discharge.repository  OptIn 9com.tqhit.battery.one.features.stats.discharge.repository  SessionManager 9com.tqhit.battery.one.features.stats.discharge.repository  SessionMetricsCalculator 9com.tqhit.battery.one.features.stats.discharge.repository  Context Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  CurrentSessionCache Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  DischargeSessionRepository Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  FullSessionReEstimator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  GapEstimationCalculator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  PowerManager Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  ScreenStateReceiver Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  SessionManager Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  SessionMetricsCalculator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  HealthCache 1com.tqhit.battery.one.features.stats.health.cache  CalculateBatteryHealthUseCase 2com.tqhit.battery.one.features.stats.health.domain  CalculateBatteryHealthUseCase Tcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest  CoreBatteryStatsProvider 6com.tqhit.battery.one.features.stats.health.repository  DefaultHealthRepository 6com.tqhit.battery.one.features.stats.health.repository  
AppRepository Pcom.tqhit.battery.one.features.stats.health.repository.HealthRepositoryChartTest  BatteryRepository Pcom.tqhit.battery.one.features.stats.health.repository.HealthRepositoryChartTest  ChargingSessionManager Pcom.tqhit.battery.one.features.stats.health.repository.HealthRepositoryChartTest  CoreBatteryStatsProvider Pcom.tqhit.battery.one.features.stats.health.repository.HealthRepositoryChartTest  DefaultHealthRepository Pcom.tqhit.battery.one.features.stats.health.repository.HealthRepositoryChartTest  HealthCache Pcom.tqhit.battery.one.features.stats.health.repository.HealthRepositoryChartTest  'UnifiedBatteryNotificationServiceHelper 2com.tqhit.battery.one.features.stats.notifications  ChargingSessionManager $com.tqhit.battery.one.manager.charge  
AppRepository  com.tqhit.battery.one.repository  BatteryRepository  com.tqhit.battery.one.repository  Context ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  PackageManager ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  PowerManager ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  SharedPreferences ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  AndroidBatteryStatsDataSource io.mockk  ChargeNotificationManager io.mockk  CoreBatteryStatsProvider io.mockk  FullSessionReEstimator io.mockk  GapEstimationCalculator io.mockk  SessionManager io.mockk  SessionMetricsCalculator io.mockk  AndroidBatteryStatsDataSource 	java.lang  CalculateChargeEstimatesUseCase 	java.lang  ChargeDataRepository 	java.lang  ChargeNotificationManager 	java.lang  CoreBatteryStatsProvider 	java.lang  ManageChargeSessionUseCase 	java.lang  NewChargeViewModel 	java.lang  AndroidBatteryStatsDataSource kotlin  CalculateChargeEstimatesUseCase kotlin  ChargeDataRepository kotlin  ChargeNotificationManager kotlin  CoreBatteryStatsProvider kotlin  ManageChargeSessionUseCase kotlin  NewChargeViewModel kotlin  OptIn kotlin  String kotlin  Unit kotlin  AndroidBatteryStatsDataSource kotlin.annotation  CalculateChargeEstimatesUseCase kotlin.annotation  ChargeDataRepository kotlin.annotation  ChargeNotificationManager kotlin.annotation  CoreBatteryStatsProvider kotlin.annotation  ManageChargeSessionUseCase kotlin.annotation  NewChargeViewModel kotlin.annotation  AndroidBatteryStatsDataSource kotlin.collections  CalculateChargeEstimatesUseCase kotlin.collections  ChargeDataRepository kotlin.collections  ChargeNotificationManager kotlin.collections  CoreBatteryStatsProvider kotlin.collections  ManageChargeSessionUseCase kotlin.collections  NewChargeViewModel kotlin.collections  AndroidBatteryStatsDataSource kotlin.comparisons  CalculateChargeEstimatesUseCase kotlin.comparisons  ChargeDataRepository kotlin.comparisons  ChargeNotificationManager kotlin.comparisons  CoreBatteryStatsProvider kotlin.comparisons  ManageChargeSessionUseCase kotlin.comparisons  NewChargeViewModel kotlin.comparisons  AndroidBatteryStatsDataSource 	kotlin.io  CalculateChargeEstimatesUseCase 	kotlin.io  ChargeDataRepository 	kotlin.io  ChargeNotificationManager 	kotlin.io  CoreBatteryStatsProvider 	kotlin.io  ManageChargeSessionUseCase 	kotlin.io  NewChargeViewModel 	kotlin.io  AndroidBatteryStatsDataSource 
kotlin.jvm  CalculateChargeEstimatesUseCase 
kotlin.jvm  ChargeDataRepository 
kotlin.jvm  ChargeNotificationManager 
kotlin.jvm  CoreBatteryStatsProvider 
kotlin.jvm  ManageChargeSessionUseCase 
kotlin.jvm  NewChargeViewModel 
kotlin.jvm  AndroidBatteryStatsDataSource 
kotlin.ranges  CalculateChargeEstimatesUseCase 
kotlin.ranges  ChargeDataRepository 
kotlin.ranges  ChargeNotificationManager 
kotlin.ranges  CoreBatteryStatsProvider 
kotlin.ranges  ManageChargeSessionUseCase 
kotlin.ranges  NewChargeViewModel 
kotlin.ranges  AndroidBatteryStatsDataSource kotlin.sequences  CalculateChargeEstimatesUseCase kotlin.sequences  ChargeDataRepository kotlin.sequences  ChargeNotificationManager kotlin.sequences  CoreBatteryStatsProvider kotlin.sequences  ManageChargeSessionUseCase kotlin.sequences  NewChargeViewModel kotlin.sequences  AndroidBatteryStatsDataSource kotlin.text  CalculateChargeEstimatesUseCase kotlin.text  ChargeDataRepository kotlin.text  ChargeNotificationManager kotlin.text  CoreBatteryStatsProvider kotlin.text  ManageChargeSessionUseCase kotlin.text  NewChargeViewModel kotlin.text  ExperimentalCoroutinesApi kotlinx.coroutines  Assert 	org.junit  Rule 	org.junit  CoreBatteryStatsProvider org.junit.Assert  FullSessionReEstimator org.junit.Assert  GapEstimationCalculator org.junit.Assert  SessionManager org.junit.Assert  SessionMetricsCalculator org.junit.Assert  RunWith org.junit.runner  Config org.robolectric.annotation                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            