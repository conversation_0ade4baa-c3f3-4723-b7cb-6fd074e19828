<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.tqhit.battery.one.features.emoji.presentation.gallery</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.tqhit.battery.one.features.emoji.presentation.gallery</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.tqhit.battery.one.features.emoji.presentation.gallery</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">14</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">14</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.250s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#ClearAllFilters event resets all filters">ClearAllFilters event resets all filters</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#DismissError event clears error message">DismissError event clears error message</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#FilterByCategory event filters styles correctly">FilterByCategory event filters styles correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#LoadInitialData event loads styles">LoadInitialData event loads styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#RefreshData event triggers refresh">RefreshData event triggers refresh</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#SearchStyles event filters by search query">SearchStyles event filters by search query</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#SelectStyle event updates selected style">SelectStyle event updates selected style</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#ToggleShowOnlyFree event filters free styles">ToggleShowOnlyFree event filters free styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#ToggleShowOnlyPopular event filters popular styles">ToggleShowOnlyPopular event filters popular styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#ToggleShowOnlyPremium event filters premium styles">ToggleShowOnlyPremium event filters premium styles</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#battery state changes are handled correctly">battery state changes are handled correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#error handling works correctly">error handling works correctly</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#initial state is correct">initial state is correct</a>
</li>
<li>
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>.
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html#multiple filter combinations work correctly">multiple filter combinations work correctly</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/com.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest.html">BatteryGalleryViewModelTest</a>
</td>
<td>14</td>
<td>14</td>
<td>0</td>
<td>0.250s</td>
<td class="failures">0%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jun 20, 2025, 2:45:19 PM</p>
</div>
</div>
</body>
</html>
