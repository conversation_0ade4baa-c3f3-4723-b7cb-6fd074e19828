package com.tqhit.battery.one.features.emoji.domain.use_case;

import com.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class GetBatteryStylesUseCase_Factory implements Factory<GetBatteryStylesUseCase> {
  private final Provider<BatteryStyleRepository> batteryStyleRepositoryProvider;

  public GetBatteryStylesUseCase_Factory(
      Provider<BatteryStyleRepository> batteryStyleRepositoryProvider) {
    this.batteryStyleRepositoryProvider = batteryStyleRepositoryProvider;
  }

  @Override
  public GetBatteryStylesUseCase get() {
    return newInstance(batteryStyleRepositoryProvider.get());
  }

  public static GetBatteryStylesUseCase_Factory create(
      Provider<BatteryStyleRepository> batteryStyleRepositoryProvider) {
    return new GetBatteryStylesUseCase_Factory(batteryStyleRepositoryProvider);
  }

  public static GetBatteryStylesUseCase newInstance(BatteryStyleRepository batteryStyleRepository) {
    return new GetBatteryStylesUseCase(batteryStyleRepository);
  }
}
