<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" tests="13" skipped="0" failures="0" errors="0" timestamp="2025-06-20T07:45:18" hostname="DESKTOP-KBSUI08" time="0.001">
  <properties/>
  <testcase name="test isValid returns false for blank name" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test isValid returns false for empty emoji image URL" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test matchesSearch with matching category" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test matchesSearch with matching name" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test isValid returns false for empty battery image URL" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test valid battery style creation" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test getPreviewId generates unique identifier" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test battery style with default values" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test createDefault returns valid default style" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test isValid returns true for valid style" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test isValid returns false for empty id" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.001"/>
  <testcase name="test matchesSearch with empty query returns true" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <testcase name="test matchesSearch with non-matching query" classname="com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
