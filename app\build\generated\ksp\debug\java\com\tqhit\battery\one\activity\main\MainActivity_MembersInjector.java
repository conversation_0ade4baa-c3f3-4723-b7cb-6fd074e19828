package com.tqhit.battery.one.activity.main;

import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper;
import com.tqhit.battery.one.ads.core.ApplovinBannerAdManager;
import com.tqhit.battery.one.features.navigation.DynamicNavigationManager;
import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager;
import com.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import com.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository;
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper;
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<StatsChargeRepository> statsChargeRepositoryProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<ApplovinBannerAdManager> applovinBannerAdManagerProvider;

  private final Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider;

  private final Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider;

  private final Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider;

  private final Provider<UnifiedBatteryNotificationServiceHelper> unifiedBatteryNotificationServiceHelperProvider;

  private final Provider<UsageStatsPermissionManager> usageStatsPermissionManagerProvider;

  private final Provider<DynamicNavigationManager> dynamicNavigationManagerProvider;

  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  public MainActivity_MembersInjector(Provider<StatsChargeRepository> statsChargeRepositoryProvider,
      Provider<AppRepository> appRepositoryProvider,
      Provider<ApplovinBannerAdManager> applovinBannerAdManagerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider,
      Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider,
      Provider<UnifiedBatteryNotificationServiceHelper> unifiedBatteryNotificationServiceHelperProvider,
      Provider<UsageStatsPermissionManager> usageStatsPermissionManagerProvider,
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    this.statsChargeRepositoryProvider = statsChargeRepositoryProvider;
    this.appRepositoryProvider = appRepositoryProvider;
    this.applovinBannerAdManagerProvider = applovinBannerAdManagerProvider;
    this.remoteConfigHelperProvider = remoteConfigHelperProvider;
    this.enhancedDischargeTimerServiceHelperProvider = enhancedDischargeTimerServiceHelperProvider;
    this.dischargeSessionRepositoryProvider = dischargeSessionRepositoryProvider;
    this.unifiedBatteryNotificationServiceHelperProvider = unifiedBatteryNotificationServiceHelperProvider;
    this.usageStatsPermissionManagerProvider = usageStatsPermissionManagerProvider;
    this.dynamicNavigationManagerProvider = dynamicNavigationManagerProvider;
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  public static MembersInjector<MainActivity> create(
      Provider<StatsChargeRepository> statsChargeRepositoryProvider,
      Provider<AppRepository> appRepositoryProvider,
      Provider<ApplovinBannerAdManager> applovinBannerAdManagerProvider,
      Provider<FirebaseRemoteConfigHelper> remoteConfigHelperProvider,
      Provider<EnhancedDischargeTimerServiceHelper> enhancedDischargeTimerServiceHelperProvider,
      Provider<DischargeSessionRepository> dischargeSessionRepositoryProvider,
      Provider<UnifiedBatteryNotificationServiceHelper> unifiedBatteryNotificationServiceHelperProvider,
      Provider<UsageStatsPermissionManager> usageStatsPermissionManagerProvider,
      Provider<DynamicNavigationManager> dynamicNavigationManagerProvider,
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider) {
    return new MainActivity_MembersInjector(statsChargeRepositoryProvider, appRepositoryProvider, applovinBannerAdManagerProvider, remoteConfigHelperProvider, enhancedDischargeTimerServiceHelperProvider, dischargeSessionRepositoryProvider, unifiedBatteryNotificationServiceHelperProvider, usageStatsPermissionManagerProvider, dynamicNavigationManagerProvider, coreBatteryStatsProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectStatsChargeRepository(instance, statsChargeRepositoryProvider.get());
    injectAppRepository(instance, appRepositoryProvider.get());
    injectApplovinBannerAdManager(instance, applovinBannerAdManagerProvider.get());
    injectRemoteConfigHelper(instance, remoteConfigHelperProvider.get());
    injectEnhancedDischargeTimerServiceHelper(instance, enhancedDischargeTimerServiceHelperProvider.get());
    injectDischargeSessionRepository(instance, dischargeSessionRepositoryProvider.get());
    injectUnifiedBatteryNotificationServiceHelper(instance, unifiedBatteryNotificationServiceHelperProvider.get());
    injectUsageStatsPermissionManager(instance, usageStatsPermissionManagerProvider.get());
    injectDynamicNavigationManager(instance, dynamicNavigationManagerProvider.get());
    injectCoreBatteryStatsProvider(instance, coreBatteryStatsProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.statsChargeRepository")
  public static void injectStatsChargeRepository(MainActivity instance,
      StatsChargeRepository statsChargeRepository) {
    instance.statsChargeRepository = statsChargeRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.appRepository")
  public static void injectAppRepository(MainActivity instance, AppRepository appRepository) {
    instance.appRepository = appRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.applovinBannerAdManager")
  public static void injectApplovinBannerAdManager(MainActivity instance,
      ApplovinBannerAdManager applovinBannerAdManager) {
    instance.applovinBannerAdManager = applovinBannerAdManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.remoteConfigHelper")
  public static void injectRemoteConfigHelper(MainActivity instance,
      FirebaseRemoteConfigHelper remoteConfigHelper) {
    instance.remoteConfigHelper = remoteConfigHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.enhancedDischargeTimerServiceHelper")
  public static void injectEnhancedDischargeTimerServiceHelper(MainActivity instance,
      EnhancedDischargeTimerServiceHelper enhancedDischargeTimerServiceHelper) {
    instance.enhancedDischargeTimerServiceHelper = enhancedDischargeTimerServiceHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.dischargeSessionRepository")
  public static void injectDischargeSessionRepository(MainActivity instance,
      DischargeSessionRepository dischargeSessionRepository) {
    instance.dischargeSessionRepository = dischargeSessionRepository;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.unifiedBatteryNotificationServiceHelper")
  public static void injectUnifiedBatteryNotificationServiceHelper(MainActivity instance,
      UnifiedBatteryNotificationServiceHelper unifiedBatteryNotificationServiceHelper) {
    instance.unifiedBatteryNotificationServiceHelper = unifiedBatteryNotificationServiceHelper;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.usageStatsPermissionManager")
  public static void injectUsageStatsPermissionManager(MainActivity instance,
      UsageStatsPermissionManager usageStatsPermissionManager) {
    instance.usageStatsPermissionManager = usageStatsPermissionManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.dynamicNavigationManager")
  public static void injectDynamicNavigationManager(MainActivity instance,
      DynamicNavigationManager dynamicNavigationManager) {
    instance.dynamicNavigationManager = dynamicNavigationManager;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.main.MainActivity.coreBatteryStatsProvider")
  public static void injectCoreBatteryStatsProvider(MainActivity instance,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    instance.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }
}
