package com.tqhit.battery.one.viewmodel.battery;

import android.content.Context;
import com.tqhit.battery.one.repository.BatteryRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class BatteryViewModel_Factory implements Factory<BatteryViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<BatteryRepository> repositoryProvider;

  public BatteryViewModel_Factory(Provider<Context> contextProvider,
      Provider<BatteryRepository> repositoryProvider) {
    this.contextProvider = contextProvider;
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public BatteryViewModel get() {
    return newInstance(contextProvider.get(), repositoryProvider.get());
  }

  public static BatteryViewModel_Factory create(Provider<Context> contextProvider,
      Provider<BatteryRepository> repositoryProvider) {
    return new BatteryViewModel_Factory(contextProvider, repositoryProvider);
  }

  public static BatteryViewModel newInstance(Context context, BatteryRepository repository) {
    return new BatteryViewModel(context, repository);
  }
}
