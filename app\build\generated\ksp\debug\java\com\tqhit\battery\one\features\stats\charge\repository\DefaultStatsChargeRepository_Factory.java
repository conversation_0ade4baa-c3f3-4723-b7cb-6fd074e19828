package com.tqhit.battery.one.features.stats.charge.repository;

import com.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DefaultStatsChargeRepository_Factory implements Factory<DefaultStatsChargeRepository> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<StatsChargeCache> statsChargeCacheProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  public DefaultStatsChargeRepository_Factory(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<StatsChargeCache> statsChargeCacheProvider,
      Provider<AppRepository> appRepositoryProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.statsChargeCacheProvider = statsChargeCacheProvider;
    this.appRepositoryProvider = appRepositoryProvider;
  }

  @Override
  public DefaultStatsChargeRepository get() {
    return newInstance(coreBatteryStatsProvider.get(), statsChargeCacheProvider.get(), appRepositoryProvider.get());
  }

  public static DefaultStatsChargeRepository_Factory create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<StatsChargeCache> statsChargeCacheProvider,
      Provider<AppRepository> appRepositoryProvider) {
    return new DefaultStatsChargeRepository_Factory(coreBatteryStatsProvider, statsChargeCacheProvider, appRepositoryProvider);
  }

  public static DefaultStatsChargeRepository newInstance(
      CoreBatteryStatsProvider coreBatteryStatsProvider, StatsChargeCache statsChargeCache,
      AppRepository appRepository) {
    return new DefaultStatsChargeRepository(coreBatteryStatsProvider, statsChargeCache, appRepository);
  }
}
