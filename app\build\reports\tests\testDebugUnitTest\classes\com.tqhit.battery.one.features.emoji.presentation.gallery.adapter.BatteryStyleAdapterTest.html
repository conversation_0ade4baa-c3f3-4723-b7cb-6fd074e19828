<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.html">com.tqhit.battery.one.features.emoji.presentation.gallery.adapter</a> &gt; BatteryStyleAdapterTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">15</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">15</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.358s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="action button is hidden for free styles"></a>
<h3 class="failures">action button is hidden for free styles</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="action button is visible for premium styles"></a>
<h3 class="failures">action button is visible for premium styles</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="adapter creates correct number of view holders"></a>
<h3 class="failures">adapter creates correct number of view holders</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="adapter handles empty list correctly"></a>
<h3 class="failures">adapter handles empty list correctly</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="adapter handles list updates correctly"></a>
<h3 class="failures">adapter handles list updates correctly</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="adapter returns correct item at position"></a>
<h3 class="failures">adapter returns correct item at position</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="category text includes premium status"></a>
<h3 class="failures">category text includes premium status</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="popular badge is hidden for non-popular styles"></a>
<h3 class="failures">popular badge is hidden for non-popular styles</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="popular badge is visible for popular styles"></a>
<h3 class="failures">popular badge is visible for popular styles</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="premium badge is hidden for free styles"></a>
<h3 class="failures">premium badge is hidden for free styles</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="premium badge is visible for premium styles"></a>
<h3 class="failures">premium badge is visible for premium styles</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="premium unlock click triggers callback"></a>
<h3 class="failures">premium unlock click triggers callback</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="style click triggers callback"></a>
<h3 class="failures">style click triggers callback</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="style long click triggers callback"></a>
<h3 class="failures">style long click triggers callback</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
<div class="test">
<a name="view holder binds style data correctly"></a>
<h3 class="failures">view holder binds style data correctly</h3>
<span class="code">
<pre>java.lang.IllegalStateException: Default FirebaseApp is not initialized in this process com.fc.p.tj.charginganimation.batterycharging.chargeeffect. Make sure to call FirebaseApp.initializeApp(Context) first.
	at com.google.firebase.FirebaseApp.getInstance(FirebaseApp.java:179)
	at com.google.firebase.remoteconfig.FirebaseRemoteConfig.getInstance(FirebaseRemoteConfig.java:81)
	at com.google.firebase.remoteconfig.ktx.RemoteConfigKt.getRemoteConfig(RemoteConfig.kt:48)
	at com.tqhit.adlib.sdk.di.FirebaseModule.provideFirebaseRemoteConfig(FirebaseModule.kt:37)
	at com.tqhit.adlib.sdk.di.FirebaseModule_ProvideFirebaseRemoteConfigFactory.provideFirebaseRemoteConfig(FirebaseModule_ProvideFirebaseRemoteConfigFactory.java:38)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1230)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1227)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1224)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl$SwitchingProvider.get(DaggerBatteryApplication_HiltComponents_SingletonC.java:1194)
	at dagger.internal.DoubleCheck.getSynchronized(DoubleCheck.java:54)
	at dagger.internal.DoubleCheck.get(DoubleCheck.java:45)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication2(DaggerBatteryApplication_HiltComponents_SingletonC.java:1163)
	at com.tqhit.battery.one.DaggerBatteryApplication_HiltComponents_SingletonC$SingletonCImpl.injectBatteryApplication(DaggerBatteryApplication_HiltComponents_SingletonC.java:1143)
	at com.tqhit.battery.one.Hilt_BatteryApplication.hiltInternalInject(Hilt_BatteryApplication.java:52)
	at com.tqhit.battery.one.Hilt_BatteryApplication.onCreate(Hilt_BatteryApplication.java:43)
	at com.tqhit.battery.one.BatteryApplication.onCreate(BatteryApplication.kt:75)
	at android.app.Instrumentation.callApplicationOnCreate(Instrumentation.java:1192)
	at org.robolectric.android.internal.RoboMonitoringInstrumentation.callApplicationOnCreate(RoboMonitoringInstrumentation.java:148)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$installAndCreateApplication$2(AndroidTestEnvironment.java:381)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:86)
	at org.robolectric.android.internal.AndroidTestEnvironment.installAndCreateApplication(AndroidTestEnvironment.java:379)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$0(AndroidTestEnvironment.java:250)
	at org.robolectric.util.PerfStatsCollector.measure(PerfStatsCollector.java:53)
	at org.robolectric.android.internal.AndroidTestEnvironment.lambda$createApplicationSupplier$1(AndroidTestEnvironment.java:247)
	at com.google.common.base.Suppliers$NonSerializableMemoizingSupplier.get(Suppliers.java:200)
	at org.robolectric.RuntimeEnvironment.lambda$getApplication$0(RuntimeEnvironment.java:80)
	at org.robolectric.shadows.ShadowInstrumentation.runOnMainSyncNoIdle(ShadowInstrumentation.java:1201)
	at org.robolectric.RuntimeEnvironment.getApplication(RuntimeEnvironment.java:80)
	at org.robolectric.android.internal.AndroidTestEnvironment.setUpApplicationState(AndroidTestEnvironment.java:215)
	at org.robolectric.RobolectricTestRunner.beforeTest(RobolectricTestRunner.java:340)
	at org.robolectric.internal.SandboxTestRunner$2.lambda$evaluate$2(SandboxTestRunner.java:281)
	at org.robolectric.internal.bytecode.Sandbox.lambda$runOnMainThread$0(Sandbox.java:101)
	at java.base/java.util.concurrent.FutureTask.run(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at java.base/java.lang.Thread.run(Unknown Source)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="failures">action button is hidden for free styles</td>
<td class="failures">0.028s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">action button is visible for premium styles</td>
<td class="failures">0.022s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">adapter creates correct number of view holders</td>
<td class="failures">0.024s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">adapter handles empty list correctly</td>
<td class="failures">0.020s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">adapter handles list updates correctly</td>
<td class="failures">0.031s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">adapter returns correct item at position</td>
<td class="failures">0.017s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">category text includes premium status</td>
<td class="failures">0.020s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">popular badge is hidden for non-popular styles</td>
<td class="failures">0.019s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">popular badge is visible for popular styles</td>
<td class="failures">0.021s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">premium badge is hidden for free styles</td>
<td class="failures">0.022s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">premium badge is visible for premium styles</td>
<td class="failures">0.029s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">premium unlock click triggers callback</td>
<td class="failures">0.018s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">style click triggers callback</td>
<td class="failures">0.020s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">style long click triggers callback</td>
<td class="failures">0.032s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">view holder binds style data correctly</td>
<td class="failures">0.035s</td>
<td class="failures">failed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at Jun 20, 2025, 2:45:19 PM</p>
</div>
</div>
</body>
</html>
