package com.tqhit.battery.one.features.stats.health.repository

import com.tqhit.battery.one.features.stats.health.cache.HealthCache
import com.tqhit.battery.one.features.stats.health.data.HealthCalculationMode
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatsProvider
import com.tqhit.battery.one.manager.charge.ChargingSessionManager
import com.tqhit.battery.one.manager.graph.HistoryEntry
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.repository.BatteryRepository
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for HealthRepository chart functionality.
 * Tests chart data generation, time range filtering, and fallback mechanisms.
 */
@ExperimentalCoroutinesApi
class HealthRepositoryChartTest {

    private lateinit var healthRepository: DefaultHealthRepository
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider
    private lateinit var mockChargingSessionManager: ChargingSessionManager
    private lateinit var mockAppRepository: AppRepository
    private lateinit var mockHealthCache: HealthCache
    private lateinit var mockBatteryRepository: BatteryRepository

    @Before
    fun setup() {
        mockCoreBatteryStatsProvider = mockk(relaxed = true)
        mockChargingSessionManager = mockk(relaxed = true)
        mockAppRepository = mockk(relaxed = true)
        mockHealthCache = mockk(relaxed = true)
        mockBatteryRepository = mockk(relaxed = true)

        // Setup default mocks
        every { mockAppRepository.getBatteryCapacity() } returns 1000
        coEvery { mockHealthCache.getCalculationMode() } returns HealthCalculationMode.CUMULATIVE
        every { mockChargingSessionManager.getTotalSessions() } returns 10

        healthRepository = DefaultHealthRepository(
            mockCoreBatteryStatsProvider,
            mockChargingSessionManager,
            mockAppRepository,
            mockHealthCache,
            mockBatteryRepository
        )
    }

    @Test
    fun `updateChartData with historical data uses real data`() = runTest {
        // Arrange
        val timeRange = 4
        val batteryHistory = listOf(
            HistoryEntry(System.currentTimeMillis() - 3600000, 80),
            HistoryEntry(System.currentTimeMillis() - 1800000, 75),
            HistoryEntry(System.currentTimeMillis(), 70)
        )
        val temperatureHistory = listOf(
            HistoryEntry(System.currentTimeMillis() - 3600000, 25.0),
            HistoryEntry(System.currentTimeMillis() - 1800000, 27.0),
            HistoryEntry(System.currentTimeMillis(), 29.0)
        )
        val dailyWearData = listOf(0.5, 0.8, 0.3, 0.6, 0.9, 0.4, 0.7)

        every { mockBatteryRepository.getHistoryBatteryForHours(timeRange) } returns batteryHistory
        every { mockBatteryRepository.getHistoryTemperatureForHours(timeRange) } returns temperatureHistory
        every { mockBatteryRepository.getDailyWearData(7) } returns dailyWearData

        // Act
        healthRepository.updateChartData(timeRange)

        // Assert
        val chartData = healthRepository.healthChartDataFlow.first()
        
        assertEquals("Should use specified time range", timeRange, chartData.selectedTimeRangeHours)
        assertEquals("Should have battery entries from historical data", 3, chartData.batteryPercentageEntries.size)
        assertEquals("Should have temperature entries from historical data", 3, chartData.temperatureEntries.size)
        assertEquals("Should use provided daily wear data", dailyWearData, chartData.dailyWearData)
        
        // Verify battery values
        assertEquals(80f, chartData.batteryPercentageEntries[0].y, 0.01f)
        assertEquals(75f, chartData.batteryPercentageEntries[1].y, 0.01f)
        assertEquals(70f, chartData.batteryPercentageEntries[2].y, 0.01f)
        
        // Verify temperature values
        assertEquals(25f, chartData.temperatureEntries[0].y, 0.01f)
        assertEquals(27f, chartData.temperatureEntries[1].y, 0.01f)
        assertEquals(29f, chartData.temperatureEntries[2].y, 0.01f)
    }

    @Test
    fun `updateChartData without historical data generates sample data`() = runTest {
        // Arrange
        val timeRange = 8
        every { mockBatteryRepository.getHistoryBatteryForHours(timeRange) } returns emptyList()
        every { mockBatteryRepository.getHistoryTemperatureForHours(timeRange) } returns emptyList()
        every { mockBatteryRepository.getDailyWearData(7) } returns emptyList()

        // Act
        healthRepository.updateChartData(timeRange)

        // Assert
        val chartData = healthRepository.healthChartDataFlow.first()
        
        assertEquals("Should use specified time range", timeRange, chartData.selectedTimeRangeHours)
        assertTrue("Should generate sample battery data", chartData.batteryPercentageEntries.isNotEmpty())
        assertTrue("Should generate sample temperature data", chartData.temperatureEntries.isNotEmpty())
        assertEquals("Should have 7 days of wear data", 7, chartData.dailyWearData.size)
        
        // Verify sample data is realistic
        chartData.batteryPercentageEntries.forEach { entry ->
            assertTrue("Battery percentage should be valid", entry.y in 20f..100f)
        }
        
        chartData.temperatureEntries.forEach { entry ->
            assertTrue("Temperature should be realistic", entry.y in 18f..42f)
        }
    }

    @Test
    fun `updateChartData handles different time ranges correctly`() = runTest {
        // Arrange
        val timeRanges = listOf(4, 8, 12, 24)
        every { mockBatteryRepository.getHistoryBatteryForHours(any()) } returns emptyList()
        every { mockBatteryRepository.getHistoryTemperatureForHours(any()) } returns emptyList()
        every { mockBatteryRepository.getDailyWearData(7) } returns emptyList()

        timeRanges.forEach { timeRange ->
            // Act
            healthRepository.updateChartData(timeRange)

            // Assert
            val chartData = healthRepository.healthChartDataFlow.first()
            
            assertEquals("Time range should match for ${timeRange}h", timeRange, chartData.selectedTimeRangeHours)
            
            val expectedDataPoints = when (timeRange) {
                4 -> 24
                8 -> 32
                12 -> 36
                24 -> 48
                else -> 24
            }
            
            assertTrue("Should have appropriate data points for ${timeRange}h", 
                chartData.batteryPercentageEntries.size >= expectedDataPoints - 5)
        }
    }

    @Test
    fun `updateChartData handles repository exceptions gracefully`() = runTest {
        // Arrange
        val timeRange = 4
        every { mockBatteryRepository.getHistoryBatteryForHours(timeRange) } throws RuntimeException("Database error")

        // Act
        healthRepository.updateChartData(timeRange)

        // Assert
        val chartData = healthRepository.healthChartDataFlow.first()
        
        // Should fall back to sample data
        assertEquals("Should use specified time range", timeRange, chartData.selectedTimeRangeHours)
        assertTrue("Should have fallback battery data", chartData.batteryPercentageEntries.isNotEmpty())
        assertTrue("Should have fallback temperature data", chartData.temperatureEntries.isNotEmpty())
        assertEquals("Should have 7 days of wear data", 7, chartData.dailyWearData.size)
    }

    @Test
    fun `chart data generation produces realistic battery discharge pattern`() = runTest {
        // Arrange
        every { mockBatteryRepository.getHistoryBatteryForHours(any()) } returns emptyList()
        every { mockBatteryRepository.getHistoryTemperatureForHours(any()) } returns emptyList()
        every { mockBatteryRepository.getDailyWearData(7) } returns emptyList()

        // Act
        healthRepository.updateChartData(24) // 24-hour range for clear pattern

        // Assert
        val chartData = healthRepository.healthChartDataFlow.first()
        val batteryValues = chartData.batteryPercentageEntries.map { it.y }
        
        val firstValue = batteryValues.first()
        val lastValue = batteryValues.last()
        
        assertTrue("Battery should start at reasonable level", firstValue > 70f)
        assertTrue("Battery should discharge over time", firstValue > lastValue)
        assertTrue("Battery should not discharge completely", lastValue > 15f)
        
        // Check for gradual discharge (no sudden jumps)
        for (i in 1 until batteryValues.size) {
            val diff = kotlin.math.abs(batteryValues[i] - batteryValues[i-1])
            assertTrue("Battery changes should be gradual", diff < 15f)
        }
    }

    @Test
    fun `chart data generation produces correlated temperature data`() = runTest {
        // Arrange
        every { mockBatteryRepository.getHistoryBatteryForHours(any()) } returns emptyList()
        every { mockBatteryRepository.getHistoryTemperatureForHours(any()) } returns emptyList()
        every { mockBatteryRepository.getDailyWearData(7) } returns emptyList()

        // Act
        healthRepository.updateChartData(12)

        // Assert
        val chartData = healthRepository.healthChartDataFlow.first()
        val temperatureValues = chartData.temperatureEntries.map { it.y }
        
        val avgTemperature = temperatureValues.average()
        val minTemp = temperatureValues.minOrNull()!!
        val maxTemp = temperatureValues.maxOrNull()!!
        
        assertTrue("Average temperature should be realistic", avgTemperature in 20.0..35.0)
        assertTrue("Temperature should have reasonable variation", (maxTemp - minTemp) > 2f)
        assertTrue("All temperatures should be in valid range", 
            temperatureValues.all { it in 18f..42f })
    }

    @Test
    fun `getCurrentChartData returns current chart data`() = runTest {
        // Arrange
        every { mockBatteryRepository.getHistoryBatteryForHours(any()) } returns emptyList()
        every { mockBatteryRepository.getHistoryTemperatureForHours(any()) } returns emptyList()
        every { mockBatteryRepository.getDailyWearData(7) } returns emptyList()

        // Act
        healthRepository.updateChartData(4)
        val currentData = healthRepository.getCurrentChartData()

        // Assert
        assertNotNull("Should return current chart data", currentData)
        assertEquals("Should match flow data", 
            healthRepository.healthChartDataFlow.first(), currentData)
    }
}
