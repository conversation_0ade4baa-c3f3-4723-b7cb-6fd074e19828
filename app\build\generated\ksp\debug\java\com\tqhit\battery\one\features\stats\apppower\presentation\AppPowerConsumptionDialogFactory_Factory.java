package com.tqhit.battery.one.features.stats.apppower.presentation;

import com.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager;
import com.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppPowerConsumptionDialogFactory_Factory implements Factory<AppPowerConsumptionDialogFactory> {
  private final Provider<AppUsageStatsRepository> appUsageStatsRepositoryProvider;

  private final Provider<UsageStatsPermissionManager> permissionManagerProvider;

  public AppPowerConsumptionDialogFactory_Factory(
      Provider<AppUsageStatsRepository> appUsageStatsRepositoryProvider,
      Provider<UsageStatsPermissionManager> permissionManagerProvider) {
    this.appUsageStatsRepositoryProvider = appUsageStatsRepositoryProvider;
    this.permissionManagerProvider = permissionManagerProvider;
  }

  @Override
  public AppPowerConsumptionDialogFactory get() {
    return newInstance(appUsageStatsRepositoryProvider.get(), permissionManagerProvider.get());
  }

  public static AppPowerConsumptionDialogFactory_Factory create(
      Provider<AppUsageStatsRepository> appUsageStatsRepositoryProvider,
      Provider<UsageStatsPermissionManager> permissionManagerProvider) {
    return new AppPowerConsumptionDialogFactory_Factory(appUsageStatsRepositoryProvider, permissionManagerProvider);
  }

  public static AppPowerConsumptionDialogFactory newInstance(
      AppUsageStatsRepository appUsageStatsRepository,
      UsageStatsPermissionManager permissionManager) {
    return new AppPowerConsumptionDialogFactory(appUsageStatsRepository, permissionManager);
  }
}
