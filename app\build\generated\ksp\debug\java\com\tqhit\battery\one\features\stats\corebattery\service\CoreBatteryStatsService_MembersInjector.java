package com.tqhit.battery.one.features.stats.corebattery.service;

import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CoreBatteryStatsService_MembersInjector implements MembersInjector<CoreBatteryStatsService> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  public CoreBatteryStatsService_MembersInjector(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppRepository> appRepositoryProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.appRepositoryProvider = appRepositoryProvider;
  }

  public static MembersInjector<CoreBatteryStatsService> create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<AppRepository> appRepositoryProvider) {
    return new CoreBatteryStatsService_MembersInjector(coreBatteryStatsProvider, appRepositoryProvider);
  }

  @Override
  public void injectMembers(CoreBatteryStatsService instance) {
    injectCoreBatteryStatsProvider(instance, coreBatteryStatsProvider.get());
    injectAppRepository(instance, appRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.coreBatteryStatsProvider")
  public static void injectCoreBatteryStatsProvider(CoreBatteryStatsService instance,
      CoreBatteryStatsProvider coreBatteryStatsProvider) {
    instance.coreBatteryStatsProvider = coreBatteryStatsProvider;
  }

  @InjectedFieldSignature("com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService.appRepository")
  public static void injectAppRepository(CoreBatteryStatsService instance,
      AppRepository appRepository) {
    instance.appRepository = appRepository;
  }
}
