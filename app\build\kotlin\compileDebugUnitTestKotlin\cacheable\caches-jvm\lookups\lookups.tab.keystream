  Context android.content  SharedPreferences android.content  
POWER_SERVICE android.content.Context  assets android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  packageManager android.content.Context  packageName android.content.Context  Editor !android.content.SharedPreferences  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getLong !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  PackageManager android.content.pm  AssetManager android.content.res  open  android.content.res.AssetManager  Build 
android.os  PowerManager 
android.os  VERSION android.os.Build  SDK_INT android.os.Build.VERSION  LOLLIPOP android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  isIgnoringBatteryOptimizations android.os.PowerManager  
isInteractive android.os.PowerManager  LayoutInflater android.view  View android.view  GONE android.view.View  VISIBLE android.view.View  performClick android.view.View  performLongClick android.view.View  
visibility android.view.View  TextView android.widget  text android.widget.TextView  InstantTaskExecutorRule #androidx.arch.core.executor.testing  Fragment androidx.fragment.app  LifecycleOwner androidx.lifecycle  ProcessLifecycleOwner androidx.lifecycle  	Companion (androidx.lifecycle.ProcessLifecycleOwner  get (androidx.lifecycle.ProcessLifecycleOwner  get 2androidx.lifecycle.ProcessLifecycleOwner.Companion  RecyclerView androidx.recyclerview.widget  	itemCount (androidx.recyclerview.widget.ListAdapter  ApplicationProvider androidx.test.core.app  getApplicationContext *androidx.test.core.app.ApplicationProvider  Entry !com.github.mikephil.charting.data  y +com.github.mikephil.charting.data.BaseEntry  x 'com.github.mikephil.charting.data.Entry  y 'com.github.mikephil.charting.data.Entry  MaterialButton "com.google.android.material.button  performClick 1com.google.android.material.button.MaterialButton  text 1com.google.android.material.button.MaterialButton  
visibility 1com.google.android.material.button.MaterialButton  MaterialCardView  com.google.android.material.card  performClick 1com.google.android.material.card.MaterialCardView  performLongClick 1com.google.android.material.card.MaterialCardView  
visibility 1com.google.android.material.card.MaterialCardView  Gson com.google.gson  FirebaseRemoteConfigHelper com.tqhit.adlib.sdk.firebase  	getString 7com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper  ExampleUnitTest com.tqhit.battery.one  R com.tqhit.battery.one  Test com.tqhit.battery.one  assertEquals com.tqhit.battery.one  assertEquals %com.tqhit.battery.one.ExampleUnitTest  animationGridFragment com.tqhit.battery.one.R.id  chargeFragment com.tqhit.battery.one.R.id  dischargeFragment com.tqhit.battery.one.R.id  healthFragment com.tqhit.battery.one.R.id  settingsFragment com.tqhit.battery.one.R.id  ItemBatteryStyleBinding !com.tqhit.battery.one.databinding  actionButton 9com.tqhit.battery.one.databinding.ItemBatteryStyleBinding  categoryText 9com.tqhit.battery.one.databinding.ItemBatteryStyleBinding  popularBadge 9com.tqhit.battery.one.databinding.ItemBatteryStyleBinding  premiumBadge 9com.tqhit.battery.one.databinding.ItemBatteryStyleBinding  	styleCard 9com.tqhit.battery.one.databinding.ItemBatteryStyleBinding  
styleNameText 9com.tqhit.battery.one.databinding.ItemBatteryStyleBinding  Class $com.tqhit.battery.one.features.emoji  ClassNotFoundException $com.tqhit.battery.one.features.emoji  Config $com.tqhit.battery.one.features.emoji  EmojiBatteryDIModule $com.tqhit.battery.one.features.emoji  Phase0IntegrationTest $com.tqhit.battery.one.features.emoji  RobolectricTestRunner $com.tqhit.battery.one.features.emoji  RunWith $com.tqhit.battery.one.features.emoji  Test $com.tqhit.battery.one.features.emoji  
assertNotNull $com.tqhit.battery.one.features.emoji  
assertTrue $com.tqhit.battery.one.features.emoji  dagger $com.tqhit.battery.one.features.emoji  endsWith $com.tqhit.battery.one.features.emoji  java $com.tqhit.battery.one.features.emoji  listOf $com.tqhit.battery.one.features.emoji  Class :com.tqhit.battery.one.features.emoji.Phase0IntegrationTest  EmojiBatteryDIModule :com.tqhit.battery.one.features.emoji.Phase0IntegrationTest  
assertNotNull :com.tqhit.battery.one.features.emoji.Phase0IntegrationTest  
assertTrue :com.tqhit.battery.one.features.emoji.Phase0IntegrationTest  dagger :com.tqhit.battery.one.features.emoji.Phase0IntegrationTest  endsWith :com.tqhit.battery.one.features.emoji.Phase0IntegrationTest  java :com.tqhit.battery.one.features.emoji.Phase0IntegrationTest  listOf :com.tqhit.battery.one.features.emoji.Phase0IntegrationTest  AssetManager 4com.tqhit.battery.one.features.emoji.data.repository  BatteryStyleCategory 4com.tqhit.battery.one.features.emoji.data.repository  BatteryStyleRepositoryImpl 4com.tqhit.battery.one.features.emoji.data.repository  BatteryStyleRepositoryImplTest 4com.tqhit.battery.one.features.emoji.data.repository  Before 4com.tqhit.battery.one.features.emoji.data.repository  ByteArrayInputStream 4com.tqhit.battery.one.features.emoji.data.repository  Context 4com.tqhit.battery.one.features.emoji.data.repository  ExperimentalCoroutinesApi 4com.tqhit.battery.one.features.emoji.data.repository  FirebaseRemoteConfigHelper 4com.tqhit.battery.one.features.emoji.data.repository  Gson 4com.tqhit.battery.one.features.emoji.data.repository  IOException 4com.tqhit.battery.one.features.emoji.data.repository  OptIn 4com.tqhit.battery.one.features.emoji.data.repository  Test 4com.tqhit.battery.one.features.emoji.data.repository  any 4com.tqhit.battery.one.features.emoji.data.repository  assertEquals 4com.tqhit.battery.one.features.emoji.data.repository  assertFalse 4com.tqhit.battery.one.features.emoji.data.repository  
assertNotNull 4com.tqhit.battery.one.features.emoji.data.repository  
assertNull 4com.tqhit.battery.one.features.emoji.data.repository  
assertTrue 4com.tqhit.battery.one.features.emoji.data.repository  every 4com.tqhit.battery.one.features.emoji.data.repository  find 4com.tqhit.battery.one.features.emoji.data.repository  first 4com.tqhit.battery.one.features.emoji.data.repository  mockAssetManager 4com.tqhit.battery.one.features.emoji.data.repository  mockContext 4com.tqhit.battery.one.features.emoji.data.repository  mockRemoteConfigHelper 4com.tqhit.battery.one.features.emoji.data.repository  mockk 4com.tqhit.battery.one.features.emoji.data.repository  
repository 4com.tqhit.battery.one.features.emoji.data.repository  runTest 4com.tqhit.battery.one.features.emoji.data.repository  testStylesJson 4com.tqhit.battery.one.features.emoji.data.repository  toByteArray 4com.tqhit.battery.one.features.emoji.data.repository  
trimIndent 4com.tqhit.battery.one.features.emoji.data.repository  verify 4com.tqhit.battery.one.features.emoji.data.repository  batteryStylesFlow Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  
clearCache Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  getAllStyles Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  getCurrentStyles Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  
getFreeStyles Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  getPopularStyles Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  getPremiumStyles Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  getStyleById Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  getStylesByCategory Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  
hasCachedData Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  
isLoadingFlow Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  
refreshStyles Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  searchStyles Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  BatteryStyleCategory Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  BatteryStyleRepositoryImpl Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  ByteArrayInputStream Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  Gson Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  IOException Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  any Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  assertEquals Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  assertFalse Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  
assertNotNull Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  
assertNull Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  
assertTrue Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  every Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  find Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  first Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  gson Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  mockAssetManager Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  mockContext Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  mockRemoteConfigHelper Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  mockk Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  
repository Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  runTest Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  testStylesJson Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  toByteArray Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  
trimIndent Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  verify Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  Before 'com.tqhit.battery.one.features.emoji.di  Config 'com.tqhit.battery.one.features.emoji.di  EmojiBatteryDIModule 'com.tqhit.battery.one.features.emoji.di  EmojiBatteryDIModuleTest 'com.tqhit.battery.one.features.emoji.di  RobolectricTestRunner 'com.tqhit.battery.one.features.emoji.di  RunWith 'com.tqhit.battery.one.features.emoji.di  Test 'com.tqhit.battery.one.features.emoji.di  
assertNotNull 'com.tqhit.battery.one.features.emoji.di  
assertTrue 'com.tqhit.battery.one.features.emoji.di  contains 'com.tqhit.battery.one.features.emoji.di  dagger 'com.tqhit.battery.one.features.emoji.di  java 'com.tqhit.battery.one.features.emoji.di  EmojiBatteryDIModule @com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest  
assertNotNull @com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest  
assertTrue @com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest  contains @com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest  dagger @com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest  java @com.tqhit.battery.one.features.emoji.di.EmojiBatteryDIModuleTest  BatteryStyle 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleCategory 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleCategoryTest 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleConfig 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleConfigTest 1com.tqhit.battery.one.features.emoji.domain.model  BatteryStyleTest 1com.tqhit.battery.one.features.emoji.domain.model  Test 1com.tqhit.battery.one.features.emoji.domain.model  assertEquals 1com.tqhit.battery.one.features.emoji.domain.model  assertFalse 1com.tqhit.battery.one.features.emoji.domain.model  assertNotEquals 1com.tqhit.battery.one.features.emoji.domain.model  
assertNotNull 1com.tqhit.battery.one.features.emoji.domain.model  
assertNull 1com.tqhit.battery.one.features.emoji.domain.model  
assertTrue 1com.tqhit.battery.one.features.emoji.domain.model  contains 1com.tqhit.battery.one.features.emoji.domain.model  
createDefault 1com.tqhit.battery.one.features.emoji.domain.model  findByDisplayName 1com.tqhit.battery.one.features.emoji.domain.model  first 1com.tqhit.battery.one.features.emoji.domain.model  forEach 1com.tqhit.battery.one.features.emoji.domain.model  
fromString 1com.tqhit.battery.one.features.emoji.domain.model  getAllSorted 1com.tqhit.battery.one.features.emoji.domain.model  
getDefault 1com.tqhit.battery.one.features.emoji.domain.model  getMainFilterCategories 1com.tqhit.battery.one.features.emoji.domain.model  
isNotEmpty 1com.tqhit.battery.one.features.emoji.domain.model  listOf 1com.tqhit.battery.one.features.emoji.domain.model  map 1com.tqhit.battery.one.features.emoji.domain.model  toSet 1com.tqhit.battery.one.features.emoji.domain.model  until 1com.tqhit.battery.one.features.emoji.domain.model  	Companion >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  batteryImageUrl >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  category >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  
createDefault >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  
defaultConfig >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  
emojiImageUrl >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  getPreviewId >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  id >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  	isPopular >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  	isPremium >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  isValid >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  
matchesSearch >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  name >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  timestampEpochMillis >com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle  
createDefault Hcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyle.Companion  ANIMAL Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  	CHARACTER Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  CUTE Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  	Companion Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  FOOD Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  GAMING Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  HEART Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  HOT Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  MINIMAL Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  NATURE Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  SEASONAL Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  displayName Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  emoji Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  findByDisplayName Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  
fromString Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  getAllSorted Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  
getDefault Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  getDisplayText Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  getMainFilterCategories Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  
isFeatured Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  	sortOrder Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  values Fcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory  findByDisplayName Pcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion  
fromString Pcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion  getAllSorted Pcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion  
getDefault Pcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion  getMainFilterCategories Pcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory.Companion  BatteryStyleCategory Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  assertEquals Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  assertFalse Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  
assertNotNull Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  
assertNull Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  
assertTrue Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  contains Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  findByDisplayName Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  first Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  forEach Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  
fromString Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  getAllSorted Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  
getDefault Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  getMainFilterCategories Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  
isNotEmpty Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  listOf Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  map Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  toSet Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  until Jcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategoryTest  	Companion Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  
createDefault Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  emojiSizeScale Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  isValid Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  percentageColor Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  percentageFontSizeDp Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  	showEmoji Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  showPercentage Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  	validated Dcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig  
createDefault Ncom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfig.Companion  BatteryStyleConfig Hcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest  assertEquals Hcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest  assertFalse Hcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest  
assertTrue Hcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest  
createDefault Hcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleConfigTest  BatteryStyle Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  BatteryStyleCategory Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  assertEquals Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  assertFalse Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  assertNotEquals Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  
assertNotNull Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  
assertTrue Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  contains Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  
createDefault Bcom.tqhit.battery.one.features.emoji.domain.model.BatteryStyleTest  BatteryStyleRepository 6com.tqhit.battery.one.features.emoji.domain.repository  batteryStylesFlow Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  getAllStyles Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  getCurrentStyles Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  
getFreeStyles Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  getPopularStyles Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  getPremiumStyles Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  getStyleById Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  getStylesByCategory Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  
hasCachedData Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  
isLoadingFlow Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  
refreshStyles Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  searchStyles Mcom.tqhit.battery.one.features.emoji.domain.repository.BatteryStyleRepository  BatteryStyle 4com.tqhit.battery.one.features.emoji.domain.use_case  BatteryStyleCategory 4com.tqhit.battery.one.features.emoji.domain.use_case  BatteryStyleRepository 4com.tqhit.battery.one.features.emoji.domain.use_case  Before 4com.tqhit.battery.one.features.emoji.domain.use_case  GetBatteryStylesUseCase 4com.tqhit.battery.one.features.emoji.domain.use_case  GetBatteryStylesUseCaseTest 4com.tqhit.battery.one.features.emoji.domain.use_case  MutableStateFlow 4com.tqhit.battery.one.features.emoji.domain.use_case  RuntimeException 4com.tqhit.battery.one.features.emoji.domain.use_case  Test 4com.tqhit.battery.one.features.emoji.domain.use_case  assertEquals 4com.tqhit.battery.one.features.emoji.domain.use_case  assertFalse 4com.tqhit.battery.one.features.emoji.domain.use_case  
assertNull 4com.tqhit.battery.one.features.emoji.domain.use_case  
assertTrue 4com.tqhit.battery.one.features.emoji.domain.use_case  coEvery 4com.tqhit.battery.one.features.emoji.domain.use_case  coVerify 4com.tqhit.battery.one.features.emoji.domain.use_case  contains 4com.tqhit.battery.one.features.emoji.domain.use_case  every 4com.tqhit.battery.one.features.emoji.domain.use_case  filter 4com.tqhit.battery.one.features.emoji.domain.use_case  first 4com.tqhit.battery.one.features.emoji.domain.use_case  listOf 4com.tqhit.battery.one.features.emoji.domain.use_case  mockRepository 4com.tqhit.battery.one.features.emoji.domain.use_case  mockk 4com.tqhit.battery.one.features.emoji.domain.use_case  runTest 4com.tqhit.battery.one.features.emoji.domain.use_case  
testStyles 4com.tqhit.battery.one.features.emoji.domain.use_case  useCase 4com.tqhit.battery.one.features.emoji.domain.use_case  getAllStyles Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  getAllStylesFlow Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  getCurrentStyles Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  
getFreeStyles Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  getLoadingStateFlow Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  getPopularStyles Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  getPremiumStyles Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  getStyleById Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  getStylesByCategory Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  
hasCachedData Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  
refreshStyles Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  searchStyles Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  BatteryStyle Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  BatteryStyleCategory Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  GetBatteryStylesUseCase Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  MutableStateFlow Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  RuntimeException Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  assertEquals Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  assertFalse Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  
assertNull Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  
assertTrue Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  coEvery Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  coVerify Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  contains Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  every Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  filter Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  first Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  listOf Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  mockRepository Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  mockk Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  runTest Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  
testStyles Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  useCase Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  After 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryEvent 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryState 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryViewModel 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryGalleryViewModelTest 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryStyle 9com.tqhit.battery.one.features.emoji.presentation.gallery  BatteryStyleCategory 9com.tqhit.battery.one.features.emoji.presentation.gallery  Before 9com.tqhit.battery.one.features.emoji.presentation.gallery  CoreBatteryStatsProvider 9com.tqhit.battery.one.features.emoji.presentation.gallery  CoreBatteryStatus 9com.tqhit.battery.one.features.emoji.presentation.gallery  Dispatchers 9com.tqhit.battery.one.features.emoji.presentation.gallery  ExperimentalCoroutinesApi 9com.tqhit.battery.one.features.emoji.presentation.gallery  GetBatteryStylesUseCase 9com.tqhit.battery.one.features.emoji.presentation.gallery  InstantTaskExecutorRule 9com.tqhit.battery.one.features.emoji.presentation.gallery  MutableStateFlow 9com.tqhit.battery.one.features.emoji.presentation.gallery  OptIn 9com.tqhit.battery.one.features.emoji.presentation.gallery  Rule 9com.tqhit.battery.one.features.emoji.presentation.gallery  RuntimeException 9com.tqhit.battery.one.features.emoji.presentation.gallery  StandardTestDispatcher 9com.tqhit.battery.one.features.emoji.presentation.gallery  Test 9com.tqhit.battery.one.features.emoji.presentation.gallery  all 9com.tqhit.battery.one.features.emoji.presentation.gallery  assertEquals 9com.tqhit.battery.one.features.emoji.presentation.gallery  assertFalse 9com.tqhit.battery.one.features.emoji.presentation.gallery  
assertNotNull 9com.tqhit.battery.one.features.emoji.presentation.gallery  
assertNull 9com.tqhit.battery.one.features.emoji.presentation.gallery  
assertTrue 9com.tqhit.battery.one.features.emoji.presentation.gallery  coEvery 9com.tqhit.battery.one.features.emoji.presentation.gallery  contains 9com.tqhit.battery.one.features.emoji.presentation.gallery  
createDefault 9com.tqhit.battery.one.features.emoji.presentation.gallery  every 9com.tqhit.battery.one.features.emoji.presentation.gallery  first 9com.tqhit.battery.one.features.emoji.presentation.gallery  
isNotEmpty 9com.tqhit.battery.one.features.emoji.presentation.gallery  listOf 9com.tqhit.battery.one.features.emoji.presentation.gallery  mockCoreBatteryStatsProvider 9com.tqhit.battery.one.features.emoji.presentation.gallery  mockGetBatteryStylesUseCase 9com.tqhit.battery.one.features.emoji.presentation.gallery  mockk 9com.tqhit.battery.one.features.emoji.presentation.gallery  	resetMain 9com.tqhit.battery.one.features.emoji.presentation.gallery  runTest 9com.tqhit.battery.one.features.emoji.presentation.gallery  setMain 9com.tqhit.battery.one.features.emoji.presentation.gallery  
testStyles 9com.tqhit.battery.one.features.emoji.presentation.gallery  	viewModel 9com.tqhit.battery.one.features.emoji.presentation.gallery  ClearAllFilters Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  	Companion Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  DismissError Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  FilterByCategory Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  LoadInitialData Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  RefreshData Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  SearchStyles Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  SelectStyle Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  ToggleShowOnlyFree Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  ToggleShowOnlyPopular Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  ToggleShowOnlyPremium Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryEvent  	allStyles Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  displayedStyles Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  errorMessage Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  	isLoading Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  isRefreshing Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  lastRefreshTimestamp Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  searchQuery Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  selectedCategory Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  selectedStyleId Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  showOnlyFree Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  showOnlyPopular Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  showOnlyPremium Mcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryState  handleEvent Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  uiState Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  BatteryGalleryEvent Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  BatteryGalleryViewModel Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  BatteryStyle Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  BatteryStyleCategory Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  CoreBatteryStatus Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  Dispatchers Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  InstantTaskExecutorRule Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  MutableStateFlow Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  RuntimeException Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  StandardTestDispatcher Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  advanceUntilIdle Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  all Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  assertEquals Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  assertFalse Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  
assertNotNull Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  
assertNull Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  
assertTrue Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  coEvery Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  contains Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  
createDefault Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  every Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  first Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  
isNotEmpty Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  listOf Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  mockCoreBatteryStatsProvider Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  mockGetBatteryStylesUseCase Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  mockk Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  	resetMain Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  runTest Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  setMain Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  testDispatcher Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  
testStyles Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  	viewModel Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  ApplicationProvider Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  BatteryStyle Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  BatteryStyleAdapter Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  BatteryStyleAdapterTest Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  BatteryStyleCategory Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  Before Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  Config Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  Context Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  RecyclerView Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  RobolectricTestRunner Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  RunWith Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  String Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  Test Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  Unit Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  View Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  assertEquals Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  
assertTrue Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  contains Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  	emptyList Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  find Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  listOf Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  mockOnPremiumUnlockClick Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  mockOnStyleClick Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  mockOnStyleLongClick Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  mockk Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  verify Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  BatteryStyleViewHolder Ucom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter  getItem Ucom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter  	itemCount Ucom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter  onBindViewHolder Ucom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter  onCreateViewHolder Ucom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter  
submitList Ucom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter  binding lcom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter.BatteryStyleViewHolder  ApplicationProvider Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  BatteryStyle Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  BatteryStyleAdapter Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  BatteryStyleCategory Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  RecyclerView Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  View Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  adapter Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  assertEquals Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  
assertTrue Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  contains Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  context Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  	emptyList Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  find Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  listOf Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  mockOnImageLoadError Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  mockOnPremiumUnlockClick Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  mockOnStyleClick Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  mockOnStyleLongClick Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  mockk Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  
testStyles Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  verify Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  AnimationGridFragment )com.tqhit.battery.one.features.navigation  DischargeFragment )com.tqhit.battery.one.features.navigation  HealthFragment )com.tqhit.battery.one.features.navigation  NavigationState )com.tqhit.battery.one.features.navigation  NavigationStateChange )com.tqhit.battery.one.features.navigation  NavigationStateChangeTest )com.tqhit.battery.one.features.navigation  NavigationStateTest )com.tqhit.battery.one.features.navigation  R )com.tqhit.battery.one.features.navigation  SettingsFragment )com.tqhit.battery.one.features.navigation  StateChangeReason )com.tqhit.battery.one.features.navigation  StateChangeReasonTest )com.tqhit.battery.one.features.navigation  StatsChargeFragment )com.tqhit.battery.one.features.navigation  Test )com.tqhit.battery.one.features.navigation  assertEquals )com.tqhit.battery.one.features.navigation  assertFalse )com.tqhit.battery.one.features.navigation  
assertNull )com.tqhit.battery.one.features.navigation  
assertTrue )com.tqhit.battery.one.features.navigation  createChargingState )com.tqhit.battery.one.features.navigation  createDefaultState )com.tqhit.battery.one.features.navigation  createDischargingState )com.tqhit.battery.one.features.navigation  	emptyList )com.tqhit.battery.one.features.navigation  forEach )com.tqhit.battery.one.features.navigation  java )com.tqhit.battery.one.features.navigation  listOf )com.tqhit.battery.one.features.navigation  toList )com.tqhit.battery.one.features.navigation  ALL_MENU_ITEMS 9com.tqhit.battery.one.features.navigation.NavigationState  ALWAYS_VISIBLE_ITEMS 9com.tqhit.battery.one.features.navigation.NavigationState  	Companion 9com.tqhit.battery.one.features.navigation.NavigationState  activeFragmentId 9com.tqhit.battery.one.features.navigation.NavigationState  copy 9com.tqhit.battery.one.features.navigation.NavigationState  createChargingState 9com.tqhit.battery.one.features.navigation.NavigationState  createDefaultState 9com.tqhit.battery.one.features.navigation.NavigationState  createDischargingState 9com.tqhit.battery.one.features.navigation.NavigationState  createFragment 9com.tqhit.battery.one.features.navigation.NavigationState  getFragmentClass 9com.tqhit.battery.one.features.navigation.NavigationState  
isCharging 9com.tqhit.battery.one.features.navigation.NavigationState  isChargingState 9com.tqhit.battery.one.features.navigation.NavigationState  isDischargingState 9com.tqhit.battery.one.features.navigation.NavigationState  isMenuItemVisible 9com.tqhit.battery.one.features.navigation.NavigationState  shouldShowTransition 9com.tqhit.battery.one.features.navigation.NavigationState  ALL_MENU_ITEMS Ccom.tqhit.battery.one.features.navigation.NavigationState.Companion  ALWAYS_VISIBLE_ITEMS Ccom.tqhit.battery.one.features.navigation.NavigationState.Companion  createChargingState Ccom.tqhit.battery.one.features.navigation.NavigationState.Companion  createDefaultState Ccom.tqhit.battery.one.features.navigation.NavigationState.Companion  createDischargingState Ccom.tqhit.battery.one.features.navigation.NavigationState.Companion  newState ?com.tqhit.battery.one.features.navigation.NavigationStateChange  
previousState ?com.tqhit.battery.one.features.navigation.NavigationStateChange  reason ?com.tqhit.battery.one.features.navigation.NavigationStateChange  NavigationState Ccom.tqhit.battery.one.features.navigation.NavigationStateChangeTest  NavigationStateChange Ccom.tqhit.battery.one.features.navigation.NavigationStateChangeTest  StateChangeReason Ccom.tqhit.battery.one.features.navigation.NavigationStateChangeTest  assertEquals Ccom.tqhit.battery.one.features.navigation.NavigationStateChangeTest  
assertNull Ccom.tqhit.battery.one.features.navigation.NavigationStateChangeTest  createChargingState Ccom.tqhit.battery.one.features.navigation.NavigationStateChangeTest  createDischargingState Ccom.tqhit.battery.one.features.navigation.NavigationStateChangeTest  AnimationGridFragment =com.tqhit.battery.one.features.navigation.NavigationStateTest  DischargeFragment =com.tqhit.battery.one.features.navigation.NavigationStateTest  HealthFragment =com.tqhit.battery.one.features.navigation.NavigationStateTest  NavigationState =com.tqhit.battery.one.features.navigation.NavigationStateTest  R =com.tqhit.battery.one.features.navigation.NavigationStateTest  SettingsFragment =com.tqhit.battery.one.features.navigation.NavigationStateTest  StatsChargeFragment =com.tqhit.battery.one.features.navigation.NavigationStateTest  assertEquals =com.tqhit.battery.one.features.navigation.NavigationStateTest  assertFalse =com.tqhit.battery.one.features.navigation.NavigationStateTest  
assertTrue =com.tqhit.battery.one.features.navigation.NavigationStateTest  createChargingState =com.tqhit.battery.one.features.navigation.NavigationStateTest  createDefaultState =com.tqhit.battery.one.features.navigation.NavigationStateTest  createDischargingState =com.tqhit.battery.one.features.navigation.NavigationStateTest  	emptyList =com.tqhit.battery.one.features.navigation.NavigationStateTest  java =com.tqhit.battery.one.features.navigation.NavigationStateTest  listOf =com.tqhit.battery.one.features.navigation.NavigationStateTest  
APP_RESUME ;com.tqhit.battery.one.features.navigation.StateChangeReason  CHARGING_STARTED ;com.tqhit.battery.one.features.navigation.StateChangeReason  CHARGING_STOPPED ;com.tqhit.battery.one.features.navigation.StateChangeReason  FRAGMENT_RESTORE ;com.tqhit.battery.one.features.navigation.StateChangeReason  
INITIAL_SETUP ;com.tqhit.battery.one.features.navigation.StateChangeReason  USER_NAVIGATION ;com.tqhit.battery.one.features.navigation.StateChangeReason  values ;com.tqhit.battery.one.features.navigation.StateChangeReason  StateChangeReason ?com.tqhit.battery.one.features.navigation.StateChangeReasonTest  assertEquals ?com.tqhit.battery.one.features.navigation.StateChangeReasonTest  
assertTrue ?com.tqhit.battery.one.features.navigation.StateChangeReasonTest  listOf ?com.tqhit.battery.one.features.navigation.StateChangeReasonTest  toList ?com.tqhit.battery.one.features.navigation.StateChangeReasonTest  Before $com.tqhit.battery.one.features.stats  Context $com.tqhit.battery.one.features.stats  CoreBatteryStatsProvider $com.tqhit.battery.one.features.stats  CoreBatteryStatus $com.tqhit.battery.one.features.stats  DefaultCoreBatteryStatsProvider $com.tqhit.battery.one.features.stats  	Exception $com.tqhit.battery.one.features.stats  ExperimentalCoroutinesApi $com.tqhit.battery.one.features.stats  OptIn $com.tqhit.battery.one.features.stats  System $com.tqhit.battery.one.features.stats  Test $com.tqhit.battery.one.features.stats  'UnifiedBatteryNotificationServiceHelper $com.tqhit.battery.one.features.stats  UnifiedBatteryServiceTest $com.tqhit.battery.one.features.stats  assertEquals $com.tqhit.battery.one.features.stats  assertFalse $com.tqhit.battery.one.features.stats  
assertNotNull $com.tqhit.battery.one.features.stats  
assertNull $com.tqhit.battery.one.features.stats  
assertTrue $com.tqhit.battery.one.features.stats  coreBatteryStatsProvider $com.tqhit.battery.one.features.stats  fail $com.tqhit.battery.one.features.stats  first $com.tqhit.battery.one.features.stats  mockk $com.tqhit.battery.one.features.stats  runTest $com.tqhit.battery.one.features.stats  CoreBatteryStatus >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  DefaultCoreBatteryStatsProvider >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  System >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  'UnifiedBatteryNotificationServiceHelper >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  assertEquals >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  assertFalse >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  
assertNotNull >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  
assertNull >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  
assertTrue >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  coreBatteryStatsProvider >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  fail >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  first >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  mockContext >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  mockk >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  runTest >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  
serviceHelper >com.tqhit.battery.one.features.stats.UnifiedBatteryServiceTest  
AppRepository +com.tqhit.battery.one.features.stats.charge  Before +com.tqhit.battery.one.features.stats.charge  $CalculateSimpleChargeEstimateUseCase +com.tqhit.battery.one.features.stats.charge  (CalculateSimpleChargeEstimateUseCaseTest +com.tqhit.battery.one.features.stats.charge  PowerCalculationManualTest +com.tqhit.battery.one.features.stats.charge  StatsChargeRepository +com.tqhit.battery.one.features.stats.charge  StatsChargeSession +com.tqhit.battery.one.features.stats.charge   StatsChargeSessionStatisticsTest +com.tqhit.battery.one.features.stats.charge  StatsChargeSessionTest +com.tqhit.battery.one.features.stats.charge  StatsChargeStatus +com.tqhit.battery.one.features.stats.charge  StatsChargeViewModel +com.tqhit.battery.one.features.stats.charge  (StatsChargeViewModelPowerCalculationTest +com.tqhit.battery.one.features.stats.charge  System +com.tqhit.battery.one.features.stats.charge  Test +com.tqhit.battery.one.features.stats.charge  abs +com.tqhit.battery.one.features.stats.charge  assert +com.tqhit.battery.one.features.stats.charge  assertEquals +com.tqhit.battery.one.features.stats.charge  assertFalse +com.tqhit.battery.one.features.stats.charge  assertNotEquals +com.tqhit.battery.one.features.stats.charge  
assertNull +com.tqhit.battery.one.features.stats.charge  
assertTrue +com.tqhit.battery.one.features.stats.charge  coEvery +com.tqhit.battery.one.features.stats.charge  	createNew +com.tqhit.battery.one.features.stats.charge  
endSession +com.tqhit.battery.one.features.stats.charge  every +com.tqhit.battery.one.features.stats.charge  flowOf +com.tqhit.battery.one.features.stats.charge  mockAppRepository +com.tqhit.battery.one.features.stats.charge  mockStatsChargeRepository +com.tqhit.battery.one.features.stats.charge  mockk +com.tqhit.battery.one.features.stats.charge  println +com.tqhit.battery.one.features.stats.charge  $CalculateSimpleChargeEstimateUseCase Tcom.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest  StatsChargeSession Tcom.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest  StatsChargeStatus Tcom.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest  System Tcom.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest  assertEquals Tcom.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest  useCase Tcom.tqhit.battery.one.features.stats.charge.CalculateSimpleChargeEstimateUseCaseTest  abs Fcom.tqhit.battery.one.features.stats.charge.PowerCalculationManualTest  assert Fcom.tqhit.battery.one.features.stats.charge.PowerCalculationManualTest  println Fcom.tqhit.battery.one.features.stats.charge.PowerCalculationManualTest  StatsChargeSession Lcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest  System Lcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest  assert Lcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest  assertEquals Lcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest  	createNew Lcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest  
endSession Lcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionStatisticsTest  StatsChargeSession Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  System Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  assertEquals Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  assertFalse Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  assertNotEquals Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  
assertNull Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  
assertTrue Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  	createNew Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  
endSession Bcom.tqhit.battery.one.features.stats.charge.StatsChargeSessionTest  StatsChargeStatus Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  StatsChargeViewModel Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  assertEquals Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  coEvery Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  createTestStatus Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  every Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  flowOf Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  mockAppRepository Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  (mockCalculateSimpleChargeEstimateUseCase Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  mockStatsChargeRepository Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  mockk Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  	viewModel Tcom.tqhit.battery.one.features.stats.charge.StatsChargeViewModelPowerCalculationTest  StatsChargeSession 0com.tqhit.battery.one.features.stats.charge.data  StatsChargeStatus 0com.tqhit.battery.one.features.stats.charge.data  	Companion Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  copy Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  	createNew Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  durationMillis Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  
endPercentage Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  
endSession Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  endTimeEpochMillis Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  getPercentageCharged Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  hashCode Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  isActive Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  percentageCharged Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  startPercentage Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  startTimeEpochMillis Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  totalChargeMah Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  totalChargePercentage Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  	createNew Mcom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion  
endSession Mcom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession.Companion  $CalculateSimpleChargeEstimateUseCase 2com.tqhit.battery.one.features.stats.charge.domain  calculateTimeToFull Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  calculateTimeToTarget Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  execute Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  StatsChargeFragment 8com.tqhit.battery.one.features.stats.charge.presentation  StatsChargeViewModel 8com.tqhit.battery.one.features.stats.charge.presentation  	Companion Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  calculatePower Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  formatPower Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  StatsChargeRepository 6com.tqhit.battery.one.features.stats.charge.repository  activeChargeSessionFlow Lcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository  statsChargeStatusFlow Lcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepository  CoreBatteryStatus 0com.tqhit.battery.one.features.stats.corebattery  CoreBatteryStatusTest 0com.tqhit.battery.one.features.stats.corebattery  System 0com.tqhit.battery.one.features.stats.corebattery  Test 0com.tqhit.battery.one.features.stats.corebattery  assertEquals 0com.tqhit.battery.one.features.stats.corebattery  assertFalse 0com.tqhit.battery.one.features.stats.corebattery  assertNotEquals 0com.tqhit.battery.one.features.stats.corebattery  
assertTrue 0com.tqhit.battery.one.features.stats.corebattery  contains 0com.tqhit.battery.one.features.stats.corebattery  
createDefault 0com.tqhit.battery.one.features.stats.corebattery  CoreBatteryStatus Fcom.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest  System Fcom.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest  assertEquals Fcom.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest  assertFalse Fcom.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest  assertNotEquals Fcom.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest  
assertTrue Fcom.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest  contains Fcom.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest  
createDefault Fcom.tqhit.battery.one.features.stats.corebattery.CoreBatteryStatusTest  CoreBatteryStatus 5com.tqhit.battery.one.features.stats.corebattery.data  	Companion Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  copy Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  
createDefault Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  currentMicroAmperes Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  hashCode Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  
isCharging Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  
percentage Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  
pluggedSource Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  temperatureCelsius Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  timestampEpochMillis Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  toString Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  voltageMillivolts Gcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus  
createDefault Qcom.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus.Companion  CoreBatteryStatsProvider 7com.tqhit.battery.one.features.stats.corebattery.domain  DefaultCoreBatteryStatsProvider 7com.tqhit.battery.one.features.stats.corebattery.domain  coreBatteryStatusFlow Pcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider  getCurrentStatus Pcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider  updateStatus Pcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider  getCurrentStatus Wcom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider  Before .com.tqhit.battery.one.features.stats.discharge  ScreenStateTimeTracker .com.tqhit.battery.one.features.stats.discharge  ScreenTimeGapValidationTest .com.tqhit.battery.one.features.stats.discharge  System .com.tqhit.battery.one.features.stats.discharge  Test .com.tqhit.battery.one.features.stats.discharge  Thread .com.tqhit.battery.one.features.stats.discharge  assertEquals .com.tqhit.battery.one.features.stats.discharge  
assertTrue .com.tqhit.battery.one.features.stats.discharge  kotlin .com.tqhit.battery.one.features.stats.discharge  println .com.tqhit.battery.one.features.stats.discharge  repeat .com.tqhit.battery.one.features.stats.discharge  ScreenStateTimeTracker Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  System Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  Thread Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  assertEquals Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  
assertTrue Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  kotlin Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  println Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  repeat Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  screenStateTimeTracker Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  sessionStartTime Jcom.tqhit.battery.one.features.stats.discharge.ScreenTimeGapValidationTest  CurrentSessionCache 4com.tqhit.battery.one.features.stats.discharge.cache  DischargeRatesCache 4com.tqhit.battery.one.features.stats.discharge.cache  clearCurrentSession Hcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache  getCurrentSession Hcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache  saveCurrentSession Hcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCache  getAverageScreenOffRateMah Hcom.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache  getAverageScreenOnRateMah Hcom.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache  DischargeSessionData 3com.tqhit.battery.one.features.stats.discharge.data  ScreenStateChangeEvent 3com.tqhit.battery.one.features.stats.discharge.data  apply Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  copy Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  currentPercentage Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  durationMillis Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  every Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  isActive Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  screenOffTimeMillis Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  screenOnTimeMillis Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  totalMahConsumed Hcom.tqhit.battery.one.features.stats.discharge.data.DischargeSessionData  ScreenStateReceiver 9com.tqhit.battery.one.features.stats.discharge.datasource  forceCheckScreenState Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  register Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  screenStateFlow Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  
unregister Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  AppLifecycleManager 5com.tqhit.battery.one.features.stats.discharge.domain  AppLifecycleManagerTest 5com.tqhit.battery.one.features.stats.discharge.domain  AppState 5com.tqhit.battery.one.features.stats.discharge.domain  Before 5com.tqhit.battery.one.features.stats.discharge.domain  Boolean 5com.tqhit.battery.one.features.stats.discharge.domain  Context 5com.tqhit.battery.one.features.stats.discharge.domain  CoreBatteryStatus 5com.tqhit.battery.one.features.stats.discharge.domain  CorrectionType 5com.tqhit.battery.one.features.stats.discharge.domain  CurrentSessionCache 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeCalculatorTest 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeRateCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeRatesCache 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeSessionData 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeSessionRepository 5com.tqhit.battery.one.features.stats.discharge.domain  EnhancedScreenTimeTrackerTest 5com.tqhit.battery.one.features.stats.discharge.domain  ExperimentalCoroutinesApi 5com.tqhit.battery.one.features.stats.discharge.domain  FullSessionReEstimator 5com.tqhit.battery.one.features.stats.discharge.domain  FullSessionReEstimatorTest 5com.tqhit.battery.one.features.stats.discharge.domain  GapEstimationCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  Int 5com.tqhit.battery.one.features.stats.discharge.domain  LifecycleOwner 5com.tqhit.battery.one.features.stats.discharge.domain  Long 5com.tqhit.battery.one.features.stats.discharge.domain  MutableStateFlow 5com.tqhit.battery.one.features.stats.discharge.domain  OptIn 5com.tqhit.battery.one.features.stats.discharge.domain  Pair 5com.tqhit.battery.one.features.stats.discharge.domain  PowerManager 5com.tqhit.battery.one.features.stats.discharge.domain  ProcessLifecycleOwner 5com.tqhit.battery.one.features.stats.discharge.domain  Runs 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenStateReceiver 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenStateTimeTracker 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeValidationService 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeValidationServiceTest 5com.tqhit.battery.one.features.stats.discharge.domain  SessionManager 5com.tqhit.battery.one.features.stats.discharge.domain  SessionMetricsCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  System 5com.tqhit.battery.one.features.stats.discharge.domain  Test 5com.tqhit.battery.one.features.stats.discharge.domain  Thread 5com.tqhit.battery.one.features.stats.discharge.domain  
TimeConverter 5com.tqhit.battery.one.features.stats.discharge.domain  TimeConverterTest 5com.tqhit.battery.one.features.stats.discharge.domain  ValidationResult 5com.tqhit.battery.one.features.stats.discharge.domain  appLifecycleManager 5com.tqhit.battery.one.features.stats.discharge.domain  apply 5com.tqhit.battery.one.features.stats.discharge.domain  assertEquals 5com.tqhit.battery.one.features.stats.discharge.domain  assertFalse 5com.tqhit.battery.one.features.stats.discharge.domain  
assertNotNull 5com.tqhit.battery.one.features.stats.discharge.domain  
assertNull 5com.tqhit.battery.one.features.stats.discharge.domain  
assertTrue 5com.tqhit.battery.one.features.stats.discharge.domain  coEvery 5com.tqhit.battery.one.features.stats.discharge.domain  coVerify 5com.tqhit.battery.one.features.stats.discharge.domain  com 5com.tqhit.battery.one.features.stats.discharge.domain  createRepository 5com.tqhit.battery.one.features.stats.discharge.domain  createTestSession 5com.tqhit.battery.one.features.stats.discharge.domain  every 5com.tqhit.battery.one.features.stats.discharge.domain  get 5com.tqhit.battery.one.features.stats.discharge.domain  just 5com.tqhit.battery.one.features.stats.discharge.domain  kotlin 5com.tqhit.battery.one.features.stats.discharge.domain  mockContext 5com.tqhit.battery.one.features.stats.discharge.domain  mockCurrentSessionCache 5com.tqhit.battery.one.features.stats.discharge.domain  mockDischargeRateCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  mockDischargeRatesCache 5com.tqhit.battery.one.features.stats.discharge.domain  mockFullSessionReEstimator 5com.tqhit.battery.one.features.stats.discharge.domain  	mockOwner 5com.tqhit.battery.one.features.stats.discharge.domain  mockPowerManager 5com.tqhit.battery.one.features.stats.discharge.domain  mockScreenStateReceiver 5com.tqhit.battery.one.features.stats.discharge.domain  mockScreenTimeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  mockSessionManager 5com.tqhit.battery.one.features.stats.discharge.domain  mockTimeConverter 5com.tqhit.battery.one.features.stats.discharge.domain  mockk 5com.tqhit.battery.one.features.stats.discharge.domain  mockkStatic 5com.tqhit.battery.one.features.stats.discharge.domain  
mutableListOf 5com.tqhit.battery.one.features.stats.discharge.domain  reEstimator 5com.tqhit.battery.one.features.stats.discharge.domain  repeat 5com.tqhit.battery.one.features.stats.discharge.domain  
repository 5com.tqhit.battery.one.features.stats.discharge.domain  runTest 5com.tqhit.battery.one.features.stats.discharge.domain  tracker 5com.tqhit.battery.one.features.stats.discharge.domain  until 5com.tqhit.battery.one.features.stats.discharge.domain  verify 5com.tqhit.battery.one.features.stats.discharge.domain  appState Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  getCurrentStateInfo Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  isDischargeFragmentActive Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  onStart Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  onStop Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  setDischargeFragmentActive Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  shouldTriggerUiUpdate Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  AppLifecycleManager Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  AppState Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  ProcessLifecycleOwner Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  Thread Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  appLifecycleManager Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  assertEquals Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  assertFalse Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  
assertTrue Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  every Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  get Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  	mockOwner Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  mockk Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  mockkStatic Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  runTest Mcom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManagerTest  
BACKGROUND >com.tqhit.battery.one.features.stats.discharge.domain.AppState  
FOREGROUND >com.tqhit.battery.one.features.stats.discharge.domain.AppState  OFF_TIME_ADJUSTED Dcom.tqhit.battery.one.features.stats.discharge.domain.CorrectionType  PROPORTIONAL_SCALED Dcom.tqhit.battery.one.features.stats.discharge.domain.CorrectionType  	Companion Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  MIXED_USAGE_SCREEN_OFF_WEIGHT Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  MIXED_USAGE_SCREEN_ON_WEIGHT Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  calculateCurrentCapacityMah Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  calculateMixedDischargeRate Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  estimateTimeRemainingMillis Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  shouldSkipUpdate Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  MIXED_USAGE_SCREEN_OFF_WEIGHT Scom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion  MIXED_USAGE_SCREEN_ON_WEIGHT Scom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator.Companion  DischargeCalculator Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest  assertEquals Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest  assertFalse Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest  
assertTrue Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest  
calculator Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculatorTest  calculateMahConsumed Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  calculateMixedRate Mcom.tqhit.battery.one.features.stats.discharge.domain.DischargeRateCalculator  ScreenStateTimeTracker Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  System Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  Thread Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  assertEquals Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  
assertTrue Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  
mutableListOf Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  repeat Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  runTest Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  tracker Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest  until Scom.tqhit.battery.one.features.stats.discharge.domain.EnhancedScreenTimeTrackerTest   reEstimateFullSessionScreenTimes Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  CoreBatteryStatus Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  DischargeSessionData Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  FullSessionReEstimator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  ScreenTimeCalculator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  System Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  assertEquals Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  
assertNotNull Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  coEvery Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  createTestSession Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  every Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  mockDischargeRateCalculator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  mockDischargeRatesCache Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  mockScreenTimeCalculator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  mockTimeConverter Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  mockk Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  reEstimator Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  runTest Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  verify Pcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimatorTest  applyGapEstimationResults Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  forceSetScreenOffTime Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  forceSetScreenState Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  getCurrentTimes Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  handleScreenStateChange Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  incrementCurrentState Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  
initialize Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  reset Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  BatteryRates Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  ScreenTimes Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  calculateScreenTimes Jcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeCalculator  getAppStateInfo Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  shouldTriggerUiUpdate Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  validateScreenTimes Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  CorrectionType Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  ProcessLifecycleOwner Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  ScreenTimeValidationService Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  ValidationResult Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  appLifecycleManager Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  apply Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  assertEquals Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  
assertTrue Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  createMockSession Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  every Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  get Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  mockk Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  mockkStatic Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  validationService Ucom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationServiceTest  createNewSession Dcom.tqhit.battery.one.features.stats.discharge.domain.SessionManager  formatMillisToHoursMinutes Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  !formatMillisToHoursMinutesSeconds Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  formatMillisToMinutes Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  
hoursToMillis Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  
millisToHours Ccom.tqhit.battery.one.features.stats.discharge.domain.TimeConverter  
TimeConverter Gcom.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest  assertEquals Gcom.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest  
timeConverter Gcom.tqhit.battery.one.features.stats.discharge.domain.TimeConverterTest  	Corrected Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult  NoValidationNeeded Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult  Valid Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult  correctedScreenOffTime Pcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Corrected  correctedScreenOnTime Pcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Corrected  correctionType Pcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Corrected  
screenOffTime Lcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Valid  screenOnTime Lcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult.Valid  AppLifecycleManager :com.tqhit.battery.one.features.stats.discharge.integration  AppState :com.tqhit.battery.one.features.stats.discharge.integration  Before :com.tqhit.battery.one.features.stats.discharge.integration  Boolean :com.tqhit.battery.one.features.stats.discharge.integration  Context :com.tqhit.battery.one.features.stats.discharge.integration  CoreBatteryStatus :com.tqhit.battery.one.features.stats.discharge.integration  CorrectionType :com.tqhit.battery.one.features.stats.discharge.integration  CurrentSessionCache :com.tqhit.battery.one.features.stats.discharge.integration  DischargeSessionData :com.tqhit.battery.one.features.stats.discharge.integration  DischargeSessionRepository :com.tqhit.battery.one.features.stats.discharge.integration  ExperimentalCoroutinesApi :com.tqhit.battery.one.features.stats.discharge.integration  FullSessionReEstimator :com.tqhit.battery.one.features.stats.discharge.integration  GapEstimationCalculator :com.tqhit.battery.one.features.stats.discharge.integration  Int :com.tqhit.battery.one.features.stats.discharge.integration  Long :com.tqhit.battery.one.features.stats.discharge.integration  MutableStateFlow :com.tqhit.battery.one.features.stats.discharge.integration  OptIn :com.tqhit.battery.one.features.stats.discharge.integration  PowerManager :com.tqhit.battery.one.features.stats.discharge.integration  ProcessLifecycleOwner :com.tqhit.battery.one.features.stats.discharge.integration  Runs :com.tqhit.battery.one.features.stats.discharge.integration  ScreenStateReceiver :com.tqhit.battery.one.features.stats.discharge.integration  ScreenTimeConstraintTest :com.tqhit.battery.one.features.stats.discharge.integration  #ScreenTimeEstimationIntegrationTest :com.tqhit.battery.one.features.stats.discharge.integration  ScreenTimeOscillationTest :com.tqhit.battery.one.features.stats.discharge.integration  !ScreenTimeUiUpdateIntegrationTest :com.tqhit.battery.one.features.stats.discharge.integration  ScreenTimeValidationService :com.tqhit.battery.one.features.stats.discharge.integration  SessionManager :com.tqhit.battery.one.features.stats.discharge.integration  SessionMetricsCalculator :com.tqhit.battery.one.features.stats.discharge.integration  System :com.tqhit.battery.one.features.stats.discharge.integration  Test :com.tqhit.battery.one.features.stats.discharge.integration  Thread :com.tqhit.battery.one.features.stats.discharge.integration  ValidationResult :com.tqhit.battery.one.features.stats.discharge.integration  androidx :com.tqhit.battery.one.features.stats.discharge.integration  appLifecycleManager :com.tqhit.battery.one.features.stats.discharge.integration  apply :com.tqhit.battery.one.features.stats.discharge.integration  assertEquals :com.tqhit.battery.one.features.stats.discharge.integration  assertFalse :com.tqhit.battery.one.features.stats.discharge.integration  
assertTrue :com.tqhit.battery.one.features.stats.discharge.integration  coEvery :com.tqhit.battery.one.features.stats.discharge.integration  coVerify :com.tqhit.battery.one.features.stats.discharge.integration  com :com.tqhit.battery.one.features.stats.discharge.integration  createMockSession :com.tqhit.battery.one.features.stats.discharge.integration  createRepository :com.tqhit.battery.one.features.stats.discharge.integration  createTestSession :com.tqhit.battery.one.features.stats.discharge.integration  every :com.tqhit.battery.one.features.stats.discharge.integration  forEach :com.tqhit.battery.one.features.stats.discharge.integration  get :com.tqhit.battery.one.features.stats.discharge.integration  just :com.tqhit.battery.one.features.stats.discharge.integration  kotlin :com.tqhit.battery.one.features.stats.discharge.integration  listOf :com.tqhit.battery.one.features.stats.discharge.integration  mockContext :com.tqhit.battery.one.features.stats.discharge.integration  mockCurrentSessionCache :com.tqhit.battery.one.features.stats.discharge.integration  mockFullSessionReEstimator :com.tqhit.battery.one.features.stats.discharge.integration  mockPowerManager :com.tqhit.battery.one.features.stats.discharge.integration  mockScreenStateReceiver :com.tqhit.battery.one.features.stats.discharge.integration  mockSessionManager :com.tqhit.battery.one.features.stats.discharge.integration  mockk :com.tqhit.battery.one.features.stats.discharge.integration  mockkStatic :com.tqhit.battery.one.features.stats.discharge.integration  
repository :com.tqhit.battery.one.features.stats.discharge.integration  runTest :com.tqhit.battery.one.features.stats.discharge.integration  validationService :com.tqhit.battery.one.features.stats.discharge.integration  verify :com.tqhit.battery.one.features.stats.discharge.integration  DischargeSessionData Scom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest  System Scom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest  assertEquals Scom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest  
assertTrue Scom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest  com Scom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest  createTestSession Scom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest  kotlin Scom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeConstraintTest  Context ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  CoreBatteryStatus ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  DischargeSessionData ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  DischargeSessionRepository ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  MutableStateFlow ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  Runs ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  System ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  Thread ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  assertEquals ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  
assertTrue ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  coEvery ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  coVerify ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  com ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  createRepository ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  createTestSession ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  every ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  just ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  kotlin ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockContext ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockCurrentSessionCache ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockFullSessionReEstimator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockGapEstimationCalculator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockPowerManager ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockScreenStateReceiver ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockSessionManager ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockSessionMetricsCalculator ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  mockk ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  
repository ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  runTest ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  verify ^com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeEstimationIntegrationTest  DischargeSessionData Tcom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest  System Tcom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest  assertEquals Tcom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest  assertFalse Tcom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest  
assertTrue Tcom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest  createTestSession Tcom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest  kotlin Tcom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest  listOf Tcom.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeOscillationTest  AppLifecycleManager \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  AppState \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  CorrectionType \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  ProcessLifecycleOwner \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  ScreenTimeValidationService \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  appLifecycleManager \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  apply \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  assertEquals \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  assertFalse \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  
assertTrue \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  createMockSession \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  every \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  get \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  mockk \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  mockkStatic \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  runTest \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  validationService \com.tqhit.battery.one.features.stats.discharge.integration.ScreenTimeUiUpdateIntegrationTest  	Corrected Kcom.tqhit.battery.one.features.stats.discharge.integration.ValidationResult  Valid Kcom.tqhit.battery.one.features.stats.discharge.integration.ValidationResult  	lifecycle Ccom.tqhit.battery.one.features.stats.discharge.integration.androidx  LifecycleOwner Mcom.tqhit.battery.one.features.stats.discharge.integration.androidx.lifecycle  DischargeFragment ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeUiState ;com.tqhit.battery.one.features.stats.discharge.presentation  	Companion Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  screenOffTimeUI Lcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState  screenOnTimeUI Lcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiState  Before 9com.tqhit.battery.one.features.stats.discharge.repository  Boolean 9com.tqhit.battery.one.features.stats.discharge.repository  Context 9com.tqhit.battery.one.features.stats.discharge.repository  CoreBatteryStatus 9com.tqhit.battery.one.features.stats.discharge.repository  CurrentSessionCache 9com.tqhit.battery.one.features.stats.discharge.repository  DischargeSessionData 9com.tqhit.battery.one.features.stats.discharge.repository  DischargeSessionRepository 9com.tqhit.battery.one.features.stats.discharge.repository  DischargeSessionRepositoryTest 9com.tqhit.battery.one.features.stats.discharge.repository  ExperimentalCoroutinesApi 9com.tqhit.battery.one.features.stats.discharge.repository  FullSessionReEstimator 9com.tqhit.battery.one.features.stats.discharge.repository  GapEstimationCalculator 9com.tqhit.battery.one.features.stats.discharge.repository  Int 9com.tqhit.battery.one.features.stats.discharge.repository  Long 9com.tqhit.battery.one.features.stats.discharge.repository  MutableStateFlow 9com.tqhit.battery.one.features.stats.discharge.repository  OptIn 9com.tqhit.battery.one.features.stats.discharge.repository  PowerManager 9com.tqhit.battery.one.features.stats.discharge.repository  Runs 9com.tqhit.battery.one.features.stats.discharge.repository  ScreenStateReceiver 9com.tqhit.battery.one.features.stats.discharge.repository  SessionManager 9com.tqhit.battery.one.features.stats.discharge.repository  SessionMetricsCalculator 9com.tqhit.battery.one.features.stats.discharge.repository  System 9com.tqhit.battery.one.features.stats.discharge.repository  Test 9com.tqhit.battery.one.features.stats.discharge.repository  Thread 9com.tqhit.battery.one.features.stats.discharge.repository  assertEquals 9com.tqhit.battery.one.features.stats.discharge.repository  
assertNull 9com.tqhit.battery.one.features.stats.discharge.repository  
assertTrue 9com.tqhit.battery.one.features.stats.discharge.repository  coEvery 9com.tqhit.battery.one.features.stats.discharge.repository  coVerify 9com.tqhit.battery.one.features.stats.discharge.repository  com 9com.tqhit.battery.one.features.stats.discharge.repository  createRepository 9com.tqhit.battery.one.features.stats.discharge.repository  createTestSession 9com.tqhit.battery.one.features.stats.discharge.repository  every 9com.tqhit.battery.one.features.stats.discharge.repository  just 9com.tqhit.battery.one.features.stats.discharge.repository  mockContext 9com.tqhit.battery.one.features.stats.discharge.repository  mockCurrentSessionCache 9com.tqhit.battery.one.features.stats.discharge.repository  mockFullSessionReEstimator 9com.tqhit.battery.one.features.stats.discharge.repository  mockPowerManager 9com.tqhit.battery.one.features.stats.discharge.repository  mockScreenStateReceiver 9com.tqhit.battery.one.features.stats.discharge.repository  mockSessionManager 9com.tqhit.battery.one.features.stats.discharge.repository  mockk 9com.tqhit.battery.one.features.stats.discharge.repository  
repository 9com.tqhit.battery.one.features.stats.discharge.repository  runTest 9com.tqhit.battery.one.features.stats.discharge.repository  verify 9com.tqhit.battery.one.features.stats.discharge.repository  currentSession Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  incrementScreenTimeForUI Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  processBatteryStatus Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  Context Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  CoreBatteryStatus Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  DischargeSessionData Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  DischargeSessionRepository Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  MutableStateFlow Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  Runs Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  System Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  Thread Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  assertEquals Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  
assertNull Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  
assertTrue Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  coEvery Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  coVerify Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  com Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  createRepository Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  createTestSession Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  every Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  just Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockContext Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockCurrentSessionCache Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockFullSessionReEstimator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockGapEstimationCalculator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockPowerManager Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockScreenStateReceiver Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockSessionManager Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockSessionMetricsCalculator Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  mockk Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  
repository Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  runTest Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  verify Xcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepositoryTest  HealthCalculationMode 0com.tqhit.battery.one.features.stats.health.data  HealthChartData 0com.tqhit.battery.one.features.stats.health.data  HealthStatus 0com.tqhit.battery.one.features.stats.health.data  HealthStatusTest 0com.tqhit.battery.one.features.stats.health.data  System 0com.tqhit.battery.one.features.stats.health.data  Test 0com.tqhit.battery.one.features.stats.health.data  assertEquals 0com.tqhit.battery.one.features.stats.health.data  assertFalse 0com.tqhit.battery.one.features.stats.health.data  
assertTrue 0com.tqhit.battery.one.features.stats.health.data  createCalculated 0com.tqhit.battery.one.features.stats.health.data  
createDefault 0com.tqhit.battery.one.features.stats.health.data  
CUMULATIVE Fcom.tqhit.battery.one.features.stats.health.data.HealthCalculationMode  SINGULAR Fcom.tqhit.battery.one.features.stats.health.data.HealthCalculationMode  getDescription Fcom.tqhit.battery.one.features.stats.health.data.HealthCalculationMode  getDisplayName Fcom.tqhit.battery.one.features.stats.health.data.HealthCalculationMode  	Companion @com.tqhit.battery.one.features.stats.health.data.HealthChartData  batteryPercentageEntries @com.tqhit.battery.one.features.stats.health.data.HealthChartData  copy @com.tqhit.battery.one.features.stats.health.data.HealthChartData  createEmpty @com.tqhit.battery.one.features.stats.health.data.HealthChartData  createSample @com.tqhit.battery.one.features.stats.health.data.HealthChartData  
dailyWearData @com.tqhit.battery.one.features.stats.health.data.HealthChartData  getMaxBatteryPercentage @com.tqhit.battery.one.features.stats.health.data.HealthChartData  isValid @com.tqhit.battery.one.features.stats.health.data.HealthChartData  selectedTimeRangeHours @com.tqhit.battery.one.features.stats.health.data.HealthChartData  temperatureEntries @com.tqhit.battery.one.features.stats.health.data.HealthChartData  createEmpty Jcom.tqhit.battery.one.features.stats.health.data.HealthChartData.Companion  createSample Jcom.tqhit.battery.one.features.stats.health.data.HealthChartData.Companion  	Companion =com.tqhit.battery.one.features.stats.health.data.HealthStatus  calculationMode =com.tqhit.battery.one.features.stats.health.data.HealthStatus  createCalculated =com.tqhit.battery.one.features.stats.health.data.HealthStatus  
createDefault =com.tqhit.battery.one.features.stats.health.data.HealthStatus  designCapacityMah =com.tqhit.battery.one.features.stats.health.data.HealthStatus  effectiveCapacityMah =com.tqhit.battery.one.features.stats.health.data.HealthStatus  getCapacityLossMah =com.tqhit.battery.one.features.stats.health.data.HealthStatus  getDegradationPercentage =com.tqhit.battery.one.features.stats.health.data.HealthStatus  healthPercentage =com.tqhit.battery.one.features.stats.health.data.HealthStatus  isValid =com.tqhit.battery.one.features.stats.health.data.HealthStatus  timestampEpochMillis =com.tqhit.battery.one.features.stats.health.data.HealthStatus  
totalSessions =com.tqhit.battery.one.features.stats.health.data.HealthStatus  createCalculated Gcom.tqhit.battery.one.features.stats.health.data.HealthStatus.Companion  
createDefault Gcom.tqhit.battery.one.features.stats.health.data.HealthStatus.Companion  HealthCalculationMode Acom.tqhit.battery.one.features.stats.health.data.HealthStatusTest  HealthStatus Acom.tqhit.battery.one.features.stats.health.data.HealthStatusTest  System Acom.tqhit.battery.one.features.stats.health.data.HealthStatusTest  assertEquals Acom.tqhit.battery.one.features.stats.health.data.HealthStatusTest  assertFalse Acom.tqhit.battery.one.features.stats.health.data.HealthStatusTest  
assertTrue Acom.tqhit.battery.one.features.stats.health.data.HealthStatusTest  createCalculated Acom.tqhit.battery.one.features.stats.health.data.HealthStatusTest  
createDefault Acom.tqhit.battery.one.features.stats.health.data.HealthStatusTest  Before 2com.tqhit.battery.one.features.stats.health.domain  CalculateBatteryHealthUseCase 2com.tqhit.battery.one.features.stats.health.domain  !CalculateBatteryHealthUseCaseTest 2com.tqhit.battery.one.features.stats.health.domain  HealthCalculationMode 2com.tqhit.battery.one.features.stats.health.domain  Test 2com.tqhit.battery.one.features.stats.health.domain  assertEquals 2com.tqhit.battery.one.features.stats.health.domain  assertFalse 2com.tqhit.battery.one.features.stats.health.domain  
assertTrue 2com.tqhit.battery.one.features.stats.health.domain  calculateCumulativeHealth Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  calculateDegradationRate Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  calculateEffectiveCapacity Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  calculateHealthStatus Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  calculateSingularHealth Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  estimateRemainingLifespan Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  isFullChargeCycle Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  CalculateBatteryHealthUseCase Tcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest  HealthCalculationMode Tcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest  assertEquals Tcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest  assertFalse Tcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest  
assertTrue Tcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest  calculateBatteryHealthUseCase Tcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCaseTest  Entry 6com.tqhit.battery.one.features.stats.health.repository  HealthChartData 6com.tqhit.battery.one.features.stats.health.repository  HealthChartDataTest 6com.tqhit.battery.one.features.stats.health.repository  List 6com.tqhit.battery.one.features.stats.health.repository  Test 6com.tqhit.battery.one.features.stats.health.repository  all 6com.tqhit.battery.one.features.stats.health.repository  assertEquals 6com.tqhit.battery.one.features.stats.health.repository  assertFalse 6com.tqhit.battery.one.features.stats.health.repository  
assertTrue 6com.tqhit.battery.one.features.stats.health.repository  average 6com.tqhit.battery.one.features.stats.health.repository  createEmpty 6com.tqhit.battery.one.features.stats.health.repository  createSample 6com.tqhit.battery.one.features.stats.health.repository  	emptyList 6com.tqhit.battery.one.features.stats.health.repository  first 6com.tqhit.battery.one.features.stats.health.repository  forEach 6com.tqhit.battery.one.features.stats.health.repository  last 6com.tqhit.battery.one.features.stats.health.repository  listOf 6com.tqhit.battery.one.features.stats.health.repository  map 6com.tqhit.battery.one.features.stats.health.repository  	maxOrNull 6com.tqhit.battery.one.features.stats.health.repository  	minOrNull 6com.tqhit.battery.one.features.stats.health.repository  rangeTo 6com.tqhit.battery.one.features.stats.health.repository  sorted 6com.tqhit.battery.one.features.stats.health.repository  Entry Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  HealthChartData Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  List Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  all Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  assertEquals Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  assertFalse Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  
assertTrue Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  average Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  createEmpty Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  createSample Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  	emptyList Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  first Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  last Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  listOf Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  map Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  	maxOrNull Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  	minOrNull Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  rangeTo Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  sorted Jcom.tqhit.battery.one.features.stats.health.repository.HealthChartDataTest  'UnifiedBatteryNotificationServiceHelper 2com.tqhit.battery.one.features.stats.notifications  isServiceRunning Zcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper  HealthFragment #com.tqhit.battery.one.fragment.main  SettingsFragment #com.tqhit.battery.one.fragment.main  AnimationGridFragment -com.tqhit.battery.one.fragment.main.animation  
AppRepository  com.tqhit.battery.one.repository  chargeAlarmPercentFlow .com.tqhit.battery.one.repository.AppRepository  getBatteryCapacity .com.tqhit.battery.one.repository.AppRepository  After com.tqhit.battery.one.utils  BackgroundPermissionManager com.tqhit.battery.one.utils  BackgroundPermissionManagerTest com.tqhit.battery.one.utils  Before com.tqhit.battery.one.utils  Build com.tqhit.battery.one.utils  Config com.tqhit.battery.one.utils  Context com.tqhit.battery.one.utils  Math com.tqhit.battery.one.utils  PackageManager com.tqhit.battery.one.utils  PowerManager com.tqhit.battery.one.utils  RobolectricTestRunner com.tqhit.battery.one.utils  RunWith com.tqhit.battery.one.utils  SharedPreferences com.tqhit.battery.one.utils  System com.tqhit.battery.one.utils  Test com.tqhit.battery.one.utils  Unit com.tqhit.battery.one.utils  assertEquals com.tqhit.battery.one.utils  assertFalse com.tqhit.battery.one.utils  
assertTrue com.tqhit.battery.one.utils  every com.tqhit.battery.one.utils  getRemainingCooldownTime com.tqhit.battery.one.utils  isIgnoringBatteryOptimizations com.tqhit.battery.one.utils  isInCooldownPeriod com.tqhit.battery.one.utils  mockContext com.tqhit.battery.one.utils  
mockEditor com.tqhit.battery.one.utils  mockPowerManager com.tqhit.battery.one.utils  mockSharedPreferences com.tqhit.battery.one.utils  mockk com.tqhit.battery.one.utils  mockkStatic com.tqhit.battery.one.utils  recordDialogDismissal com.tqhit.battery.one.utils  $shouldShowBackgroundPermissionDialog com.tqhit.battery.one.utils  
unmockkStatic com.tqhit.battery.one.utils  verify com.tqhit.battery.one.utils  getRemainingCooldownTime 7com.tqhit.battery.one.utils.BackgroundPermissionManager  isIgnoringBatteryOptimizations 7com.tqhit.battery.one.utils.BackgroundPermissionManager  isInCooldownPeriod 7com.tqhit.battery.one.utils.BackgroundPermissionManager  recordDialogDismissal 7com.tqhit.battery.one.utils.BackgroundPermissionManager  $shouldShowBackgroundPermissionDialog 7com.tqhit.battery.one.utils.BackgroundPermissionManager  BackgroundPermissionManager ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  Build ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  Context ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  Math ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  System ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  Unit ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  assertEquals ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  assertFalse ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  
assertTrue ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  every ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  getRemainingCooldownTime ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  isIgnoringBatteryOptimizations ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  isInCooldownPeriod ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  mockContext ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  
mockEditor ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  mockPackageManager ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  mockPowerManager ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  mockSharedPreferences ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  mockk ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  mockkStatic ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  recordDialogDismissal ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  $shouldShowBackgroundPermissionDialog ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  
unmockkStatic ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  verify ;com.tqhit.battery.one.utils.BackgroundPermissionManagerTest  Editor -com.tqhit.battery.one.utils.SharedPreferences  Module dagger  	InstallIn dagger.hilt  AssetManager io.mockk  BatteryStyleCategory io.mockk  BatteryStyleRepositoryImpl io.mockk  Before io.mockk  Boolean io.mockk  ByteArrayInputStream io.mockk  Context io.mockk  CoreBatteryStatus io.mockk  CurrentSessionCache io.mockk  DischargeRateCalculator io.mockk  DischargeRatesCache io.mockk  DischargeSessionData io.mockk  DischargeSessionRepository io.mockk  ExperimentalCoroutinesApi io.mockk  FirebaseRemoteConfigHelper io.mockk  FullSessionReEstimator io.mockk  GapEstimationCalculator io.mockk  Gson io.mockk  IOException io.mockk  Int io.mockk  Long io.mockk  MockKAdditionalAnswerScope io.mockk  MockKMatcherScope io.mockk  MockKStubScope io.mockk  MockKVerificationScope io.mockk  MutableStateFlow io.mockk  OptIn io.mockk  PowerManager io.mockk  Runs io.mockk  ScreenStateReceiver io.mockk  ScreenTimeCalculator io.mockk  SessionManager io.mockk  SessionMetricsCalculator io.mockk  System io.mockk  Test io.mockk  Thread io.mockk  
TimeConverter io.mockk  any io.mockk  assertEquals io.mockk  assertFalse io.mockk  
assertNotNull io.mockk  
assertNull io.mockk  
assertTrue io.mockk  coEvery io.mockk  coVerify io.mockk  com io.mockk  createRepository io.mockk  createTestSession io.mockk  every io.mockk  find io.mockk  first io.mockk  just io.mockk  kotlin io.mockk  mockAssetManager io.mockk  mockContext io.mockk  mockCurrentSessionCache io.mockk  mockDischargeRateCalculator io.mockk  mockDischargeRatesCache io.mockk  mockFullSessionReEstimator io.mockk  mockPowerManager io.mockk  mockRemoteConfigHelper io.mockk  mockScreenStateReceiver io.mockk  mockScreenTimeCalculator io.mockk  mockSessionManager io.mockk  mockTimeConverter io.mockk  mockk io.mockk  mockkStatic io.mockk  reEstimator io.mockk  
repository io.mockk  runTest io.mockk  testStylesJson io.mockk  toByteArray io.mockk  
trimIndent io.mockk  
unmockkStatic io.mockk  verify io.mockk  BatteryStyleCategory io.mockk.MockKMatcherScope  Build io.mockk.MockKMatcherScope  Context io.mockk.MockKMatcherScope  ProcessLifecycleOwner io.mockk.MockKMatcherScope  any io.mockk.MockKMatcherScope  appLifecycleManager io.mockk.MockKMatcherScope  get io.mockk.MockKMatcherScope  mockAppRepository io.mockk.MockKMatcherScope  mockAssetManager io.mockk.MockKMatcherScope  mockContext io.mockk.MockKMatcherScope  mockCoreBatteryStatsProvider io.mockk.MockKMatcherScope  mockCurrentSessionCache io.mockk.MockKMatcherScope  mockDischargeRateCalculator io.mockk.MockKMatcherScope  mockDischargeRatesCache io.mockk.MockKMatcherScope  
mockEditor io.mockk.MockKMatcherScope  mockFullSessionReEstimator io.mockk.MockKMatcherScope  mockGetBatteryStylesUseCase io.mockk.MockKMatcherScope  mockPowerManager io.mockk.MockKMatcherScope  mockRemoteConfigHelper io.mockk.MockKMatcherScope  mockRepository io.mockk.MockKMatcherScope  mockScreenStateReceiver io.mockk.MockKMatcherScope  mockScreenTimeCalculator io.mockk.MockKMatcherScope  mockSessionManager io.mockk.MockKMatcherScope  mockSharedPreferences io.mockk.MockKMatcherScope  mockStatsChargeRepository io.mockk.MockKMatcherScope  mockTimeConverter io.mockk.MockKMatcherScope  just io.mockk.MockKStubScope  returns io.mockk.MockKStubScope  throws io.mockk.MockKStubScope  BatteryStyleCategory io.mockk.MockKVerificationScope  any io.mockk.MockKVerificationScope  mockAssetManager io.mockk.MockKVerificationScope  
mockEditor io.mockk.MockKVerificationScope  mockFullSessionReEstimator io.mockk.MockKVerificationScope  mockOnPremiumUnlockClick io.mockk.MockKVerificationScope  mockOnStyleClick io.mockk.MockKVerificationScope  mockOnStyleLongClick io.mockk.MockKVerificationScope  mockRepository io.mockk.MockKVerificationScope  mockScreenTimeCalculator io.mockk.MockKVerificationScope  mockSessionManager io.mockk.MockKVerificationScope  ByteArrayInputStream java.io  IOException java.io  InputStream java.io  Class 	java.lang  ClassNotFoundException 	java.lang  	Exception 	java.lang  RuntimeException 	java.lang  forName java.lang.Class  
getAnnotation java.lang.Class  	modifiers java.lang.Class  name java.lang.Class  packageName java.lang.Class  
simpleName java.lang.Class  message java.lang.Exception  abs java.lang.Math  currentTimeMillis java.lang.System  sleep java.lang.Thread  
isAbstract java.lang.reflect.Modifier  Array kotlin  	ByteArray kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  apply kotlin  assert kotlin  map kotlin  repeat kotlin  toList kotlin  toString 
kotlin.Any  forEach kotlin.Array  map kotlin.Array  size kotlin.Array  toList kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Double  div 
kotlin.Double  minus 
kotlin.Double  plus 
kotlin.Double  rangeTo 
kotlin.Double  times 
kotlin.Double  toLong 
kotlin.Double  
unaryMinus 
kotlin.Double  	compareTo kotlin.Float  minus kotlin.Float  rangeTo kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  times 
kotlin.Int  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  times kotlin.Long  toDouble kotlin.Long  toInt kotlin.Long  
unaryMinus kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  first kotlin.Pair  second kotlin.Pair  contains 
kotlin.String  
isNotEmpty 
kotlin.String  plus 
kotlin.String  toByteArray 
kotlin.String  
trimIndent 
kotlin.String  message kotlin.Throwable  IntIterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  Set kotlin.collections  all kotlin.collections  any kotlin.collections  average kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  first kotlin.collections  forEach kotlin.collections  
isNotEmpty kotlin.collections  last kotlin.collections  listOf kotlin.collections  map kotlin.collections  	maxOrNull kotlin.collections  	minOrNull kotlin.collections  
mutableListOf kotlin.collections  sorted kotlin.collections  toByteArray kotlin.collections  toList kotlin.collections  toSet kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  all kotlin.collections.List  any kotlin.collections.List  average kotlin.collections.List  contains kotlin.collections.List  filter kotlin.collections.List  find kotlin.collections.List  first kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  last kotlin.collections.List  map kotlin.collections.List  	maxOrNull kotlin.collections.List  	minOrNull kotlin.collections.List  size kotlin.collections.List  sorted kotlin.collections.List  toSet kotlin.collections.List  add kotlin.collections.MutableList  get kotlin.collections.MutableList  size kotlin.collections.MutableList  size kotlin.collections.Set  SuspendFunction1 kotlin.coroutines  endsWith 	kotlin.io  println 	kotlin.io  java 
kotlin.jvm  kotlin 
kotlin.jvm  abs kotlin.math  	CharRange 
kotlin.ranges  ClosedFloatingPointRange 
kotlin.ranges  ClosedRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  first 
kotlin.ranges  last 
kotlin.ranges  rangeTo 
kotlin.ranges  until 
kotlin.ranges  contains &kotlin.ranges.ClosedFloatingPointRange  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  average kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  last kotlin.sequences  map kotlin.sequences  	maxOrNull kotlin.sequences  	minOrNull kotlin.sequences  sorted kotlin.sequences  toList kotlin.sequences  toSet kotlin.sequences  all kotlin.text  any kotlin.text  contains kotlin.text  endsWith kotlin.text  filter kotlin.text  find kotlin.text  first kotlin.text  forEach kotlin.text  
isNotEmpty kotlin.text  last kotlin.text  map kotlin.text  	maxOrNull kotlin.text  	minOrNull kotlin.text  repeat kotlin.text  toByteArray kotlin.text  toList kotlin.text  toSet kotlin.text  
trimIndent kotlin.text  Dispatchers kotlinx.coroutines  ExperimentalCoroutinesApi kotlinx.coroutines  	resetMain kotlinx.coroutines.Dispatchers  setMain kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  
SharedFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  first kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  value (kotlinx.coroutines.flow.MutableStateFlow  first !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  StandardTestDispatcher kotlinx.coroutines.test  TestDispatcher kotlinx.coroutines.test  	TestScope kotlinx.coroutines.test  advanceUntilIdle kotlinx.coroutines.test  	resetMain kotlinx.coroutines.test  runTest kotlinx.coroutines.test  setMain kotlinx.coroutines.test  AppState !kotlinx.coroutines.test.TestScope  BatteryGalleryEvent !kotlinx.coroutines.test.TestScope  BatteryStyleCategory !kotlinx.coroutines.test.TestScope  ByteArrayInputStream !kotlinx.coroutines.test.TestScope  CoreBatteryStatus !kotlinx.coroutines.test.TestScope  CorrectionType !kotlinx.coroutines.test.TestScope  IOException !kotlinx.coroutines.test.TestScope  MutableStateFlow !kotlinx.coroutines.test.TestScope  RuntimeException !kotlinx.coroutines.test.TestScope  ScreenTimeCalculator !kotlinx.coroutines.test.TestScope  System !kotlinx.coroutines.test.TestScope  Thread !kotlinx.coroutines.test.TestScope  advanceUntilIdle !kotlinx.coroutines.test.TestScope  all !kotlinx.coroutines.test.TestScope  any !kotlinx.coroutines.test.TestScope  appLifecycleManager !kotlinx.coroutines.test.TestScope  assertEquals !kotlinx.coroutines.test.TestScope  assertFalse !kotlinx.coroutines.test.TestScope  
assertNotNull !kotlinx.coroutines.test.TestScope  
assertNull !kotlinx.coroutines.test.TestScope  
assertTrue !kotlinx.coroutines.test.TestScope  coEvery !kotlinx.coroutines.test.TestScope  coVerify !kotlinx.coroutines.test.TestScope  contains !kotlinx.coroutines.test.TestScope  coreBatteryStatsProvider !kotlinx.coroutines.test.TestScope  
createDefault !kotlinx.coroutines.test.TestScope  createMockSession !kotlinx.coroutines.test.TestScope  createRepository !kotlinx.coroutines.test.TestScope  createTestSession !kotlinx.coroutines.test.TestScope  every !kotlinx.coroutines.test.TestScope  filter !kotlinx.coroutines.test.TestScope  find !kotlinx.coroutines.test.TestScope  first !kotlinx.coroutines.test.TestScope  
isNotEmpty !kotlinx.coroutines.test.TestScope  kotlin !kotlinx.coroutines.test.TestScope  mockAssetManager !kotlinx.coroutines.test.TestScope  mockCoreBatteryStatsProvider !kotlinx.coroutines.test.TestScope  mockCurrentSessionCache !kotlinx.coroutines.test.TestScope  mockDischargeRateCalculator !kotlinx.coroutines.test.TestScope  mockDischargeRatesCache !kotlinx.coroutines.test.TestScope  mockFullSessionReEstimator !kotlinx.coroutines.test.TestScope  mockGetBatteryStylesUseCase !kotlinx.coroutines.test.TestScope  	mockOwner !kotlinx.coroutines.test.TestScope  mockPowerManager !kotlinx.coroutines.test.TestScope  mockRemoteConfigHelper !kotlinx.coroutines.test.TestScope  mockRepository !kotlinx.coroutines.test.TestScope  mockScreenTimeCalculator !kotlinx.coroutines.test.TestScope  mockSessionManager !kotlinx.coroutines.test.TestScope  mockTimeConverter !kotlinx.coroutines.test.TestScope  mockk !kotlinx.coroutines.test.TestScope  
mutableListOf !kotlinx.coroutines.test.TestScope  reEstimator !kotlinx.coroutines.test.TestScope  repeat !kotlinx.coroutines.test.TestScope  
repository !kotlinx.coroutines.test.TestScope  
testStyles !kotlinx.coroutines.test.TestScope  testStylesJson !kotlinx.coroutines.test.TestScope  toByteArray !kotlinx.coroutines.test.TestScope  tracker !kotlinx.coroutines.test.TestScope  until !kotlinx.coroutines.test.TestScope  useCase !kotlinx.coroutines.test.TestScope  validationService !kotlinx.coroutines.test.TestScope  verify !kotlinx.coroutines.test.TestScope  	viewModel !kotlinx.coroutines.test.TestScope  After 	org.junit  AnimationGridFragment 	org.junit  AppLifecycleManager 	org.junit  AppState 	org.junit  ApplicationProvider 	org.junit  AssetManager 	org.junit  BackgroundPermissionManager 	org.junit  BatteryGalleryEvent 	org.junit  BatteryGalleryViewModel 	org.junit  BatteryStyle 	org.junit  BatteryStyleAdapter 	org.junit  BatteryStyleCategory 	org.junit  BatteryStyleConfig 	org.junit  BatteryStyleRepository 	org.junit  BatteryStyleRepositoryImpl 	org.junit  Before 	org.junit  Boolean 	org.junit  Build 	org.junit  ByteArrayInputStream 	org.junit  CalculateBatteryHealthUseCase 	org.junit  $CalculateSimpleChargeEstimateUseCase 	org.junit  Config 	org.junit  Context 	org.junit  CoreBatteryStatsProvider 	org.junit  CoreBatteryStatus 	org.junit  CorrectionType 	org.junit  CurrentSessionCache 	org.junit  DefaultCoreBatteryStatsProvider 	org.junit  DischargeCalculator 	org.junit  DischargeFragment 	org.junit  DischargeRateCalculator 	org.junit  DischargeRatesCache 	org.junit  DischargeSessionData 	org.junit  DischargeSessionRepository 	org.junit  Dispatchers 	org.junit  Entry 	org.junit  	Exception 	org.junit  ExperimentalCoroutinesApi 	org.junit  FirebaseRemoteConfigHelper 	org.junit  FullSessionReEstimator 	org.junit  GapEstimationCalculator 	org.junit  GetBatteryStylesUseCase 	org.junit  Gson 	org.junit  HealthCalculationMode 	org.junit  HealthChartData 	org.junit  HealthFragment 	org.junit  HealthStatus 	org.junit  IOException 	org.junit  InstantTaskExecutorRule 	org.junit  Int 	org.junit  LifecycleOwner 	org.junit  List 	org.junit  Long 	org.junit  Math 	org.junit  MutableStateFlow 	org.junit  NavigationState 	org.junit  NavigationStateChange 	org.junit  OptIn 	org.junit  PackageManager 	org.junit  Pair 	org.junit  PowerManager 	org.junit  ProcessLifecycleOwner 	org.junit  R 	org.junit  RecyclerView 	org.junit  RobolectricTestRunner 	org.junit  Rule 	org.junit  RunWith 	org.junit  Runs 	org.junit  RuntimeException 	org.junit  ScreenStateReceiver 	org.junit  ScreenStateTimeTracker 	org.junit  ScreenTimeCalculator 	org.junit  ScreenTimeValidationService 	org.junit  SessionManager 	org.junit  SessionMetricsCalculator 	org.junit  SettingsFragment 	org.junit  SharedPreferences 	org.junit  StandardTestDispatcher 	org.junit  StateChangeReason 	org.junit  StatsChargeFragment 	org.junit  StatsChargeSession 	org.junit  StatsChargeStatus 	org.junit  String 	org.junit  System 	org.junit  Test 	org.junit  Thread 	org.junit  
TimeConverter 	org.junit  'UnifiedBatteryNotificationServiceHelper 	org.junit  Unit 	org.junit  ValidationResult 	org.junit  View 	org.junit  all 	org.junit  androidx 	org.junit  any 	org.junit  appLifecycleManager 	org.junit  apply 	org.junit  assertEquals 	org.junit  assertFalse 	org.junit  assertNotEquals 	org.junit  
assertNotNull 	org.junit  
assertNull 	org.junit  
assertTrue 	org.junit  average 	org.junit  coEvery 	org.junit  coVerify 	org.junit  com 	org.junit  contains 	org.junit  coreBatteryStatsProvider 	org.junit  createCalculated 	org.junit  createChargingState 	org.junit  
createDefault 	org.junit  createDefaultState 	org.junit  createDischargingState 	org.junit  createEmpty 	org.junit  createMockSession 	org.junit  	createNew 	org.junit  createRepository 	org.junit  createSample 	org.junit  createTestSession 	org.junit  	emptyList 	org.junit  
endSession 	org.junit  every 	org.junit  fail 	org.junit  filter 	org.junit  find 	org.junit  findByDisplayName 	org.junit  first 	org.junit  forEach 	org.junit  
fromString 	org.junit  get 	org.junit  getAllSorted 	org.junit  
getDefault 	org.junit  getMainFilterCategories 	org.junit  getRemainingCooldownTime 	org.junit  isIgnoringBatteryOptimizations 	org.junit  isInCooldownPeriod 	org.junit  
isNotEmpty 	org.junit  java 	org.junit  just 	org.junit  kotlin 	org.junit  last 	org.junit  listOf 	org.junit  map 	org.junit  	maxOrNull 	org.junit  	minOrNull 	org.junit  mockAssetManager 	org.junit  mockContext 	org.junit  mockCoreBatteryStatsProvider 	org.junit  mockCurrentSessionCache 	org.junit  mockDischargeRateCalculator 	org.junit  mockDischargeRatesCache 	org.junit  
mockEditor 	org.junit  mockFullSessionReEstimator 	org.junit  mockGetBatteryStylesUseCase 	org.junit  mockOnPremiumUnlockClick 	org.junit  mockOnStyleClick 	org.junit  mockOnStyleLongClick 	org.junit  	mockOwner 	org.junit  mockPowerManager 	org.junit  mockRemoteConfigHelper 	org.junit  mockRepository 	org.junit  mockScreenStateReceiver 	org.junit  mockScreenTimeCalculator 	org.junit  mockSessionManager 	org.junit  mockSharedPreferences 	org.junit  mockTimeConverter 	org.junit  mockk 	org.junit  mockkStatic 	org.junit  
mutableListOf 	org.junit  println 	org.junit  rangeTo 	org.junit  reEstimator 	org.junit  recordDialogDismissal 	org.junit  repeat 	org.junit  
repository 	org.junit  	resetMain 	org.junit  runTest 	org.junit  setMain 	org.junit  $shouldShowBackgroundPermissionDialog 	org.junit  sorted 	org.junit  
testStyles 	org.junit  testStylesJson 	org.junit  toByteArray 	org.junit  toList 	org.junit  toSet 	org.junit  tracker 	org.junit  
trimIndent 	org.junit  
unmockkStatic 	org.junit  until 	org.junit  useCase 	org.junit  validationService 	org.junit  verify 	org.junit  	viewModel 	org.junit  assertEquals org.junit.Assert  assertFalse org.junit.Assert  assertNotEquals org.junit.Assert  
assertNotNull org.junit.Assert  
assertNull org.junit.Assert  
assertTrue org.junit.Assert  fail org.junit.Assert  BatteryStyleViewHolder org.junit.BatteryStyleAdapter  Editor org.junit.SharedPreferences  	Corrected org.junit.ValidationResult  Valid org.junit.ValidationResult  	lifecycle org.junit.androidx  LifecycleOwner org.junit.androidx.lifecycle  RunWith org.junit.runner  RobolectricTestRunner org.robolectric  Config org.robolectric.annotation  NONE !org.robolectric.annotation.Config  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  v android.util.Log  w android.util.Log  FirebaseApp com.google.firebase  Log 4com.tqhit.battery.one.features.emoji.data.repository  String 4com.tqhit.battery.one.features.emoji.data.repository  mockkStatic 4com.tqhit.battery.one.features.emoji.data.repository  Log Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  mockkStatic Scom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImplTest  Log 4com.tqhit.battery.one.features.emoji.domain.use_case  String 4com.tqhit.battery.one.features.emoji.domain.use_case  mockkStatic 4com.tqhit.battery.one.features.emoji.domain.use_case  Log Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  mockkStatic Pcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCaseTest  Log 9com.tqhit.battery.one.features.emoji.presentation.gallery  String 9com.tqhit.battery.one.features.emoji.presentation.gallery  mockkStatic 9com.tqhit.battery.one.features.emoji.presentation.gallery  Log Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  mockkStatic Ucom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModelTest  FirebaseApp Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  	ShadowLog Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  System Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  mockkStatic Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  FirebaseApp Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  	ShadowLog Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  System Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  mockkStatic Ycom.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapterTest  Log io.mockk  String io.mockk  Log io.mockk.MockKMatcherScope  out java.lang.System  FirebaseApp 	org.junit  Log 	org.junit  	ShadowLog 	org.junit  	ShadowLog org.robolectric.shadows  stream !org.robolectric.shadows.ShadowLog                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 