package com.tqhit.battery.one.ads.core;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ApplovinNativeAdManager_Factory implements Factory<ApplovinNativeAdManager> {
  private final Provider<Context> contextProvider;

  public ApplovinNativeAdManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ApplovinNativeAdManager get() {
    return newInstance(contextProvider.get());
  }

  public static ApplovinNativeAdManager_Factory create(Provider<Context> contextProvider) {
    return new ApplovinNativeAdManager_Factory(contextProvider);
  }

  public static ApplovinNativeAdManager newInstance(Context context) {
    return new ApplovinNativeAdManager(context);
  }
}
