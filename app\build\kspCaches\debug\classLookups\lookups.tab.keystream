  Activity android.app  Application android.app  Dialog android.app  Service android.app  Context android.content  ContextWrapper android.content  SharedPreferences android.content  Editor !android.content.SharedPreferences  Log android.util  ContextThemeWrapper android.view  View android.view  ProgressBar android.widget  ComponentActivity androidx.activity  	Companion #androidx.activity.ComponentActivity  AppCompatActivity androidx.appcompat.app  ComponentActivity androidx.core.app  SplashScreen androidx.core.splashscreen  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  	Lifecycle androidx.lifecycle  	ViewModel androidx.lifecycle  Event androidx.lifecycle.Lifecycle  	Companion "androidx.lifecycle.Lifecycle.Event  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  Gson com.google.gson  AdLibHiltApplication com.tqhit.adlib.sdk  AdLibBaseApplication com.tqhit.adlib.sdk.base  AdLibBaseActivity com.tqhit.adlib.sdk.base.ui  AdLibBaseDialog com.tqhit.adlib.sdk.base.ui  AdLibBaseFragment com.tqhit.adlib.sdk.base.ui  PreferencesHelper com.tqhit.adlib.sdk.data.local  BatteryApplication com.tqhit.battery.one  	Companion (com.tqhit.battery.one.BatteryApplication  AnimationActivity (com.tqhit.battery.one.activity.animation  MainActivity #com.tqhit.battery.one.activity.main  	Companion 0com.tqhit.battery.one.activity.main.MainActivity  ChargingOverlayActivity &com.tqhit.battery.one.activity.overlay  EnterPasswordActivity 'com.tqhit.battery.one.activity.password  	Companion =com.tqhit.battery.one.activity.password.EnterPasswordActivity  SplashActivity %com.tqhit.battery.one.activity.splash  	Companion 4com.tqhit.battery.one.activity.splash.SplashActivity  StartingActivity 'com.tqhit.battery.one.activity.starting  ApplovinAppOpenAdManager com.tqhit.battery.one.ads.core  ApplovinBannerAdManager com.tqhit.battery.one.ads.core  ApplovinInterstitialAdManager com.tqhit.battery.one.ads.core  ApplovinNativeAdManager com.tqhit.battery.one.ads.core  ApplovinRewardedAdManager com.tqhit.battery.one.ads.core  VerticalProgressBar (com.tqhit.battery.one.component.progress  ActivityAnimationBinding !com.tqhit.battery.one.databinding  ActivityChargingOverlayBinding !com.tqhit.battery.one.databinding  ActivityEnterPasswordBinding !com.tqhit.battery.one.databinding  ActivityMainBinding !com.tqhit.battery.one.databinding  ActivitySplashBinding !com.tqhit.battery.one.databinding  ActivityStartingBinding !com.tqhit.battery.one.databinding  DialogSelectBatteryAlarmBinding !com.tqhit.battery.one.databinding  "DialogSelectBatteryAlarmLowBinding !com.tqhit.battery.one.databinding  FragmentAnimationGridBinding !com.tqhit.battery.one.databinding  FragmentChargeBinding !com.tqhit.battery.one.databinding  FragmentHealthBinding !com.tqhit.battery.one.databinding  FragmentSettingsBinding !com.tqhit.battery.one.databinding  NewFragmentDischargeBinding !com.tqhit.battery.one.databinding  SelectBatteryAlarmDialog "com.tqhit.battery.one.dialog.alarm  SelectBatteryAlarmLowDialog "com.tqhit.battery.one.dialog.alarm  ChangeCapacityDialog %com.tqhit.battery.one.dialog.capacity  SetupPasswordDialog %com.tqhit.battery.one.dialog.capacity  SelectLanguageDialog %com.tqhit.battery.one.dialog.language  BackgroundPermissionDialog 'com.tqhit.battery.one.dialog.permission  	Companion Bcom.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog  SelectColorDialog "com.tqhit.battery.one.dialog.theme  SelectThemeDialog "com.tqhit.battery.one.dialog.theme  
LoadingDialog "com.tqhit.battery.one.dialog.utils  NotificationDialog "com.tqhit.battery.one.dialog.utils  BatteryStyleRepositoryImpl 4com.tqhit.battery.one.features.emoji.data.repository  	Companion Ocom.tqhit.battery.one.features.emoji.data.repository.BatteryStyleRepositoryImpl  EmojiBatteryDIModule 'com.tqhit.battery.one.features.emoji.di  BatteryStyleRepository 6com.tqhit.battery.one.features.emoji.domain.repository  GetBatteryStylesUseCase 4com.tqhit.battery.one.features.emoji.domain.use_case  	Companion Lcom.tqhit.battery.one.features.emoji.domain.use_case.GetBatteryStylesUseCase  BatteryGalleryViewModel 9com.tqhit.battery.one.features.emoji.presentation.gallery  EmojiBatteryFragment 9com.tqhit.battery.one.features.emoji.presentation.gallery  	Companion Qcom.tqhit.battery.one.features.emoji.presentation.gallery.BatteryGalleryViewModel  	Companion Ncom.tqhit.battery.one.features.emoji.presentation.gallery.EmojiBatteryFragment  BatteryStyleAdapter Acom.tqhit.battery.one.features.emoji.presentation.gallery.adapter  DynamicNavigationManager )com.tqhit.battery.one.features.navigation  FragmentLifecycleOptimizer )com.tqhit.battery.one.features.navigation  	Companion Bcom.tqhit.battery.one.features.navigation.DynamicNavigationManager  	Companion Dcom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer  
FragmentState Dcom.tqhit.battery.one.features.navigation.FragmentLifecycleOptimizer  AppPowerConsumptionData 2com.tqhit.battery.one.features.stats.apppower.data  AppPowerConsumptionSummary 2com.tqhit.battery.one.features.stats.apppower.data  UsageStatsPermissionManager 8com.tqhit.battery.one.features.stats.apppower.permission  	Companion Tcom.tqhit.battery.one.features.stats.apppower.permission.UsageStatsPermissionManager  AppPowerConsumptionAdapter :com.tqhit.battery.one.features.stats.apppower.presentation  AppPowerConsumptionDialog :com.tqhit.battery.one.features.stats.apppower.presentation   AppPowerConsumptionDialogFactory :com.tqhit.battery.one.features.stats.apppower.presentation  
AppViewHolder Ucom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionAdapter  	Companion Tcom.tqhit.battery.one.features.stats.apppower.presentation.AppPowerConsumptionDialog  AppUsageStatsRepository 8com.tqhit.battery.one.features.stats.apppower.repository  	Companion Pcom.tqhit.battery.one.features.stats.apppower.repository.AppUsageStatsRepository  PrefsStatsChargeCache 1com.tqhit.battery.one.features.stats.charge.cache  StatsChargeCache 1com.tqhit.battery.one.features.stats.charge.cache  	Companion Gcom.tqhit.battery.one.features.stats.charge.cache.PrefsStatsChargeCache  StatsChargeSession 0com.tqhit.battery.one.features.stats.charge.data  	Companion Ccom.tqhit.battery.one.features.stats.charge.data.StatsChargeSession  StatsChargeDIModule .com.tqhit.battery.one.features.stats.charge.di  $CalculateSimpleChargeEstimateUseCase 2com.tqhit.battery.one.features.stats.charge.domain  	Companion Wcom.tqhit.battery.one.features.stats.charge.domain.CalculateSimpleChargeEstimateUseCase  StatsChargeFragment 8com.tqhit.battery.one.features.stats.charge.presentation  StatsChargeViewModel 8com.tqhit.battery.one.features.stats.charge.presentation  	Companion Lcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeFragment  	Companion Mcom.tqhit.battery.one.features.stats.charge.presentation.StatsChargeViewModel  DefaultStatsChargeRepository 6com.tqhit.battery.one.features.stats.charge.repository  StatsChargeRepository 6com.tqhit.battery.one.features.stats.charge.repository  	Companion Scom.tqhit.battery.one.features.stats.charge.repository.DefaultStatsChargeRepository  CoreBatteryDIModule 3com.tqhit.battery.one.features.stats.corebattery.di  CoreBatteryStatsProvider 7com.tqhit.battery.one.features.stats.corebattery.domain  DefaultCoreBatteryStatsProvider 7com.tqhit.battery.one.features.stats.corebattery.domain  	Companion Wcom.tqhit.battery.one.features.stats.corebattery.domain.DefaultCoreBatteryStatsProvider  CoreBatteryServiceHelper 8com.tqhit.battery.one.features.stats.corebattery.service  CoreBatteryStatsService 8com.tqhit.battery.one.features.stats.corebattery.service  	Companion Qcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper  	Companion Pcom.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryStatsService  CurrentSessionCache 4com.tqhit.battery.one.features.stats.discharge.cache  DischargeRatesCache 4com.tqhit.battery.one.features.stats.discharge.cache  PrefsCurrentSessionCache 4com.tqhit.battery.one.features.stats.discharge.cache  PrefsDischargeRatesCache 4com.tqhit.battery.one.features.stats.discharge.cache  	Companion Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsCurrentSessionCache  	Companion Mcom.tqhit.battery.one.features.stats.discharge.cache.PrefsDischargeRatesCache  DischargeSessionData 3com.tqhit.battery.one.features.stats.discharge.data  ScreenStateReceiver 9com.tqhit.battery.one.features.stats.discharge.datasource  	Companion Mcom.tqhit.battery.one.features.stats.discharge.datasource.ScreenStateReceiver  StatsDischargeModule 1com.tqhit.battery.one.features.stats.discharge.di  StatsDischargeProvidersModule 1com.tqhit.battery.one.features.stats.discharge.di  AppLifecycleManager 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  DischargeRateCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  FullSessionReEstimator 5com.tqhit.battery.one.features.stats.discharge.domain  GapEstimationCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenStateTimeTracker 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  ScreenTimeValidationService 5com.tqhit.battery.one.features.stats.discharge.domain  SessionManager 5com.tqhit.battery.one.features.stats.discharge.domain  SessionMetricsCalculator 5com.tqhit.battery.one.features.stats.discharge.domain  
TimeConverter 5com.tqhit.battery.one.features.stats.discharge.domain  	Companion Icom.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager  	Companion Icom.tqhit.battery.one.features.stats.discharge.domain.DischargeCalculator  	Companion Lcom.tqhit.battery.one.features.stats.discharge.domain.FullSessionReEstimator  	Companion Mcom.tqhit.battery.one.features.stats.discharge.domain.GapEstimationCalculator  	Companion Lcom.tqhit.battery.one.features.stats.discharge.domain.ScreenStateTimeTracker  	Companion Qcom.tqhit.battery.one.features.stats.discharge.domain.ScreenTimeValidationService  AnimationHelper ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeFragment ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeUiUpdater ;com.tqhit.battery.one.features.stats.discharge.presentation  DischargeViewModel ;com.tqhit.battery.one.features.stats.discharge.presentation  InfoButtonManager ;com.tqhit.battery.one.features.stats.discharge.presentation  	Companion Kcom.tqhit.battery.one.features.stats.discharge.presentation.AnimationHelper  	Companion Mcom.tqhit.battery.one.features.stats.discharge.presentation.DischargeFragment  	Companion Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeUiUpdater  	Companion Ncom.tqhit.battery.one.features.stats.discharge.presentation.DischargeViewModel  	Companion Mcom.tqhit.battery.one.features.stats.discharge.presentation.InfoButtonManager  BatteryRepository 9com.tqhit.battery.one.features.stats.discharge.repository  DischargeSessionRepository 9com.tqhit.battery.one.features.stats.discharge.repository  	Companion Kcom.tqhit.battery.one.features.stats.discharge.repository.BatteryRepository  	Companion Tcom.tqhit.battery.one.features.stats.discharge.repository.DischargeSessionRepository  EnhancedDischargeTimerService 6com.tqhit.battery.one.features.stats.discharge.service  #EnhancedDischargeTimerServiceHelper 6com.tqhit.battery.one.features.stats.discharge.service  	Companion Tcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerService  	Companion Zcom.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper  DefaultHealthCache 1com.tqhit.battery.one.features.stats.health.cache  HealthCache 1com.tqhit.battery.one.features.stats.health.cache  	Companion Dcom.tqhit.battery.one.features.stats.health.cache.DefaultHealthCache  HealthDIModule .com.tqhit.battery.one.features.stats.health.di  CalculateBatteryHealthUseCase 2com.tqhit.battery.one.features.stats.health.domain  GetHealthHistoryUseCase 2com.tqhit.battery.one.features.stats.health.domain  	Companion Pcom.tqhit.battery.one.features.stats.health.domain.CalculateBatteryHealthUseCase  	Companion Jcom.tqhit.battery.one.features.stats.health.domain.GetHealthHistoryUseCase  HealthViewModel 8com.tqhit.battery.one.features.stats.health.presentation  	Companion Hcom.tqhit.battery.one.features.stats.health.presentation.HealthViewModel  DefaultHealthRepository 6com.tqhit.battery.one.features.stats.health.repository  HealthRepository 6com.tqhit.battery.one.features.stats.health.repository  HistoryBatteryRepository 6com.tqhit.battery.one.features.stats.health.repository  	Companion Ncom.tqhit.battery.one.features.stats.health.repository.DefaultHealthRepository  	Companion Ocom.tqhit.battery.one.features.stats.health.repository.HistoryBatteryRepository  !UnifiedBatteryNotificationService 2com.tqhit.battery.one.features.stats.notifications  'UnifiedBatteryNotificationServiceHelper 2com.tqhit.battery.one.features.stats.notifications  	Companion Tcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationService  	Companion Zcom.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper  ChargeFragment #com.tqhit.battery.one.fragment.main  DischargeFragment #com.tqhit.battery.one.fragment.main  HealthFragment #com.tqhit.battery.one.fragment.main  SettingsFragment #com.tqhit.battery.one.fragment.main  	Companion 5com.tqhit.battery.one.fragment.main.DischargeFragment  AnimationGridFragment -com.tqhit.battery.one.fragment.main.animation  AnimationCategory 2com.tqhit.battery.one.fragment.main.animation.data  
AnimationItem 2com.tqhit.battery.one.fragment.main.animation.data  
ChargeSession $com.tqhit.battery.one.manager.charge  ChargingSessionManager $com.tqhit.battery.one.manager.charge  	Companion 2com.tqhit.battery.one.manager.charge.ChargeSession  	Companion ;com.tqhit.battery.one.manager.charge.ChargingSessionManager  DischargeSession 'com.tqhit.battery.one.manager.discharge  DischargeSessionManager 'com.tqhit.battery.one.manager.discharge  	Companion 8com.tqhit.battery.one.manager.discharge.DischargeSession  	Companion ?com.tqhit.battery.one.manager.discharge.DischargeSessionManager  BatteryHistoryManager #com.tqhit.battery.one.manager.graph  HistoryManager #com.tqhit.battery.one.manager.graph  TemperatureHistoryManager #com.tqhit.battery.one.manager.graph  ThemeManager #com.tqhit.battery.one.manager.theme  AnimationRepository  com.tqhit.battery.one.repository  
AppRepository  com.tqhit.battery.one.repository  BatteryRepository  com.tqhit.battery.one.repository  	Companion 4com.tqhit.battery.one.repository.AnimationRepository  	Companion .com.tqhit.battery.one.repository.AppRepository  	Companion 2com.tqhit.battery.one.repository.BatteryRepository  BatteryMonitorService com.tqhit.battery.one.service  ChargingOverlayService com.tqhit.battery.one.service  VibrationService com.tqhit.battery.one.service  	Companion 3com.tqhit.battery.one.service.BatteryMonitorService  	Companion 4com.tqhit.battery.one.service.ChargingOverlayService  	Companion .com.tqhit.battery.one.service.VibrationService  AppViewModel com.tqhit.battery.one.viewmodel  AnimationViewModel )com.tqhit.battery.one.viewmodel.animation  BatteryViewModel 'com.tqhit.battery.one.viewmodel.battery  Lazy kotlin  Pair kotlin  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  ItemDecoration )androidx.recyclerview.widget.RecyclerView  PagerAdapter androidx.viewpager.widget  
DebugActivity $com.tqhit.battery.one.activity.debug  StartingViewAdapter 'com.tqhit.battery.one.activity.starting  GridSpacingItemDecoration 9com.tqhit.battery.one.features.emoji.presentation.gallery  AnimationAdapter 5com.tqhit.battery.one.fragment.main.animation.adapter  CategoryAdapter 5com.tqhit.battery.one.fragment.main.animation.adapter  GridSpacingItemDecoration 5com.tqhit.battery.one.fragment.main.animation.adapter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         