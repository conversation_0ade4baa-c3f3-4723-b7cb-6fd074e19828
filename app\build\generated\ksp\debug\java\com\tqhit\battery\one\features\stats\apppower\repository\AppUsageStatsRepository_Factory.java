package com.tqhit.battery.one.features.stats.apppower.repository;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AppUsageStatsRepository_Factory implements Factory<AppUsageStatsRepository> {
  private final Provider<Context> contextProvider;

  public AppUsageStatsRepository_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AppUsageStatsRepository get() {
    return newInstance(contextProvider.get());
  }

  public static AppUsageStatsRepository_Factory create(Provider<Context> contextProvider) {
    return new AppUsageStatsRepository_Factory(contextProvider);
  }

  public static AppUsageStatsRepository newInstance(Context context) {
    return new AppUsageStatsRepository(context);
  }
}
