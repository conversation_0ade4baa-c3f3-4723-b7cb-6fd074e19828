package com.tqhit.battery.one.viewmodel.animation;

import android.content.Context;
import com.tqhit.battery.one.repository.AnimationRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationViewModel_Factory implements Factory<AnimationViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<AnimationRepository> repositoryProvider;

  public AnimationViewModel_Factory(Provider<Context> contextProvider,
      Provider<AnimationRepository> repositoryProvider) {
    this.contextProvider = contextProvider;
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public AnimationViewModel get() {
    return newInstance(contextProvider.get(), repositoryProvider.get());
  }

  public static AnimationViewModel_Factory create(Provider<Context> contextProvider,
      Provider<AnimationRepository> repositoryProvider) {
    return new AnimationViewModel_Factory(contextProvider, repositoryProvider);
  }

  public static AnimationViewModel newInstance(Context context, AnimationRepository repository) {
    return new AnimationViewModel(context, repository);
  }
}
