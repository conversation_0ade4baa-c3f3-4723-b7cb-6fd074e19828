package com.tqhit.battery.one.features.stats.charge.domain;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class CalculateSimpleChargeEstimateUseCase_Factory implements Factory<CalculateSimpleChargeEstimateUseCase> {
  @Override
  public CalculateSimpleChargeEstimateUseCase get() {
    return newInstance();
  }

  public static CalculateSimpleChargeEstimateUseCase_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static CalculateSimpleChargeEstimateUseCase newInstance() {
    return new CalculateSimpleChargeEstimateUseCase();
  }

  private static final class InstanceHolder {
    static final CalculateSimpleChargeEstimateUseCase_Factory INSTANCE = new CalculateSimpleChargeEstimateUseCase_Factory();
  }
}
