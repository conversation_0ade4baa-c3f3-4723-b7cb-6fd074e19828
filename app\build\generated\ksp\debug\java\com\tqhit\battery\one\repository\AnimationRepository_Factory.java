package com.tqhit.battery.one.repository;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class AnimationRepository_Factory implements Factory<AnimationRepository> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public AnimationRepository_Factory(Provider<PreferencesHelper> preferencesHelperProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  @Override
  public AnimationRepository get() {
    return newInstance(preferencesHelperProvider.get());
  }

  public static AnimationRepository_Factory create(
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new AnimationRepository_Factory(preferencesHelperProvider);
  }

  public static AnimationRepository newInstance(PreferencesHelper preferencesHelper) {
    return new AnimationRepository(preferencesHelper);
  }
}
